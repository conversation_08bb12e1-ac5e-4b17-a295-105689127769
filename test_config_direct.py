#!/usr/bin/env python3
"""
Direct test for IntelliCrawler configuration system
Tests configuration functionality by importing modules directly
"""

import os
import sys
import tempfile
import json
from pathlib import Path

# Add the IntelliCrawler module to the path
sys.path.insert(0, str(Path(__file__).parent))

# Import config module directly to avoid AI dependencies
sys.path.insert(0, str(Path(__file__).parent / "intellicrawler"))

def test_environment_variable_validation():
    """Test environment variable validation directly"""
    print("=" * 60)
    print("🧪 TESTING ENVIRONMENT VARIABLE VALIDATION")
    print("=" * 60)
    
    original_env = dict(os.environ)
    
    try:
        # Import config module directly
        from utils.config import validate_environment_variables
        
        # Test scenario 1: Valid configuration
        print(f"\n🧪 Testing scenario: Valid configuration")
        print("-" * 40)
        
        # Clear and set test environment variables
        env_keys_to_clear = ['DEEPSEEK_API_KEY', 'MAX_PAGES_DEFAULT', 'CRAWL_DEPTH_DEFAULT', 'DELAY_DEFAULT']
        for key in env_keys_to_clear:
            os.environ.pop(key, None)
        
        test_env_vars = {
            'DEEPSEEK_API_KEY': 'sk-1234567890abcdef1234567890abcdef',
            'MAX_PAGES_DEFAULT': '100',
            'CRAWL_DEPTH_DEFAULT': '3',
            'DELAY_DEFAULT': '1.0'
        }
        
        for key, value in test_env_vars.items():
            os.environ[key] = value
            print(f"  📝 Set {key}={value}")
        
        # Run validation
        validation = validate_environment_variables()
        
        print(f"  📊 Result: {'✅ VALID' if validation['valid'] else '❌ INVALID'}")
        print(f"  ❌ Errors: {len(validation['errors'])}")
        print(f"  ⚠️  Warnings: {len(validation['warnings'])}")
        print(f"  ℹ️  Info: {len(validation['info'])}")
        
        # Print details
        for error in validation['errors']:
            print(f"    ❌ {error['variable']}: {error['message']}")
        for warning in validation['warnings']:
            print(f"    ⚠️  {warning['variable']}: {warning['message']}")
        for info in validation['info']:
            print(f"    ✅ {info['variable']}: {info['message']}")
                
        # Test scenario 2: Invalid values
        print(f"\n🧪 Testing scenario: Invalid numeric values")
        print("-" * 40)
        
        os.environ['MAX_PAGES_DEFAULT'] = 'not_a_number'
        os.environ['CRAWL_DEPTH_DEFAULT'] = '-1'
        
        validation2 = validate_environment_variables()
        print(f"  📊 Result: {'✅ VALID' if validation2['valid'] else '❌ INVALID'}")
        print(f"  ❌ Errors: {len(validation2['errors'])}")
        print(f"  ⚠️  Warnings: {len(validation2['warnings'])}")
        
        for error in validation2['errors']:
            print(f"    ❌ {error['variable']}: {error['message']}")
                
        return True
        
    except Exception as e:
        print(f"❌ Environment variable validation test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)

def test_env_loading():
    """Test loading environment variables directly"""
    print("\n" + "=" * 60)
    print("🔧 TESTING ENVIRONMENT VARIABLE LOADING")
    print("=" * 60)
    
    original_env = dict(os.environ)
    
    try:
        # Set some test environment variables
        test_env_vars = {
            'DEEPSEEK_API_KEY': 'sk-test123456789abcdef',
            'MAX_PAGES_DEFAULT': '150',
            'CRAWL_DEPTH_DEFAULT': '5',
            'DELAY_DEFAULT': '2.0',
            'RESPECT_ROBOTS': 'false',
            'ENABLE_JAVASCRIPT': 'true',
            'DEBUG_MODE': 'true'
        }
        
        print("📝 Setting test environment variables:")
        for key, value in test_env_vars.items():
            os.environ[key] = value
            print(f"  {key}={value}")
        
        from utils.config import load_env_variables
        
        env_vars = load_env_variables()
        
        print(f"\n✅ Environment variables loaded")
        print(f"📋 Loaded keys: {list(k for k in env_vars.keys() if not k.startswith('_'))}")
        
        # Verify some key values
        expected_checks = [
            ('deepseek_api_key', 'sk-test123456789abcdef'),
            ('max_pages_default', 150),
            ('crawl_depth_default', 5),
            ('delay_default', 2.0),
            ('respect_robots', False),
            ('enable_javascript', True),
            ('debug_mode', True)
        ]
        
        print(f"\n🔍 Verifying loaded values:")
        for key, expected_value in expected_checks:
            actual_value = env_vars.get(key)
            if actual_value == expected_value:
                print(f"  ✅ {key}: {actual_value}")
            else:
                print(f"  ❌ {key}: {actual_value} (expected {expected_value})")
        
        return True
        
    except Exception as e:
        print(f"❌ Environment loading test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)

def test_config_operations():
    """Test basic configuration operations"""
    print("\n" + "=" * 60)
    print("📁 TESTING CONFIG FILE OPERATIONS")
    print("=" * 60)
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # Import config module directly
            from utils import config as config_module
            
            # Mock the config directory
            original_config_dir = config_module.CONFIG_DIR
            original_config_file = config_module.CONFIG_FILE
            original_env_file = config_module.ENV_FILE
            
            config_module.CONFIG_DIR = temp_dir
            config_module.CONFIG_FILE = os.path.join(temp_dir, "config.json")
            config_module.ENV_FILE = os.path.join(temp_dir, ".env")
            
            print(f"🏗️  Using temporary config directory: {temp_dir}")
            
            # Test default config creation
            print("🔨 Testing default config creation...")
            default_config = config_module.create_default_config()
            print(f"✅ Default config created with {len(default_config)} keys")
            print(f"🔍 Sample keys: {list(default_config.keys())[:5]}...")
            
            # Test config modification and saving
            print("💾 Testing config modification and saving...")
            test_config = default_config.copy()
            test_config['test_key'] = 'test_value'
            test_config['deepseek_api_key'] = 'test_api_key_12345'
            
            save_result = config_module.save_config(test_config)
            print(f"💾 Save result: {'✅ Success' if save_result else '❌ Failed'}")
            
            # Verify file was created
            config_file_exists = os.path.exists(config_module.CONFIG_FILE)
            print(f"📁 Config file exists: {'✅ Yes' if config_file_exists else '❌ No'}")
            
            if config_file_exists:
                with open(config_module.CONFIG_FILE, 'r') as f:
                    saved_data = json.load(f)
                print(f"📄 Saved data has {len(saved_data)} keys")
                print(f"🔍 Test key in saved data: {'✅ Yes' if 'test_key' in saved_data else '❌ No'}")
                
                # Check specific values
                if saved_data.get('test_key') == 'test_value':
                    print(f"✅ Test key value correct: {saved_data['test_key']}")
                else:
                    print(f"❌ Test key value incorrect: {saved_data.get('test_key')}")
                    
                if saved_data.get('deepseek_api_key') == 'test_api_key_12345':
                    print(f"✅ API key value correct")
                else:
                    print(f"❌ API key value incorrect: {saved_data.get('deepseek_api_key')}")
            
            # Test config loading
            print("📥 Testing config loading...")
            loaded_config = config_module.load_config()
            print(f"📥 Loaded config with {len(loaded_config)} keys")
            print(f"🔍 Test key preserved: {'✅ Yes' if loaded_config.get('test_key') == 'test_value' else '❌ No'}")
            print(f"🔍 API key preserved: {'✅ Yes' if loaded_config.get('deepseek_api_key') == 'test_api_key_12345' else '❌ No'}")
            
            return True
            
        except Exception as e:
            print(f"❌ Config operations test failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
        finally:
            try:
                # Restore original paths
                config_module.CONFIG_DIR = original_config_dir
                config_module.CONFIG_FILE = original_config_file
                config_module.ENV_FILE = original_env_file
            except:
                pass

def test_dotenv_integration():
    """Test .env file integration with python-dotenv"""
    print("\n" + "=" * 60)
    print("📄 TESTING .ENV FILE INTEGRATION")
    print("=" * 60)
    
    # Create temporary .env file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
        f.write("""# Test .env file for IntelliCrawler
DEEPSEEK_API_KEY=sk-testenv123456789
MAX_PAGES_DEFAULT=75
CRAWL_DEPTH_DEFAULT=2
DELAY_DEFAULT=1.5
RESPECT_ROBOTS=true
DEBUG_MODE=false
USER_AGENT=TestCrawler/1.0
""")
        temp_env_file = f.name
    
    try:
        print(f"📝 Created test .env file: {temp_env_file}")
        
        # Import and mock the config module
        from utils import config as config_module
        
        original_root_env = config_module.ROOT_ENV_FILE
        config_module.ROOT_ENV_FILE = temp_env_file
        
        print("🔄 Loading environment variables from .env file...")
        env_vars = config_module.load_env_variables()
        
        print(f"✅ .env file loaded successfully")
        print(f"📋 Loaded environment variables:")
        
        expected_values = {
            'deepseek_api_key': 'sk-testenv123456789',
            'max_pages_default': 75,
            'crawl_depth_default': 2,
            'delay_default': 1.5,
            'respect_robots': True,
            'debug_mode': False,
            'user_agent': 'TestCrawler/1.0'
        }
        
        for key, expected in expected_values.items():
            actual = env_vars.get(key)
            if actual == expected:
                print(f"  ✅ {key}: {actual}")
            else:
                print(f"  ❌ {key}: {actual} (expected {expected})")
        
        # Check validation results
        validation = env_vars.get('_validation_results')
        if validation:
            print(f"\n📊 Validation results:")
            print(f"  Valid: {'✅ Yes' if validation.get('valid') else '❌ No'}")
            print(f"  Errors: {len(validation.get('errors', []))}")
            print(f"  Warnings: {len(validation.get('warnings', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ .env file test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        try:
            os.unlink(temp_env_file)
            config_module.ROOT_ENV_FILE = original_root_env
        except:
            pass

def main():
    """Run all configuration tests"""
    print("🚀 INTELLICRAWLER CONFIGURATION SYSTEM TESTS (DIRECT)")
    print("=" * 80)
    
    tests = [
        ("Environment Variable Validation", test_environment_variable_validation),
        ("Environment Variable Loading", test_env_loading),
        ("Config File Operations", test_config_operations),
        (".env File Integration", test_dotenv_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running: {test_name}")
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: FAILED with exception: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 80)
    print(f"📊 TEST RESULTS")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success rate: {(passed / (passed + failed) * 100):.1f}%")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Configuration system is working correctly.")
        print("\n📋 CONFIGURATION SUMMARY:")
        print("✅ Environment variable validation working")
        print("✅ .env file loading working")
        print("✅ Configuration file operations working")
        print("✅ Error handling and validation working")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())