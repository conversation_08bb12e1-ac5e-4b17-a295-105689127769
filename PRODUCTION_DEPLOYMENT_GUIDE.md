
# IntelliCrawler Production Deployment Guide

**Version:** 2.0.0  
**Date:** August 30, 2025  
**Target Audience:** System Administrators, DevOps Teams, Production Engineers  

---

## Table of Contents

1. [Executive Summary](#executive-summary)
2. [Production Environment Prerequisites](#production-environment-prerequisites)
3. [Production Installation Process](#production-installation-process)
4. [Production Configuration](#production-configuration)
5. [Deployment Methods](#deployment-methods)
6. [Security Hardening](#security-hardening)
7. [Monitoring and Maintenance](#monitoring-and-maintenance)
8. [Scaling and Load Management](#scaling-and-load-management)
9. [Troubleshooting and Recovery](#troubleshooting-and-recovery)
10. [Appendices](#appendices)

---

## Executive Summary

IntelliCrawler is an AI-powered web scraping and analysis platform designed for production environments requiring intelligent, scalable, and reliable data extraction capabilities. This guide provides comprehensive instructions for deploying IntelliCrawler in production environments with enterprise-grade security, monitoring, and scalability considerations.

### Key Production Considerations
- **AI Integration**: Requires secure API key management for DeepSeek AI services
- **Web Scraping Compliance**: Implements ethical scraping practices with rate limiting and respect for robots.txt
- **Resource Management**: Memory and CPU intensive operations requiring proper resource allocation
- **Network Security**: Extensive network access requirements for web scraping operations
- **Data Handling**: Secure storage and export of potentially sensitive scraped data

### Deployment Options
- **Standalone Server**: Direct installation on Linux servers
- **Containerized**: Docker-based deployment with orchestration support
- **Service Mode**: Background service operation without GUI
- **Multi-Instance**: Distributed deployment for high-throughput scenarios

---

## Production Environment Prerequisites

### Hardware Requirements

#### Minimum Requirements
- **CPU**: 4 cores (2.0 GHz minimum)
- **RAM**: 8 GB minimum
- **Storage**: 50 GB available space (20 GB for application, 30 GB for data/logs)
- **Network**: Stable internet connection with adequate bandwidth for web scraping operations

#### Recommended Production Specifications
- **CPU**: 8+ cores (3.0 GHz+) for optimal parallel processing
- **RAM**: 16-32 GB for handling multiple concurrent scraping sessions
- **Storage**: 100+ GB SSD storage for performance and data retention
- **Network**: High-bandwidth connection (100+ Mbps) with low latency

#### High-Volume/Enterprise Specifications  
- **CPU**: 16+ cores with high clock speeds
- **RAM**: 64+ GB for large-scale operations
- **Storage**: 500+ GB NVMe SSD with backup storage
- **Network**: Enterprise-grade connection with redundancy

### Operating System Support

#### Primary Support (Recommended)
- **Ubuntu 20.04 LTS or 22.04 LTS** (Tested and validated)
- **Red Hat Enterprise Linux 8/9**
- **CentOS Stream 8/9**
- **Amazon Linux 2**

#### Secondary Support
- **Debian 11/12**
- **SUSE Linux Enterprise Server 15**
- **Ubuntu 18.04 LTS** (Legacy support)

#### System Requirements
- **Python**: 3.9+ (3.11+ recommended for production)
- **Kernel**: Linux kernel 4.15+ (5.4+ recommended)
- **Architecture**: x86_64 (ARM64 experimental support available)

### Network and Firewall Configuration

#### Outbound Network Requirements
```bash
# HTTP/HTTPS traffic for web scraping (essential)
Port 80/TCP (HTTP)
Port 443/TCP (HTTPS)

# DeepSeek AI API access (required for AI features)
api.deepseek.com:443/TCP

# Package repositories and updates
# Ubuntu/Debian
archive.ubuntu.com:80,443/TCP
security.ubuntu.com:80,443/TCP
# RHEL/CentOS
cdn.redhat.com:443/TCP
# Python Package Index
pypi.org:443/TCP
files.pythonhosted.org:443/TCP

# Browser driver downloads (Selenium)
chromedriver.storage.googleapis.com:443/TCP
github.com:443/TCP (for WebDriver manager)

# Optional: Proxy services (if using proxy rotation)
# Various proxy provider endpoints - configure as needed
```

#### Inbound Network Requirements (Optional)
```bash
# Only required if running web interface accessible externally
Port 8080/TCP (Default web interface - configurable)
Port 22/TCP (SSH access for management)
```

#### Firewall Configuration Example (UFW)
```bash
# Essential outbound rules
ufw allow out 80,443/tcp
ufw allow out 53/udp  # DNS

# SSH access (adjust to your management network)
ufw allow from 10.0.0.0/8 to any port 22

# Optional: Web interface access
ufw allow from 10.0.0.0/8 to any port 8080

# Enable firewall
ufw --force enable
```

### Security Prerequisites

#### File System Permissions
- Dedicated service account with minimal privileges
- Separate directories for application, data, logs, and configuration
- Proper file ownership and permissions (750/640)

#### Network Security
- Network segmentation for scraping operations
- Proxy authentication and rotation capabilities
- SSL/TLS certificate management for secure API communications

#### Compliance Considerations
- Data protection regulation compliance (GDPR, CCPA, etc.)
- Web scraping legal compliance frameworks
- Audit logging and data retention policies
- Privacy impact assessment completion

---

## Production Installation Process

### Pre-Installation Setup

#### 1. System Preparation
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y  # Ubuntu/Debian
sudo yum update -y                      # RHEL/CentOS

# Install essential system dependencies
sudo apt install -y python3 python3-pip python3-venv git wget curl unzip \
    build-essential libssl-dev libffi-dev python3-dev \
    libxml2-dev libxslt1-dev zlib1g-dev \
    libjpeg-dev libpng-dev \
    xvfb x11-utils  # For headless browser operation

# Install Chrome/Chromium for Selenium (Ubuntu/Debian)
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
sudo apt update
sudo apt install -y google-chrome-stable

# Alternative: Install Chromium
sudo apt install -y chromium-browser
```

#### 2. Service Account Creation
```bash
# Create dedicated service account
sudo useradd -r -s /bin/false -m -d /opt/intellicrawler intellicrawler

# Create directory structure
sudo mkdir -p /opt/intellicrawler/{app,data,logs,config,backups}
sudo mkdir -p /var/log/intellicrawler
sudo mkdir -p /etc/intellicrawler

# Set ownership
sudo chown -R intellicrawler:intellicrawler /opt/intellicrawler
sudo chown -R intellicrawler:intellicrawler /var/log/intellicrawler
sudo chown -R intellicrawler:intellicrawler /etc/intellicrawler

# Set permissions
sudo chmod 750 /opt/intellicrawler
sudo chmod 755 /opt/intellicrawler/{data,logs,backups}
sudo chmod 700 /opt/intellicrawler/config
sudo chmod 700 /etc/intellicrawler
```

#### 3. Python Environment Setup
```bash
# Switch to intellicrawler user
sudo -u intellicrawler bash

# Create Python virtual environment
cd /opt/intellicrawler
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Upgrade pip and install wheel
pip install --upgrade pip wheel setuptools
```

### Application Installation

#### 1. Source Code Deployment
```bash
# Option A: Git clone (development/staging)
git clone https://github.com/yourusername/intellicrawler.git app
cd app
git checkout production  # Use production branch/tag

# Option B: Release package (production)
# Download and extract release package
wget https://github.com/yourusername/intellicrawler/archive/v2.0.0.tar.gz
tar -xzf v2.0.0.tar.gz
mv intellicrawler-2.0.0 app

cd app
```

#### 2. Dependency Installation
```bash
# Install production dependencies
pip install -r requirements.txt

# Install additional production packages
pip install gunicorn supervisor

# Install package in development mode for flexibility
pip install -e .

# Verify installation
python -c "import intellicrawler; print(f'IntelliCrawler {intellicrawler.__version__} installed successfully')"
```

#### 3. Configuration File Setup
```bash
# Create production configuration directory
mkdir -p /etc/intellicrawler

# Copy configuration templates
cp .env.template /etc/intellicrawler/.env.production
cp config/production.example.json /etc/intellicrawler/config.json

# Set secure permissions
chmod 640 /etc/intellicrawler/.env.production
chmod 640 /etc/intellicrawler/config.json
chown intellicrawler:intellicrawler /etc/intellicrawler/*
```

### Post-Installation Verification

#### 1. Import Test
```bash
# Test basic imports
sudo -u intellicrawler bash -c "
cd /opt/intellicrawler
source venv/bin/activate
python3 -c 'import intellicrawler; print(\"✅ IntelliCrawler imports successfully\")'
"
```

#### 2. Configuration Validation
```bash
# Run configuration validation
sudo -u intellicrawler bash -c "
cd /opt/intellicrawler
source venv/bin/activate
python3 -m intellicrawler.utils.config --validate
"
```

#### 3. Browser Verification
```bash
# Test Chrome/Selenium integration
sudo -u intellicrawler bash -c "
cd /opt/intellicrawler
source venv/bin/activate
python3 -c '
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
options = Options()
options.add_argument(\"--headless\")
options.add_argument(\"--no-sandbox\")
options.add_argument(\"--disable-dev-shm-usage\")
driver = webdriver.Chrome(options=options)
driver.get(\"https://httpbin.org/get\")
print(\"✅ Chrome/Selenium working correctly\")
driver.quit()
'
"
```

---

## Production Configuration

### Environment Variables Security

#### 1. Production .env File Template
```bash
# /etc/intellicrawler/.env.production
# Production environment configuration for IntelliCrawler

# ============================================================================
# CRITICAL SECURITY SETTINGS
# ============================================================================

# DeepSeek API Key (REQUIRED - obtain from https://platform.deepseek.com)
DEEPSEEK_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Production environment identifier
ENVIRONMENT=production
DEBUG_MODE=false
VERBOSE_LOGGING=false
LOG_LEVEL=INFO

# ============================================================================
# BROWSER AND RENDERING CONFIGURATION
# ============================================================================

# Chrome executable path (production path)
CHROME_PATH=/usr/bin/google-chrome-stable

# Headless operation (required for server environments)
CHROME_HEADLESS=true
CHROME_NO_SANDBOX=true
CHROME_DISABLE_DEV_SHM=true

# ============================================================================
# PERFORMANCE AND SCALING SETTINGS
# ============================================================================

# Conservative defaults for production stability
MAX_PAGES_DEFAULT=50
CRAWL_DEPTH_DEFAULT=3
DELAY_DEFAULT=2.0
REQUEST_TIMEOUT=60
MAX_RETRIES=3

# Resource limits
MAX_CONCURRENT_SESSIONS=5
MAX_MEMORY_USAGE=4GB
MAX_CPU_PERCENT=80

# ============================================================================
# NETWORK AND SECURITY SETTINGS
# ============================================================================

# User agent for production
USER_AGENT=IntelliCrawler-Production/2.0 (Enterprise Web Analysis)

# Network security
RESPECT_ROBOTS=true
ENABLE_PROXY_ROTATION=true
PROXY_TIMEOUT=30

# Rate limiting (respectful crawling)
MIN_DELAY_BETWEEN_REQUESTS=1.5
MAX_REQUESTS_PER_MINUTE=30
MAX_REQUESTS_PER_HOUR=1000

# ============================================================================
# DATA MANAGEMENT AND PERSISTENCE
# ============================================================================

# Data directories
DATA_DIR=/opt/intellicrawler/data
LOG_DIR=/var/log/intellicrawler
BACKUP_DIR=/opt/intellicrawler/backups
EXPORT_DIR=/opt/intellicrawler/data/exports

# Data retention policies
MAX_LOG_AGE_DAYS=30
MAX_DATA_AGE_DAYS=90
AUTO_CLEANUP_ENABLED=true

# Export settings
DEFAULT_EXPORT_FORMAT=json
ENABLE_DATA_ENCRYPTION=true
COMPRESS_EXPORTS=true

# ============================================================================
# MONITORING AND ALERTING
# ============================================================================

# Health check settings
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=300
HEALTH_CHECK_PORT=8085

# Performance monitoring
ENABLE_METRICS_COLLECTION=true
METRICS_RETENTION_DAYS=7

# Error reporting
ENABLE_ERROR_REPORTING=true
ERROR_NOTIFICATION_EMAIL=<EMAIL>

# ============================================================================
# COMPLIANCE AND AUDIT SETTINGS
# ============================================================================

# Audit logging
ENABLE_AUDIT_LOGGING=true
AUDIT_LOG_LEVEL=INFO
AUDIT_SENSITIVE_DATA=false

# Legal compliance
GDPR_COMPLIANCE=true
DATA_ANONYMIZATION=true
CONSENT_TRACKING=true
```

#### 2. API Key Security Best Practices
```bash
# Create secure API key management
sudo mkdir -p /etc/intellicrawler/secrets
sudo chmod 700 /etc/intellicrawler/secrets

# Store API key in separate file
echo "sk-your-actual-deepseek-api-key" | sudo tee /etc/intellicrawler/secrets/deepseek_api_key
sudo chmod 600 /etc/intellicrawler/secrets/deepseek_api_key
sudo chown intellicrawler:intellicrawler /etc/intellicrawler/secrets/deepseek_api_key

# Reference in environment file
echo "DEEPSEEK_API_KEY_FILE=/etc/intellicrawler/secrets/deepseek_api_key" >> /etc/intellicrawler/.env.production
```

#### 3. Configuration Validation Script
```bash
#!/bin/bash
# /opt/intellicrawler/bin/validate-config.sh
# Production configuration validation script

set -e

echo "🔍 Validating IntelliCrawler production configuration..."

# Check environment file exists and is readable
if [[ ! -r /etc/intellicrawler/.env.production ]]; then
    echo "❌ Environment file not found or not readable"
    exit 1
fi

# Check API key is configured
if ! grep -q "^DEEPSEEK_API_KEY=" /etc/intellicrawler/.env.production; then
    echo "❌ DEEPSEEK_API_KEY not configured"
    exit 1
fi

# Check Chrome is available
if ! command -v google-chrome-stable &> /dev/null; then
    echo "❌ Chrome not installed"
    exit 1
fi

# Check Python environment
cd /opt/intellicrawler
source venv/bin/activate

# Validate Python dependencies
python3 -c "import intellicrawler" || {
    echo "❌ IntelliCrawler package not properly installed"
    exit 1
}

# Check data directories exist and are writable
for dir in "/opt/intellicrawler/data" "/var/log/intellicrawler" "/opt/intellicrawler/backups"; do
    if [[ ! -w "$dir" ]]; then
        echo "❌ Directory not writable: $dir"
        exit 1
    fi
done

echo "✅ Configuration validation passed"
```

### Production-Grade Logging Configuration

#### 1. Structured Logging Setup
```python
# /etc/intellicrawler/logging_config.json
{
    "version": 1,
    "disable_existing_loggers": false,
    "formatters": {
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        },
        "json": {
            "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
            "format": "%(asctime)s %(name)s %(levelname)s %(funcName)s %(lineno)d %(message)s"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "WARNING",
            "formatter": "detailed",
            "stream": "ext://sys.stdout"
        },
        "file_detailed": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "INFO",
            "formatter": "detailed",
            "filename": "/var/log/intellicrawler/intellicrawler.log",
            "maxBytes": 52428800,
            "backupCount": 5
        },
        "file_json": {
            "class": "logging.handlers.RotatingFileHandler", 
            "level": "INFO",
            "formatter": "json",
            "filename": "/var/log/intellicrawler/intellicrawler.json",
            "maxBytes": 52428800,
            "backupCount": 5
        },
        "error_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "ERROR", 
            "formatter": "detailed",
            "filename": "/var/log/intellicrawler/errors.log",
            "maxBytes": 10485760,
            "backupCount": 10
        }
    },
    "loggers": {
        "intellicrawler": {
            "level": "INFO",
            "handlers": ["console", "file_detailed", "file_json", "error_file"],
            "propagate": false
        },
        "selenium": {
            "level": "WARNING",
            "handlers": ["file_detailed"],
            "propagate": false
        },
        "requests": {
            "level": "WARNING",
            "handlers": ["file_detailed"],
            "propagate": false
        }
    },
    "root": {
        "level": "INFO",
        "handlers": ["console", "file_detailed"]
    }
}
```

#### 2. Log Rotation and Cleanup
```bash
# /etc/logrotate.d/intellicrawler
/var/log/intellicrawler/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 640 intellicrawler intellicrawler
    postrotate
        systemctl reload intellicrawler || true
    endscript
}
```

### Performance Optimization Settings

#### 1. System-Level Optimization
```bash
# /etc/sysctl.d/99-intellicrawler.conf
# Network optimization for web scraping
net.core.somaxconn=65535
net.ipv4.tcp_max_syn_backlog=65535
net.ipv4.tcp_keepalive_time=600
net.ipv4.tcp_keepalive_intvl=60
net.ipv4.tcp_keepalive_probes=9

# Memory management
vm.max_map_count=262144
vm.swappiness=10

# File handle limits
fs.file-max=1000000
```

#### 2. Application Resource Limits
```bash
# /etc/security/limits.d/intellicrawler.conf
intellicrawler soft nofile 65535
intellicrawler hard nofile 65535
intellicrawler soft nproc 32768
intellicrawler hard nproc 32768
intellicrawler soft memlock unlimited
intellicrawler hard memlock unlimited
```

---

## Deployment Methods

### Standalone Server Deployment

#### 1. SystemD Service Configuration
```ini
# /etc/systemd/system/intellicrawler.service
[Unit]
Description=IntelliCrawler AI-Powered Web Scraping Service
After=network.target
Wants=network-online.target

[Service]
Type=forking
User=intellicrawler
Group=intellicrawler
WorkingDirectory=/opt/intellicrawler
Environment=PATH=/opt/intellicrawler/venv/bin:/usr/local/bin:/usr/bin:/bin
EnvironmentFile=/etc/intellicrawler/.env.production
ExecStartPre=/opt/intellicrawler/bin/validate-config.sh
ExecStart=/opt/intellicrawler/venv/bin/python -m intellicrawler.main --daemon
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/var/run/intellicrawler/intellicrawler.pid
TimeoutStopSec=30
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/opt/intellicrawler/data /var/log/intellicrawler /opt/intellicrawler/backups
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

# Resource limits
LimitNOFILE=65535
LimitNPROC=32768
MemoryMax=8G
CPUQuota=400%

[Install]
WantedBy=multi-user.target
```

#### 2. Service Management Commands
```bash
# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable intellicrawler
sudo systemctl start intellicrawler

# Check service status
sudo systemctl status intellicrawler

# View logs
sudo journalctl -u intellicrawler -f

# Service management
sudo systemctl stop intellicrawler
sudo systemctl restart intellicrawler
sudo systemctl reload intellicrawler
```

#### 3. Health Check Script
```bash
#!/bin/bash
# /opt/intellicrawler/bin/health-check.sh
# Production health check script

set -e

HEALTH_CHECK_URL="http://localhost:8085/health"
TIMEOUT=10

echo "🏥 Running IntelliCrawler health check..."

# Check if service is running
if ! systemctl is-active --quiet intellicrawler; then
    echo "❌ Service is not running"
    exit 1
fi

# Check if API endpoint responds
if command -v curl >/dev/null 2>&1; then
    if ! curl -f -s --max-time $TIMEOUT "$HEALTH_CHECK_URL" >/dev/null; then
        echo "❌ Health check endpoint not responding"
        exit 1
    fi
else
    echo "⚠️  curl not available, skipping endpoint check"
fi

# Check log for recent errors
if tail -n 100 /var/log/intellicrawler/errors.log | grep -q "$(date +%Y-%m-%d)"; then
    echo "⚠️  Recent errors found in log"
    exit 1
fi

# Check disk space
DISK_USAGE=$(df /opt/intellicrawler | awk 'NR==2 {print $5}' | sed 's/%//')
if [[ $DISK_USAGE -gt 80 ]]; then
    echo "⚠️  Disk usage above 80%: ${DISK_USAGE}%"
    exit 1
fi

echo "✅ Health check passed"
```

### Containerized Deployment with Docker

#### 1. Production Dockerfile
```dockerfile
# Production Dockerfile
FROM ubuntu:22.04

LABEL maintainer="IntelliCrawler Team"
LABEL version="2.0.0"
LABEL description="IntelliCrawler AI-Powered Web Scraping Platform"

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PATH="/opt/intellicrawler/venv/bin:$PATH"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 python3-pip python3-venv python3-dev \
    build-essential \
    wget curl unzip \
    xvfb x11-utils \
    libxml2-dev libxslt1-dev \
    libjpeg-dev libpng-dev \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Create application user
RUN useradd -r -s /bin/false -m -d /opt/intellicrawler intellicrawler

# Set up directory structure
RUN mkdir -p /opt/intellicrawler/{app,data,logs,config,backups} \
    && chown -R intellicrawler:intellicrawler /opt/intellicrawler

# Switch to application user
USER intellicrawler
WORKDIR /opt/intellicrawler

# Create Python virtual environment
RUN python3 -m venv venv

# Copy requirements and install Python dependencies
COPY --chown=intellicrawler:intellicrawler requirements.txt ./
RUN venv/bin/pip install --upgrade pip wheel setuptools \
    && venv/bin/pip install -r requirements.txt

# Copy application code
COPY --chown=intellicrawler:intellicrawler . ./app/

# Install application
RUN cd app && ../venv/bin/pip install -e .

# Create startup script
RUN echo '#!/bin/bash\n\
set -e\n\
cd /opt/intellicrawler\n\
source venv/bin/activate\n\
exec python -m intellicrawler.main --daemon --headless\n\
' > /opt/intellicrawler/start.sh \
    && chmod +x /opt/intellicrawler/start.sh

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8085/health || exit 1

# Expose port
EXPOSE 8085

# Volume mounts
VOLUME ["/opt/intellicrawler/data", "/opt/intellicrawler/logs", "/opt/intellicrawler/config"]

# Start application
CMD ["/opt/intellicrawler/start.sh"]
```

#### 2. Docker Compose Configuration
```yaml
# docker-compose.production.yml
version: '3.8'

services:
  intellicrawler:
    build:
      context: .
      dockerfile: Dockerfile.production
    image: intellicrawler:2.0.0-production
    container_name: intellicrawler-prod
    restart: unless-stopped
    
    environment:
      - ENVIRONMENT=production
      - CHROME_HEADLESS=true
      - CHROME_NO_SANDBOX=true
      - CHROME_DISABLE_DEV_SHM=true
    
    env_file:
      - .env.production
    
    volumes:
      - intellicrawler_data:/opt/intellicrawler/data
      - intellicrawler_logs:/opt/intellicrawler/logs
      - intellicrawler_config:/opt/intellicrawler/config
      - intellicrawler_backups:/opt/intellicrawler/backups
    
    ports:
      - "8085:8085"  # Health check endpoint
    
    networks:
      - intellicrawler_network
    
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

  # Optional: Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: intellicrawler-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    
    volumes:
      - redis_data:/data
    
    networks:
      - intellicrawler_network
    
    deploy:
      resources:
        limits:
          memory: 512M

  # Optional: Database for audit logging
  postgres:
    image: postgres:15-alpine
    container_name: intellicrawler-postgres
    restart: unless-stopped
    
    environment:
      - POSTGRES_DB=intellicrawler
      - POSTGRES_USER=intellicrawler
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    
    volumes:
      - postgres_data:/var/lib/postgresql/data
    
    networks:
      - intellicrawler_network
    
    deploy:
      resources:
        limits:
          memory: 1G

volumes:
  intellicrawler_data:
  intellicrawler_logs:
  intellicrawler_config:
  intellicrawler_backups:
  redis_data:
  postgres_data:

networks:
  intellicrawler_network:
    driver: bridge
```

#### 3. Container Orchestration with Docker Swarm
```bash
# Initialize Docker Swarm
docker swarm init

# Create secrets for sensitive configuration
echo "sk-your-deepseek-api-key" | docker secret create deepseek_api_key -
echo "your-redis-password" | docker secret create redis_password -
echo "your-postgres-password" | docker secret create postgres_password -

# Deploy stack
docker stack deploy -c docker-compose.production.yml intellicrawler

# Monitor services
docker service ls
docker service logs intellicrawler_intellicrawler

# Scale services
docker service scale intellicrawler_intellicrawler=3

# Update service
docker service update --image intellicrawler:2.0.1-production intellicrawler_intellicrawler
```

### Kubernetes Deployment

#### 1. Kubernetes Manifests
```yaml
# k8s-namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: intellicrawler
  labels:
    name: intellicrawler

---
# k8s-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: intellicrawler-config
  namespace: intellicrawler
data:
  ENVIRONMENT: "production"
  CHROME_HEADLESS: "true"
  CHROME_NO_SANDBOX: "true"
  CHROME_DISABLE_DEV_SHM: "true"
  MAX_PAGES_DEFAULT: "50"
  CRAWL_DEPTH_DEFAULT: "3"
  DELAY_DEFAULT: "2.0"

---
# k8s-secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: intellicrawler-secrets
  namespace: intellicrawler
type: Opaque
stringData:
  DEEPSEEK_API_KEY: "sk-your-deepseek-api-key"

---
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: intellicrawler
  namespace: intellicrawler
  labels:
    app: intellicrawler
spec:
  replicas: 2
  selector:
    matchLabels:
      app: intellicrawler
  template:
    metadata:
      labels:
        app: intellicrawler
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      
      containers:
      - name: intellicrawler
        image: intellicrawler:2.0.0-production
        imagePullPolicy: Always
        
        envFrom:
        - configMapRef:
            name: intellicrawler-config
        - secretRef:
            name: intellicrawler-secrets
        
        ports:
        - containerPort: 8085
          name: health-check
        
        resources:
          limits:
            cpu: 2000m
            memory: 4Gi
          requests:
            cpu: 1000m
            memory: 2Gi
        
        livenessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        
        readinessProbe:
          httpGet:
            path: /ready
            port: 8085
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        
        volumeMounts:
        - name: data-storage
          mountPath: /opt/intellicrawler/data
        - name: log-storage
          mountPath: /opt/intellicrawler/logs
      
      volumes:
      - name: data-storage
        persistentVolumeClaim:
          claimName: intellicrawler-data-pvc
      - name: log-storage
        persistentVolumeClaim:
          claimName: intellicrawler-logs-pvc

---
# k8s-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: intellicrawler-service
  namespace: intellicrawler
spec:
  selector:
    app: intellicrawler
  ports:
  - name: health-check
    port: 8085
    targetPort: 8085
  type: ClusterIP

---
# k8s-pvc.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: intellicrawler-data-pvc
  namespace: intellicrawler
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: intellicrawler-logs-pvc
  namespace: intellicrawler
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 
      storage: 50Gi

---
# k8s-ingress.yaml (optional)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: intellicrawler-ingress
  namespace: intellicrawler
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - intellicrawler.yourcompany.com
    secretName: intellicrawler-tls
  rules:
  - host: intellicrawler.yourcompany.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: intellicrawler-service
            port:
              number: 8085
```

#### 2. Kubernetes Deployment Commands
```bash
# Apply all manifests
kubectl apply -f k8s-namespace.yaml
kubectl apply -f k8s-configmap.yaml
kubectl apply -f k8s-secret.yaml
kubectl apply -f k8s-pvc.yaml
kubectl apply -f k8s-deployment.yaml
kubectl apply -f k8s-service.yaml
kubectl apply -f k8s-ingress.yaml

# Monitor deployment
kubectl get pods -n intellicrawler -w
kubectl logs -f deployment/intellicrawler -n intellicrawler

# Scale deployment
kubectl scale deployment intellicrawler --replicas=5 -n intellicrawler

# Update deployment
kubectl set image deployment/intellicrawler intellicrawler=intellicrawler:2.0.1-production -n intellicrawler

# Health check
kubectl port-forward svc/intellicrawler-service 8085:8085 -n intellicrawler
curl http://localhost:8085/health
```

### Load Balancer Configuration

#### 1. HAProxy Configuration
```haproxy
# /etc/haproxy/haproxy.cfg
global
    daemon
    chroot /var/lib/haproxy
    user haproxy
    group haproxy
    
    # SSL configuration
    ssl-default-bind-options no-sslv3 no-tls-tickets
    ssl-default-bind-ciphers ECDHE+aes128gcm:ECDHE+aes256gcm:ECDHE+aes128sha256:ECDHE+aes256sha384:ECDHE+aes128sha:ECDHE+aes256sha:AES128+aes128gcm:AES256+aes256gcm:AES128+aes128sha256:AES256+aes256sha384:AES128+aes128sha:AES256+aes256sha:!aNULL:!eNULL:!EXPORT:!DES:!3DES:!MD5:!PSK

defaults
    mode http
    timeout connect 5s
    timeout client 30s
    timeout server 30s
    option httplog
    option dontlognull
    retries 3

frontend intellicrawler_frontend
    bind *:80
    bind *:443 ssl crt /etc/ssl/private/intellicrawler.pem
    redirect scheme https if !{ ssl_fc }
    
    default_backend intellicrawler_backend

backend intellicrawler_backend
    balance roundrobin
    option httpchk GET /health
    
    server crawler1 *********:8085 check
    server crawler2 *********:8085 check
    server crawler3 *********:8085 check
```

#### 2. Nginx Load Balancer
```nginx
# /etc/nginx/sites-available/intellicrawler
upstream intellicrawler_backend {
    least_conn;
    server *********:8085 weight=1 max_fails=3 fail_timeout=30s;
    server *********:8085 weight=1 max_fails=3 fail_timeout=30s;
    server *********:8085 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name intellicrawler.yourcompany.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name intellicrawler.yourcompany.com;
    
    ssl_certificate /etc/ssl/certs/intellicrawler.crt;
    ssl_certificate_key /etc/ssl/private/intellicrawler.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE+aes128gcm:ECDHE+aes256gcm:ECDHE+aes128sha256:ECDHE+aes256sha384;
    ssl_prefer_server_ciphers off;
    
    location / {
        proxy_pass http://intellicrawler_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Health check
        location /health {
            access_log off;
            proxy_pass http://intellicrawler_backend;
        }
    }
}
```

---

## Security Hardening

### Network Security

#### 1. Firewall Configuration (Advanced)
```bash
#!/bin/bash
# /opt/intellicrawler/security/setup-firewall.sh
# Advanced firewall configuration for IntelliCrawler

set -e

echo "🔒 Setting up advanced firewall rules for IntelliCrawler..."

# Reset iptables
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X
iptables -t mangle -F
iptables -t mangle -X

# Default policies
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# Allow loopback
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# Allow established connections
iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT

# SSH access (restrict to management network)
iptables -A INPUT -p tcp -s 10.0.0.0/8 --dport 22 -m conntrack --ctstate NEW -j ACCEPT

# Health check endpoint (internal only)
iptables -A INPUT -p tcp -s 10.0.0.0/8 --dport 8085 -m conntrack --ctstate NEW -j ACCEPT

# Rate limiting for HTTP(S) - prevent abuse
iptables -A INPUT -p tcp --dport 80 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT

# Log dropped packets (for monitoring)
iptables -A INPUT -m limit --limit 5/min -j LOG --log-prefix "iptables denied: " --log-level 7

# Save rules
iptables-save > /etc/iptables/rules.v4

echo "✅ Firewall configuration completed"
```

#### 2. Network Segmentation
```bash
# /etc/intellicrawler/network-security.conf
# Network segmentation configuration

# Management network (SSH, monitoring)
MGMT_NETWORK="********/24"

# Application network (IntelliCrawler services)
APP_NETWORK="********/24"

# DMZ network (load balancers, reverse proxies)
DMZ_NETWORK="********/24"

# External proxy network (for web scraping)
PROXY_NETWORK="********/24"

# Block internal network access from crawling operations
iptables -A OUTPUT -s $APP_NETWORK -d 10.0.0.0/8 -j DROP
iptables -A OUTPUT -s $APP_NETWORK -d **********/12 -j DROP
iptables -A OUTPUT -s $APP_NETWORK -d ***********/16 -j DROP
```

### Application Security

#### 1. API Key Rotation Script
```bash
#!/bin/bash
# /opt/intellicrawler/security/rotate-api-keys.sh
# Automated API key rotation script

set -e

BACKUP_DIR="/opt/intellicrawler/backups/api-keys"
CURRENT_DATE=$(date +%Y%m%d_%H%M%S)

echo "🔄 Starting API key rotation process..."

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Backup current configuration
cp /etc/intellicrawler/.env.production "$BACKUP_DIR/env_backup_$CURRENT_DATE"

# Generate new API key placeholder (manual step required)
echo "Manual step required:"
echo "1. Generate new DeepSeek API key at https://platform.deepseek.com/api_keys"
echo "2. Update DEEPSEEK_API_KEY in /etc/intellicrawler/.env.production"
echo "3. Run: systemctl restart intellicrawler"

# Test new configuration (after manual update)
read -p "Press Enter after updating the API key..."

# Validate new configuration
if /opt/intellicrawler/bin/validate-config.sh; then
    echo "✅ New API key validated successfully"
    
    # Archive old backup after 30 days
    find "$BACKUP_DIR" -name "env_backup_*" -mtime +30 -delete
    
    echo "✅ API key rotation completed"
else
    echo "❌ New API key validation failed"
    echo "Restoring previous configuration..."
    cp "$BACKUP_DIR/env_backup_$CURRENT_DATE" /etc/intellicrawler/.env.production
    systemctl restart intellicrawler
    exit 1
fi
```

#### 2. Security Monitoring Script
```bash
#!/bin/bash
# /opt/intellicrawler/security/security-monitor.sh
# Real-time security monitoring

set -e

LOG_FILE="/var/log/intellicrawler/security.log"
ALERT_EMAIL="<EMAIL>"

log_security_event() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - SECURITY - $1" | tee -a "$LOG_FILE"
}

# Monitor failed login attempts
check_failed_logins() {
    local failed_logins=$(grep "Failed password" /var/log/auth.log | grep "$(date '+%b %d')" | wc -l)
    if [[ $failed_logins -gt 10 ]]; then
        log_security_event "HIGH: $failed_logins failed login attempts detected today"
        echo "Security Alert: $failed_logins failed login attempts on $(hostname)" | mail -s "Security Alert" "$ALERT_EMAIL"
    fi
}

# Monitor unusual network activity
check_network_activity() {
    local connections=$(netstat -an | grep :8085 | grep ESTABLISHED | wc -l)
    if [[ $connections -gt 50 ]]; then
        log_security_event "WARNING: High number of connections ($connections) to IntelliCrawler"
    fi
}

# Monitor disk space usage
check_disk_space() {
    local disk_usage=$(df /opt/intellicrawler | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $disk_usage -gt 90 ]]; then
        log_security_event "CRITICAL: Disk space usage at $disk_usage%"
        echo "Critical Alert: Disk space usage at $disk_usage% on $(hostname)" | mail -s "Disk Space Critical" "$ALERT_EMAIL"
    fi
}

# Monitor log file integrity
check_log_integrity() {
    local recent_errors=$(grep "ERROR" /var/log/intellicrawler/errors.log | grep "$(date '+%Y-%m-%d')" | wc -l)
    if [[ $recent_errors -gt 100 ]]; then
        log_security_event "HIGH: $recent_errors errors logged today - possible attack or system issue"
    fi
}

# Run all checks
check_failed_logins
check_network_activity
check_disk_space
check_log_integrity

log_security_event "Security monitoring check completed"
```

---

## Monitoring and Maintenance

### Comprehensive Monitoring Setup

#### 1. Prometheus Configuration
```yaml
# prometheus/intellicrawler-scrape.yml
scrape_configs:
  - job_name: 'intellicrawler'
    static_configs:
      - targets: ['localhost:8085']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s
    
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'intellicrawler-prod'

  - job_name: 'intellicrawler-health'
    static_configs:
      - targets: ['localhost:8085']
    metrics_path: '/health'
    scrape_interval: 10s
    scrape_timeout: 5s
```

#### 2. Automated Maintenance Script
```bash
#!/bin/bash
# /opt/intellicrawler/maintenance/daily-maintenance.sh
# Comprehensive daily maintenance script

set -e

MAINTENANCE_LOG="/var/log/intellicrawler/maintenance.log"
BACKUP_DIR="/opt/intellicrawler/backups"
CURRENT_DATE=$(date +%Y%m%d_%H%M%S)

log_maintenance() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - MAINTENANCE - $1" | tee -a "$MAINTENANCE_LOG"
}

# Backup configuration files
backup_configuration() {
    log_maintenance "Starting configuration backup..."
    
    local backup_path="$BACKUP_DIR/config_$CURRENT_DATE"
    mkdir -p "$backup_path"
    
    cp /etc/intellicrawler/.env.production "$backup_path/"
    cp /etc/intellicrawler/config.json "$backup_path/" 2>/dev/null || true
    cp /etc/systemd/system/intellicrawler.service "$backup_path/" 2>/dev/null || true
    
    tar -czf "$backup_path.tar.gz" -C "$BACKUP_DIR" "config_$CURRENT_DATE"
    rm -rf "$backup_path"
    
    log_maintenance "Configuration backup completed: $backup_path.tar.gz"
}

# Clean up old log files
cleanup_logs() {
    log_maintenance "Starting log cleanup..."
    
    find /var/log/intellicrawler -name "*.log*" -mtime +30 -delete
    find /var/log/intellicrawler -name "*.json*" -mtime +30 -delete
    find /var/log/intellicrawler -name "*.log" -mtime +7 -exec gzip {} \;
    
    log_maintenance "Log cleanup completed"
}

# Main maintenance routine
main() {
    log_maintenance "Starting daily maintenance routine"
    
    backup_configuration
    cleanup_logs
    
    # Check system resources
    local disk_usage=$(df /opt/intellicrawler | awk 'NR==2 {print $5}' | sed 's/%//')
    log_maintenance "Disk usage: $disk_usage%"
    
    # Check service status
    if systemctl is-active --quiet intellicrawler; then
        log_maintenance "IntelliCrawler service is running"
    else
        log_maintenance "WARNING: IntelliCrawler service is not running"
        systemctl start intellicrawler
    fi
    
    log_maintenance "Daily maintenance routine completed successfully"
}

# Execute maintenance
main
```

---

## Scaling and Load Management

### Performance Optimization

#### 1. Resource Scaling Recommendations

**CPU Scaling Guidelines:**
- **Light Usage (1-10 concurrent crawls)**: 4 cores minimum
- **Medium Usage (10-50 concurrent crawls)**: 8-16 cores
- **Heavy Usage (50+ concurrent crawls)**: 16+ cores with high clock speeds

**Memory Scaling Guidelines:**
- **Base allocation**: 4GB minimum
- **Per concurrent crawl**: 200-500MB additional
- **AI processing**: 2-4GB additional for complex analysis
- **Browser instances**: 100-200MB per Chrome instance

**Storage Scaling Guidelines:**
- **Application**: 10GB base installation
- **Logs**: 1-5GB per month (with rotation)
- **Data exports**: 10GB-1TB depending on crawl volume
- **Backups**: 2x data size for redundancy

#### 2. Multi-Instance Architecture
```python
# /opt/intellicrawler/scaling/cluster_manager.py
# Cluster management for distributed IntelliCrawler deployment

import asyncio
import aiohttp
import json
from typing import List, Dict, Any
import logging

class IntelliCrawlerCluster:
    def __init__(self, instances: List[str]):
        self.instances = instances
        self.instance_stats = {}
        self.logger = logging.getLogger(__name__)
    
    async def get_instance_load(self, instance: str) -> Dict[str, Any]:
        """Get current load metrics from an instance"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"http://{instance}:8085/metrics") as response:
                    if response.status == 200:
                        metrics = await response.json()
                        return {
                            'instance': instance,
                            'cpu_usage': metrics.get('cpu_percent', 0),
                            'memory_usage': metrics.get('memory_percent', 0),
                            'active_crawls': metrics.get('active_crawls', 0),
                            'available': True
                        }
        except Exception as e:
            self.logger.error(f"Failed to get load from {instance}: {e}")
        
        return {
            'instance': instance,
            'available': False
        }
    
    async def select_best_instance(self) -> str:
        """Select the least loaded available instance"""
        tasks = [self.get_instance_load(instance) for instance in self.instances]
        results = await asyncio.gather(*tasks)
        
        # Filter available instances
        available_instances = [r for r in results if r['available']]
        
        if not available_instances:
            raise Exception("No available instances")
        
        # Select instance with lowest combined load score
        best_instance = min(
            available_instances, 
            key=lambda x: x['cpu_usage'] + x['memory_usage'] + (x['active_crawls'] * 10)
        )
        
        return best_instance['instance']
    
    async def distribute_crawl_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """Distribute a crawl job to the best available instance"""
        try:
            instance = await self.select_best_instance()
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"http://{instance}:8085/crawl",
                    json=job,
                    timeout=aiohttp.ClientTimeout(total=300)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            'status': 'success',
                            'instance': instance,
                            'result': result
                        }
                    else:
                        error = await response.text()
                        return {
                            'status': 'error',
                            'instance': instance,
                            'error': error
                        }
        
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
```

#### 3. Auto-Scaling Configuration (Kubernetes)
```yaml
# k8s-hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: intellicrawler-hpa
  namespace: intellicrawler
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: intellicrawler
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
```

---

## Troubleshooting and Recovery

### Common Production Issues

#### 1. Memory Issues
**Symptoms:**
- Application becomes slow or unresponsive
- Browser instances fail to start
- Out of memory errors in logs

**Diagnosis:**
```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head -20

# Check IntelliCrawler memory usage
systemctl status intellicrawler
journalctl -u intellicrawler | grep -i "memory\|oom"

# Check for memory leaks
top -p $(pgrep -f intellicrawler)
```

**Solutions:**
```bash
# Immediate: Restart service
systemctl restart intellicrawler

# Permanent: Increase memory limits
# Edit /etc/systemd/system/intellicrawler.service
MemoryMax=16G

# Optimize configuration
# Reduce concurrent crawls in .env.production
MAX_CONCURRENT_SESSIONS=3
MAX_PAGES_DEFAULT=25
```

#### 2. Browser/Selenium Issues
**Symptoms:**
- Chrome crashes or fails to start
- Selenium WebDriver errors
- Dynamic content not loading

**Diagnosis:**
```bash
# Test Chrome manually
google-chrome-stable --version
google-chrome-stable --headless --no-sandbox --disable-dev-shm-usage --dump-dom https://httpbin.org/get

# Check Chrome processes
ps aux | grep chrome

# Test WebDriver
python3 -c "
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
options = Options()
options.add_argument('--headless')
options.add_argument('--no-sandbox')
options.add_argument('--disable-dev-shm-usage')
driver = webdriver.Chrome(options=options)
print('WebDriver test successful')
driver.quit()
"
```

**Solutions:**
```bash
# Update Chrome
apt update && apt upgrade google-chrome-stable

# Fix shared memory issues
echo 'tmpfs /dev/shm tmpfs defaults,size=2g 0 0' >> /etc/fstab
mount -o remount /dev/shm

# Alternative: Use Chromium
apt install chromium-browser
# Update CHROME_PATH in configuration
```

#### 3. API Connectivity Issues
**Symptoms:**
- AI features not working
- Authentication errors
- API timeout errors

**Diagnosis:**
```bash
# Test API connectivity
curl -v -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"model":"deepseek-chat","messages":[{"role":"user","content":"test"}]}' \
  https://api.deepseek.com/chat/completions

# Check DNS resolution
nslookup api.deepseek.com

# Test network connectivity
traceroute api.deepseek.com
```

**Solutions:**
```bash
# Verify API key
grep DEEPSEEK_API_KEY /etc/intellicrawler/.env.production

# Check firewall rules
iptables -L OUTPUT -n | grep 443

# Update API configuration
# Regenerate API key if needed
```

### Disaster Recovery

#### 1. Complete System Recovery
```bash
#!/bin/bash
# /opt/intellicrawler/recovery/system-recovery.sh
# Complete system recovery procedure

set -e

BACKUP_DATE=${1:-"latest"}
BACKUP_DIR="/opt/intellicrawler/backups"

echo "🚨 Starting IntelliCrawler disaster recovery..."

# Stop services
systemctl stop intellicrawler
systemctl stop nginx || true
systemctl stop haproxy || true

# Restore configuration
if [[ -f "$BACKUP_DIR/config_$BACKUP_DATE.tar.gz" ]]; then
    cd "$BACKUP_DIR"
    tar -xzf "config_$BACKUP_DATE.tar.gz"
    
    cp "config_$BACKUP_DATE/.env.production" /etc/intellicrawler/
    cp "config_$BACKUP_DATE/config.json" /etc/intellicrawler/ 2>/dev/null || true
    cp "config_$BACKUP_DATE/intellicrawler.service" /etc/systemd/system/ 2>/dev/null || true
    
    echo "✅ Configuration restored"
else
    echo "❌ No configuration backup found for date: $BACKUP_DATE"
    exit 1
fi

# Restore application if needed
if [[ ! -d /opt/intellicrawler/app ]]; then
    echo "📦 Reinstalling application..."
    # Re-run installation procedure
    /opt/intellicrawler/scripts/install.sh
fi

# Restore data if available
if [[ -f "$BACKUP_DIR/data_$BACKUP_DATE.tar.gz" ]]; then
    cd /opt/intellicrawler
    tar -xzf "$BACKUP_DIR/data_$BACKUP_DATE.tar.gz"
    chown -R intellicrawler:intellicrawler /opt/intellicrawler/data
    echo "✅ Data restored"
fi

# Validate configuration
if /opt/intellicrawler/bin/validate-config.sh; then
    echo "✅ Configuration validation passed"
else
    echo "❌ Configuration validation failed"
    exit 1
fi

# Restart services
systemctl daemon-reload
systemctl start intellicrawler
systemctl start nginx || true
systemctl start haproxy || true

# Verify recovery
sleep 10
if systemctl is-active --quiet intellicrawler; then
    echo "✅ IntelliCrawler service restored successfully"
else
    echo "❌ Service recovery failed"
    journalctl -u intellicrawler --since "5 minutes ago"
    exit 1
fi

echo "🎉 Disaster recovery completed successfully"
```

#### 2. Database Recovery (if applicable)
```bash
#!/bin/bash
# /opt/intellicrawler/recovery/database-recovery.sh
# Database recovery procedure

set -e

BACKUP_DATE=${1:-"latest"}
BACKUP_DIR="/opt/intellicrawler/backups/database"
DB_NAME="intellicrawler"
DB_USER="intellicrawler"

echo "🗄️ Starting database recovery..."

# Stop application to prevent database access
systemctl stop intellicrawler

# Drop existing database (WARNING: destructive)
read -p "This will destroy the current database. Continue? (y/N): " confirm
if [[ $confirm != "y" ]]; then
    echo "Recovery cancelled"
    exit 1
fi

# Find latest backup if not specified
if [[ $BACKUP_DATE == "latest" ]]; then
    BACKUP_FILE=$(ls -t "$BACKUP_DIR"/db_backup_*.sql.gz | head -1)
else
    BACKUP_FILE="$BACKUP_DIR/db_backup_$BACKUP_DATE.sql.gz"
fi

if [[ ! -f "$BACKUP_FILE" ]]; then
    echo "❌ Backup file not found: $BACKUP_FILE"
    exit 1
fi

echo "📂 Restoring from: $BACKUP_FILE"

# Drop and recreate database
psql -U postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"
psql -U postgres -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;"

# Restore database
gunzip -c "$BACKUP_FILE" | psql -U "$DB_USER" -d "$DB_NAME"

echo "✅ Database restored successfully"

# Start application
systemctl start intellicrawler

echo "🎉 Database recovery completed"
```

---

## Appendices

### A. Production Checklist

#### Pre-Deployment Checklist
- [ ] Hardware requirements verified
- [ ] Operating system updated and configured
- [ ] Network connectivity and firewall rules tested
- [ ] SSL certificates obtained and installed
- [ ] Service account created with proper permissions
- [ ] Directory structure created with correct ownership
- [ ] Python virtual environment set up
- [ ] Dependencies installed and verified
- [ ] Configuration files created and validated
- [ ] Chrome/Chromium browser installed and tested
- [ ] DeepSeek API key obtained and configured
- [ ] Backup procedures tested
- [ ] Monitoring setup configured
- [ ] Security hardening applied
- [ ] Load balancer configured (if applicable)
- [ ] Auto-scaling configured (if applicable)

#### Post-Deployment Verification
- [ ] Application starts successfully
- [ ] Health check endpoint responds
- [ ] AI features work with valid API key
- [ ] Web crawling functions correctly
- [ ] Data export capabilities tested
- [ ] Log rotation working
- [ ] Backup procedures executed
- [ ] Monitoring alerts configured
- [ ] Performance metrics collected
- [ ] Security scans completed
- [ ] Disaster recovery tested
- [ ] Documentation updated

### B. Configuration Templates

#### Production .env Template
```bash
# /etc/intellicrawler/.env.production.template
# Production environment template - copy and customize

# CRITICAL: Replace with actual values before deployment
DEEPSEEK_API_KEY=sk-your-actual-api-key-here
ENVIRONMENT=production
DEBUG_MODE=false

# Browser configuration
CHROME_PATH=/usr/bin/google-chrome-stable
CHROME_HEADLESS=true
CHROME_NO_SANDBOX=true

# Performance settings  
MAX_PAGES_DEFAULT=50
CRAWL_DEPTH_DEFAULT=3
DELAY_DEFAULT=2.0
MAX_CONCURRENT_SESSIONS=5

# Security settings
RESPECT_ROBOTS=true
ENABLE_PROXY_ROTATION=true
REQUEST_TIMEOUT=60
MAX_RETRIES=3

# Data management
DATA_DIR=/opt/intellicrawler/data
LOG_DIR=/var/log/intellicrawler
BACKUP_DIR=/opt/intellicrawler/backups

# Monitoring
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_PORT=8085
ENABLE_METRICS_COLLECTION=true
```

### C. Command Reference

#### Service Management
```bash
# Start/Stop/Restart
sudo systemctl start intellicrawler
sudo systemctl stop intellicrawler  
sudo systemctl restart intellicrawler
sudo systemctl reload intellicrawler

# Status and logs
sudo systemctl status intellicrawler
sudo journalctl -u intellicrawler -f
sudo journalctl -u intellicrawler --since "1 hour ago"

# Enable/Disable auto-start
sudo systemctl enable intellicrawler
sudo systemctl disable intellicrawler
```

#### Configuration Management
```bash
# Validate configuration
/opt/intellicrawler/bin/validate-config.sh

# Backup configuration
/opt/intellicrawler/maintenance/backup-config.sh

# Rotate API keys
/opt/intellicrawler/security/rotate-api-keys.sh

# Update configuration
sudo nano /etc/intellicrawler/.env.production
sudo systemctl reload intellicrawler
```

#### Monitoring and Maintenance
```bash
# Health check
/opt/intellicrawler/bin/health-check.sh

# Security monitoring
/opt/intellicrawler/security/security-monitor.sh

# Daily maintenance
/opt/intellicrawler/maintenance/daily-maintenance.sh

# View metrics
curl http://localhost:8085/metrics
curl http://localhost:8085/health
```

#### Troubleshooting
```bash
# Check application logs
tail -f /var/log/intellicrawler/intellicrawler.log
tail -f /var/log/intellicrawler/errors.log

# Check system resources
htop
df -h
free -h
netstat -tulpn | grep 8085

# Test components
python3 -c "import intellicrawler; print('Import OK')"
google-chrome-stable --version
curl -s http://localhost:8085/health | jq .
```

### D. Security Hardening Checklist

#### System Level
- [ ] Operating system fully updated
- [ ] Unnecessary services disabled
- [ ] Firewall configured and enabled
- [ ] SSH access restricted to key-based authentication
- [ ] Sudo access limited to required users
- [ ] File permissions properly configured
- [ ] System monitoring enabled
- [ ] Log aggregation configured
- [ ] Intrusion detection system deployed
- [ ] Regular security updates scheduled

#### Application Level  
- [ ] Service runs as non-root user
- [ ] API keys stored securely
- [ ] Configuration files protected
- [ ] Network access restricted
- [ ] Input validation implemented
- [ ] Output sanitization enabled
- [ ] Rate limiting configured
- [ ] Session management secured
- [ ] Error handling doesn't leak information
- [ ] Audit logging enabled

#### Network Level
- [ ] Network segmentation implemented
- [ ] Internal traffic encrypted
- [ ] External access via HTTPS only
- [ ] DDoS protection enabled
- [ ] WAF configured (if applicable)
- [ ] VPN access for management
- [ ] Network monitoring enabled
- [ ] Traffic analysis configured
- [ ] Incident response procedures
- [ ] Regular penetration testing

### E. Support and Resources

#### Getting Help
- **Documentation**: Check `/opt/intellicrawler/docs/` directory
- **Logs**: Review `/var/log/intellicrawler/` for detailed error information
- **Community**: GitHub Issues and Discussions
- **Commercial Support**: Available for enterprise deployments

#### Additional Resources
- **DeepSeek API Documentation**: [https://platform.deepseek.com/docs](https://platform.deepseek.com/docs)
- **Selenium WebDriver Guide**: [https://selenium-python.readthedocs.io/](https://selenium-python.readthedocs.io/)
- **Docker Best Practices**: [https://docs.docker.com/develop/dev-best-practices/](https://docs.docker.com/develop/dev-best-practices/)
- **Kubernetes Security**: [https://kubernetes.io/docs/concepts/security/](https://kubernetes.io/docs/concepts/security/)

#### Version History
- **v2.0.0**: Initial production release with AI integration
- **v2.0.1**: Security improvements and bug fixes (planned)
- **v2.1.0**: Enhanced scaling and monitoring features (planned)

---

## Conclusion

This production deployment guide provides comprehensive instructions for deploying IntelliCrawler in enterprise production environments. By following these guidelines, system administrators and DevOps teams can ensure:

- **Secure Deployment**: With proper security hardening and access controls
- **Scalable Architecture**: Supporting growth from single instance to distributed clusters
- **Reliable Operations**: With comprehensive monitoring, maintenance, and disaster recovery
- **Compliance Ready**: Meeting enterprise security and audit requirements

For successful production deployment:

1. **Start Small**: Begin with a single-instance deployment to validate functionality
2. **Monitor Closely**: Implement comprehensive monitoring from day one
3. **Scale Gradually**: Add instances and features as demand grows
4. **Maintain Regularly**: Follow the maintenance procedures to ensure optimal performance
5. **Stay Updated**: Keep the system and dependencies current for security and features

The IntelliCrawler platform is designed to provide intelligent, ethical, and scalable web scraping capabilities for enterprise environments. With proper deployment and maintenance, it will serve as a reliable foundation for your data extraction and analysis needs.

**Remember**: Always comply with legal
 requirements and terms of service when using web scraping technology.

---

**Document Version:** 1.0  
**Last Updated:** August 30, 2025  
**Next Review:** February 2026  

*This production deployment guide is maintained by the IntelliCrawler team and updated regularly to reflect best practices and new features.*