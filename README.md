# IntelliCrawler - AI-Powered Web Scraping & Analysis Tool

**Version 2.0.0 (AI-Enhanced Edition)**

IntelliCrawler is a powerful, AI-driven web scraping and analysis tool that intelligently adapts to any website structure. Powered by DeepSeek AI, it features advanced anti-scraping detection and evasion, universal content extraction, comprehensive proxy management, and intelligent content analysis capabilities.

## [FAST] Key Features

### 🚀 [NEW] Revolutionary Unified Interface
- **One-Click Intelligence**: Start crawling and analyzing with a single button
- **Real-Time AI Integration**: AI analysis happens automatically as you crawl
- **Live Progress Monitoring**: See exactly what's happening in real-time
- **Smart Data Management**: Automatic saving and export capabilities
- **Adaptive Learning**: The system learns and improves with each crawl
- **Enhanced Performance**: Optimized for speed and reliability

### [AI] Universal Website Handler
- **Intelligent Pagination Detection**: Automatically discovers all pages on any website
- **Advanced Content Structure Analysis**: Adapts to tables, lists, cards, and custom layouts
- **Multi-Layer Encoding Detection**: Handles Base64, URL encoding, HTML entities, Unicode escapes, and more
- **Anti-Scraping Intelligence**: Detects and evades CAPTCHA, rate limiting, bot protection services
- **Success Probability Calculation**: Predicts scraping success rate before starting

### [SEARCH] Enhanced Proxy Scraper
- **Custom Website Testing**: Test any proxy website with full pagination support
- **16 Built-in Sources**: Pre-configured with major proxy providers
- **Parallel Processing**: Multi-threaded scraping for maximum speed
- **Proxy Rotation**: Use found proxies for anti-detection during scraping
- **Smart Navigation**: AI-powered website structure analysis
- **Universal Extraction**: Works with any proxy list format

### [SPIDER] Web Crawler
- **Dynamic Content Support**: JavaScript rendering with Selenium
- **Intelligent Depth Control**: Smart crawling depth management
- **Content Type Detection**: Automatic handling of different content types
- **Error Recovery**: Robust error handling and retry mechanisms

### [CONFIG] Advanced Configuration
- **DeepSeek AI Integration**: Powered by advanced language models
- **Customizable Headers**: User-agent rotation and realistic headers
- **Rate Limiting**: Respectful crawling with configurable delays
- **Export Options**: JSON, TXT, and CSV export formats

## [QUICK] Quick Start

### Prerequisites
```bash
pip install -r requirements.txt
```

### Required Dependencies
- Python 3.8+
- PyQt5
- requests
- beautifulsoup4
- selenium
- chardet
- concurrent.futures

### Optional Dependencies (for enhanced features)
- Chrome/Chromium browser (for JavaScript rendering)
- DeepSeek API key (for AI features)

### Running the Application
```bash
python intellicrawler_launcher.py
```

Or use the main module:
```bash
python -m intellicrawler.main
```

### Installation
1. Clone the repository:
```bash
git clone https://github.com/yourusername/intellicrawler.git
cd intellicrawler
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Run the application:
```bash
python intellicrawler_launcher.py
```

## [TARGET] Core Components

### Universal Website Handler
The heart of IntelliCrawler's intelligence:

```python
from intellicrawler.ui.universal_website_handler import UniversalWebsiteHandler

handler = UniversalWebsiteHandler()
analysis = handler.analyze_website_intelligence(url, "website_name")
pages = handler.discover_all_pages_universal(url, "website_name", max_pages=25)
content = handler.extract_content_universal(html_content, "website_name", page_url)
```

### Proxy Scraper with Custom Testing
Test any website for proxy extraction:

1. Enter custom URL in the "[TEST] Custom Website Testing" section
2. Set maximum pages to crawl
3. Click "[ANALYZE] Test Custom Website"
4. Review AI analysis results
5. Proceed with full crawling if successful

### Smart Website Navigator
Intelligent navigation and content extraction:

```python
from intellicrawler.ui.smart_website_navigator import SmartWebsiteNavigator

navigator = SmartWebsiteNavigator()
pages = navigator.discover_all_pages_intelligent(url, source_name)
content = navigator.extract_content_intelligent(html, source_name, page_url)
```

## [SHIELD] Anti-Scraping Evasion

IntelliCrawler includes comprehensive anti-scraping detection and evasion:

### Detection Methods
- CAPTCHA/reCAPTCHA detection
- JavaScript requirement analysis
- Rate limiting identification
- Bot protection service detection (Cloudflare, Incapsula, etc.)
- Browser fingerprinting analysis
- Behavioral pattern monitoring

### Evasion Techniques
- User-agent rotation (7 realistic agents)
- Header randomization
- Proxy rotation and IP distribution
- Random delay insertion (0.5-3 seconds)
- Session management
- Browser fingerprint spoofing

### Scoring System
```
Protection Level | Methods Detected | Evasion Difficulty | Success Rate
LOW              | 1-2 methods      | Easy              | 90%+
MEDIUM           | 3-4 methods      | Moderate          | 70-80%
HIGH             | 5+ methods       | Hard              | 30-50%
ENTERPRISE       | AI + ML          | Very Hard         | 10-20%
```

## [STATS] Built-in Proxy Sources

IntelliCrawler includes 16 pre-configured proxy sources:

1. **ProxyScrape** - API and web interface
2. **Free Proxy List** - Multiple endpoints
3. **GeoNode** - Free proxy lists
4. **Go2Proxy** - Proxy directory
5. **Spys.one** - Country-specific lists
6. **ProxyNova** - Elite proxy lists
7. **Hide.mn** - Anonymous proxies
8. **Proxifly** - GitHub-hosted lists (11K+ proxies)
9. **OpenProxyList** - Protocol-specific lists
10. **ProxyDB.net** - Database format
11. **Free-Proxy.cz** - European proxies
12. **FreeProxy.World** - Global directory
13. **Oxylabs** - Free tier
14. **IPRoyal** - Free proxy lists
15. **Proxy-List.Download** - API access
16. **Advanced.name** - Advanced filtering

## [CONFIG] Configuration

### API Key Setup
1. Go to Settings tab
2. Enter your DeepSeek API key
3. Save configuration
4. Restart application for full AI features

### Proxy Configuration
- Enable "Use proxies for anti-detection" for enhanced evasion
- Configure max proxies (10-50,000)
- Set maximum pages per source (1-50)

## [FILE] Project Structure

```
IntelliCrawler/
├── intellicrawler/
│   ├── ui/
│   │   ├── main_window.py           # Main application window
│   │   ├── proxy_scraper_tab.py     # Enhanced proxy scraper
│   │   ├── universal_website_handler.py  # Universal AI handler
│   │   ├── smart_website_navigator.py    # Smart navigation
│   │   ├── crawler_tab.py           # Web crawler interface
│   │   ├── analysis_tab.py          # AI analysis interface
│   │   └── settings_tab.py          # Configuration panel
│   ├── utils/
│   │   ├── logger.py                # Logging utilities
│   │   ├── config.py                # Configuration management
│   │   └── error_handler.py         # Error handling
│   ├── ai_integration.py            # DeepSeek AI integration
│   ├── crawler.py                   # Core crawling engine
│   └── main.py                      # Application entry point
├── documentation/                   # All documentation files
├── examples/                        # Usage examples
├── tests/                          # Test suite
├── requirements.txt                 # Dependencies
├── setup.py                        # Package setup
└── README.md                       # This file
```

## [DEBUG] Error Handling

IntelliCrawler includes comprehensive error handling:

- **Network Errors**: Automatic retry with exponential backoff
- **Encoding Issues**: Multi-layer encoding detection and fallback
- **JavaScript Errors**: Selenium fallback for dynamic content
- **Memory Management**: Automatic cleanup of resources
- **Graceful Degradation**: Fallback methods when primary fails

## [SAVE] Export Options

### Proxy Export Formats
- **JSON**: Full proxy data with metadata
- **TXT**: Simple IP:PORT format
- **CSV**: Spreadsheet-compatible format

### Analysis Export
- **Human-readable**: Formatted reports
- **Machine-readable**: JSON structure
- **Custom formats**: Extensible export system

## [GLOBE] Browser Compatibility

### Supported Browsers (for Selenium fallback)
- Chrome/Chromium (recommended)
- Microsoft Edge
- Firefox (limited support)

### User Agents
IntelliCrawler rotates between 7 realistic user agents:
- Windows Chrome (latest versions)
- macOS Chrome and Safari
- Linux Chrome
- Firefox (Windows)
- Edge (Windows)

## [OK] Success Stories

IntelliCrawler has been successfully tested on:
- ✓ 100+ proxy websites
- ✓ E-commerce sites with anti-bot protection
- ✓ News sites with paywalls
- ✓ Social media platforms (respecting ToS)
- ✓ Government data portals
- ✓ Academic databases

## [CLEAR] Best Practices

### Ethical Scraping
1. Respect robots.txt when possible
2. Use reasonable delays between requests
3. Don't overload servers
4. Follow website terms of service
5. Consider rate limiting your requests

### Performance Optimization
1. Use parallel processing for multiple sources
2. Enable proxy rotation for large-scale scraping
3. Configure appropriate timeouts
4. Monitor memory usage
5. Clean up resources properly

### Legal Considerations
- Always check website terms of service
- Respect copyright and intellectual property
- Consider data protection regulations (GDPR, CCPA)
- Use scraped data responsibly
- Implement proper data handling procedures

## [FAST] Performance Metrics

### Parallel Processing
- **8 concurrent sources** for proxy scraping
- **5 concurrent pages** per source
- **ThreadPoolExecutor** for optimal performance
- **Automatic load balancing** across workers

### Memory Efficiency
- **Streaming processing** for large datasets
- **Automatic garbage collection**
- **Resource cleanup** on completion/error
- **Memory usage monitoring**

### Speed Improvements
- **Up to 5x faster** than sequential processing
- **Smart caching** of discovered patterns
- **Optimized network requests**
- **Intelligent retry mechanisms**

## [AI] AI Integration

### DeepSeek AI Features
- **Content structure analysis**
- **Pagination pattern recognition**
- **Anti-scraping method detection**
- **Success probability calculation**
- **Fallback strategy recommendation**

### Machine Learning Components
- **Pattern learning database**
- **Success rate tracking**
- **Automatic strategy adaptation**
- **Continuous improvement**

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## License

IntelliCrawler is released under the MIT License. See LICENSE file for details.

## Troubleshooting

### Common Issues

**1. Import Errors**
```bash
# If you get import errors, ensure the package is installed correctly:
pip install -e .

# Or run directly from the directory:
python intellicrawler_launcher.py
```

**2. PyQt5 Installation Issues**
```bash
# On Ubuntu/Debian:
sudo apt-get install python3-pyqt5

# On macOS:
brew install pyqt5

# On Windows:
pip install PyQt5
```

**3. Chrome/Selenium Issues**
```bash
# Install Chrome browser and ensure it's in PATH
# Or specify Chrome path in settings
```

**4. API Key Configuration**
- Go to Settings tab in the application
- Enter your DeepSeek API key
- Save settings and restart the application

**5. Configuration Directory Issues**
- Configuration is stored in `~/.intellicrawler/`
- Delete this directory to reset all settings
- Restart the application to recreate default config

### Performance Tips

- **Memory Usage**: Close unused tabs to reduce memory consumption
- **Crawling Speed**: Adjust delay settings in crawler tab for faster/slower crawling
- **AI Features**: Ensure stable internet connection for DeepSeek AI integration
- **Proxy Testing**: Start with small page limits when testing new proxy sources

### Getting Help

For additional support:
- Check the `documentation/` folder for detailed guides
- Review error logs in `~/.intellicrawler/logs/`
- Test with the diagnostic tool (Help → Run Diagnostics)

## 🧪 Testing & Quality Assurance

IntelliCrawler includes a comprehensive testing framework ensuring 100% reliability:

### Mega Test Suite
```bash
# Run comprehensive application tests (47 tests, 100% success rate)
python intellicrawler_mega_test.py

# Run unified interface specific tests
python test_unified_interface.py
```

**Test Coverage:**
- ✅ **Application Startup**: Initialization and component loading
- ✅ **Unified Interface**: All 22 UI components and functionality
- ✅ **AI Integration**: DeepSeek API and analysis capabilities
- ✅ **Web Crawling**: Universal handler and content extraction
- ✅ **Data Persistence**: Storage, export, and session management
- ✅ **Error Handling**: Edge cases and recovery mechanisms

**Performance Benchmarks:**
- **Startup Time**: <1 second
- **Test Execution**: <2 minutes for full suite
- **Memory Usage**: <300MB during testing
- **Success Rate**: 100% (47/47 tests passing)

## Support

For support, bug reports, or feature requests:
- Create an issue on GitHub
- Check the documentation folder for detailed guides
- Review the examples directory for usage patterns

---

**IntelliCrawler v2.0.0 AI-Enhanced Edition** - Making web scraping intelligent, efficient, and ethical.

*Last updated: July 2025*