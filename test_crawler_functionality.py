#!/usr/bin/env python3
"""
Test script to verify crawler functionality
"""

import sys
import os

def test_basic_crawl(use_proxies=False):
    """Test basic crawling functionality"""
    proxy_status = "WITH proxies" if use_proxies else "WITHOUT proxies"
    print("=" * 60)
    print(f"Testing IntelliCrawler Basic Crawling ({proxy_status})")
    print("=" * 60)

    try:
        print("\n1. Importing Crawler...")
        from intellicrawler.crawler import Crawler
        print("   ✓ Crawler imported successfully")

        print("\n2. Creating Crawler instance...")
        crawler = Crawler()
        print("   ✓ Crawler instance created")

        print(f"\n3. Testing static crawl ({proxy_status})...")
        print("   Target: http://example.com")
        print("   Max pages: 1")
        print("   Depth: 1")

        # Set up signal handlers to capture results
        results = {'completed': False, 'error': None, 'pages': []}

        def on_page_scraped(page_data):
            print(f"   ✓ Page scraped: {page_data.get('url', 'Unknown')}")
            results['pages'].append(page_data)

        def on_crawl_completed(data):
            print(f"   ✓ Crawl completed with {len(data)} pages")
            results['completed'] = True

        def on_error(error_msg):
            # Only print non-proxy errors or first few proxy errors
            if 'proxy' not in error_msg.lower() or not use_proxies:
                print(f"   ⚠ Error: {error_msg}")
            results['error'] = error_msg

        def on_status(status_msg):
            # Filter out verbose proxy loading messages
            if 'Loading proxies' not in status_msg or not use_proxies:
                print(f"   Status: {status_msg}")

        # Connect signals
        crawler.page_scraped.connect(on_page_scraped)
        crawler.crawl_completed.connect(on_crawl_completed)
        crawler.error_occurred.connect(on_error)
        crawler.status_updated.connect(on_status)

        # Run the crawl
        print("\n   Starting crawl...")
        crawler.crawl(
            start_url="http://example.com",
            max_pages=1,
            depth=1,
            dynamic=False,
            delay=1.0,
            use_proxies=use_proxies
        )
        
        # Check results
        print("\n4. Checking results...")
        if results['error']:
            print(f"   ✗ Crawl failed with error: {results['error']}")
            return False
        elif results['completed']:
            print(f"   ✓ Crawl completed successfully")
            print(f"   ✓ Pages scraped: {len(results['pages'])}")
            if results['pages']:
                page = results['pages'][0]
                print(f"   ✓ Sample page title: {page.get('title', 'N/A')}")
                print(f"   ✓ Content length: {len(page.get('content', ''))} characters")
            return True
        else:
            print("   ⚠ Crawl completed but no completion signal received")
            return False
            
    except Exception as e:
        print(f"\n✗ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_proxy_scraper():
    """Test proxy scraper functionality"""
    print("\n" + "=" * 60)
    print("Testing Proxy Scraper Functionality")
    print("=" * 60)
    
    try:
        print("\n1. Importing ProxyScraper...")
        from intellicrawler.proxy_scraper_simple import ProxyScraper
        print("   ✓ ProxyScraper imported successfully")
        
        print("\n2. Creating ProxyScraper instance...")
        scraper = ProxyScraper(max_proxies=10)
        print("   ✓ ProxyScraper instance created")
        
        print("\n3. Testing proxy scraping (limited to 10 proxies)...")
        
        def progress_callback(percent, message):
            if percent % 20 == 0:  # Only print every 20%
                print(f"   Progress: {percent}% - {message}")
        
        proxies = scraper.scrape_all_sources(progress_callback)
        
        print(f"\n4. Results:")
        print(f"   ✓ Proxies found: {len(proxies)}")
        
        if proxies:
            print(f"   ✓ Sample proxy: {proxies[0]['ip']}:{proxies[0]['port']}")
            return True
        else:
            print("   ⚠ No proxies found (this may be normal if sources are down)")
            return True  # Not a failure, sources might be temporarily unavailable
            
    except Exception as e:
        print(f"\n✗ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_integration():
    """Test AI integration"""
    print("\n" + "=" * 60)
    print("Testing AI Integration")
    print("=" * 60)
    
    try:
        print("\n1. Importing DeepSeekAI...")
        from intellicrawler.ai_integration import DeepSeekAI
        print("   ✓ DeepSeekAI imported successfully")
        
        print("\n2. Creating DeepSeekAI instance...")
        ai = DeepSeekAI()
        print("   ✓ DeepSeekAI instance created")
        
        print("\n3. Checking API key configuration...")
        if ai.is_api_key_set():
            print("   ✓ API key is configured")
            # Don't actually test the API to avoid costs
            print("   ℹ Skipping actual API call to avoid costs")
        else:
            print("   ⚠ API key not configured (expected for testing)")
        
        return True
            
    except Exception as e:
        print(f"\n✗ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("\n" + "=" * 60)
    print("IntelliCrawler Functionality Test Suite")
    print("=" * 60)

    results = {
        'Basic Crawl (No Proxies)': test_basic_crawl(use_proxies=False),
        'Basic Crawl (With Proxies)': test_basic_crawl(use_proxies=True),
        'Proxy Scraper': test_proxy_scraper(),
        'AI Integration': test_ai_integration()
    }
    
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    
    for test_name, passed in results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✓ All tests passed!")
    else:
        print("✗ Some tests failed. See details above.")
    print("=" * 60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())

