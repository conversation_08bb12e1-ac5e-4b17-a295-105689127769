name: SuperCrawler CI

on:
  push:
    branches: [ main, develop, chore/cleanup ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11', '3.12']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y xvfb  # For GUI testing
        
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -U ruff pytest mypy pytest-cov pytest-qt
        pip install -r requirements.txt || echo "No requirements.txt found"
        # Install additional dependencies
        pip install requests beautifulsoup4 openai PyQt5 pandas lxml selenium webdriver-manager nltk pygments tqdm
        
    - name: Run code formatting checks (ruff)
      run: |
        ruff check . --output-format=github
        
    - name: Run type checking (mypy)
      run: |
        mypy intellicrawler --ignore-missing-imports --no-strict-optional
      continue-on-error: true  # Don't fail CI on type errors initially
      
    - name: Run tests with coverage
      run: |
        xvfb-run -a python -m pytest tests/ --cov=intellicrawler --cov-report=xml --cov-report=term-missing
      env:
        QT_QPA_PLATFORM: offscreen
        
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      if: matrix.python-version == '3.11'
      with:
        file: ./coverage.xml
        fail_ci_if_error: false
        
  security:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
        
    - name: Run security scan (bandit)
      run: |
        bandit -r supercrawler/ -f json -o bandit-report.json || true
        
    - name: Check dependencies for known vulnerabilities
      run: |
        safety check --json --output safety-report.json || true
        
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
          
  lint:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        
    - name: Install lint tools
      run: |
        python -m pip install --upgrade pip
        pip install ruff vulture pyflakes
        
    - name: Run linting
      run: |
        echo "=== Ruff Issues ==="
        ruff check . || true
        echo "=== Unused Code ==="
        vulture supercrawler/ || true
        echo "=== Import Issues ==="
        pyflakes supercrawler/ || true
        
  compatibility:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.11']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install requests beautifulsoup4
        
    - name: Test basic imports
      run: |
        python -c "import intellicrawler; print('✅ IntelliCrawler imports successfully')"
        python -c "from intellicrawler.crawler import Crawler; print('✅ Crawler imports successfully')"
        python -c "from intellicrawler.ai_integration import DeepSeekAI; print('✅ AI integration imports successfully')"