# Comprehensive Technical Documentation

**Generated by:** SuperCrawler AI v1.0  
**Date:** 2025-06-17 16:39:35  
**Sources:** 25 crawled pages  
**AI Model:** DeepSeek Reasoner  

---

## Remote Desktop ActiveX Control Documentation  
*Comprehensive guide for developers implementing Microsoft's Remote Desktop ActiveX Control*

---

### **Executive Summary**  
The Remote Desktop ActiveX Control enables embedding RDP functionality into applications/webpages. It provides two variants:  
1. **Scriptable Control** - For web clients/scripting hosts (VBScript, JavaScript)  
2. **Non-Scriptable Control** - For compiled applications (C++, .NET)  

Key capabilities include remote session management, virtual channel integration, and UI customization. Requires Windows Server with IIS 4.0+.

---

### **1. Control Implementation**

#### **Embedding in Webpages**  
```html
<!DOCTYPE HTML>
<html>
<head>
  <title>RDP Web Connection</title>
  <style>
    #rdpControl { width: 1024px; height: 768px; }
  </style>
</head>
<body>
  <object 
    id="rdpControl" 
    classid="CLSID:9059f30f-4eb1-4bd2-9fdc-36f43a218f4a" 
    codebase="msrdp.cab">
    <param name="Server" value="yourserver.domain">
    <param name="DesktopWidth" value="1024">
    <param name="DesktopHeight" value="768">
  </object>
</body>
</html>
```
*Required parameters:*  
- `Server`: Target hostname/IP  
- `DesktopWidth`/`DesktopHeight`: Session resolution  

---

### **2. Virtual Channel Integration**  
*Enable custom data tunneling between client/server*  

**Implementation Steps:**  
1. **Deploy Server Module**  
   - Install VC application on RD Session Host  
   - Ensure `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations` has `DLL` entry  

2. **Client-Side Scripting**  
```javascript
const rdp = document.getElementById("rdpControl");
rdp.VirtualChannelOptions = 1; // Enable virtual channels
rdp.CreateVirtualChannel("MyChannel");
```

---

### **3. Control Reference**  

#### **Key Interfaces**  
| Interface              | Description                          |
|------------------------|--------------------------------------|
| `IMsRdpClient`         | Base configuration/operations        |
| `IMsRdpClientAdvanced` | Advanced settings management         |
| `IMsRdpDrive`          | Drive redirection management         |

#### **Critical Enumerations**  
```cpp
enum ControlCloseStatus {
  controlCanClose,
  controlCannotClose
};

enum ControlReconnectStatus {
  reconnectSucceeded,
  reconnectFailed
};
```

#### **Core Classes**  
- `MsRdpClient` (Redistributable v2)  
- `MsRdpClientNotSafeForScripting` (v2)  
- `MsRdpClient8` (Latest features)  

---

### **4. Sample Implementation**  
*Connection with automatic reconnect*  
```javascript
function connectRDP() {
  const rdp = document.getElementById("rdpControl");
  rdp.Server = "*************";
  rdp.UserName = "admin";
  rdp.AdvancedSettings.EnableAutoReconnect = true;
  rdp.AdvancedSettings.MaxReconnectAttempts = 5;
  rdp.Connect();
}

// Event handlers
rdp.OnConnected = () => console.log("Session established");
rdp.OnDisconnected = (reason) => console.error(`Disconnect: ${reason}`);
```

---

### **5. Troubleshooting Guide**  

| Issue                          | Resolution                                                                 |
|--------------------------------|----------------------------------------------------------------------------|
| Control fails to load          | Verify `msrdp.cab` is deployed and signed                                  |
| Virtual channels unavailable   | Check registry permissions on RD Session Host                              |
| Scripting errors               | Use `MsRdpClientNotSafeForScripting` for compiled apps                     |
| Authentication failures        | Enable `EnableCredSspSupport` property                                     |
| Resolution mismatches          | Set `SmartSizing` property to `true` for dynamic scaling                   |

**Best Practices:**  
- Always specify `DesktopWidth`/`DesktopHeight`  
- Enable `AdvancedSettings.Compress` for low-bandwidth scenarios  
- Set `SecuredSettings.FullScreen` to `false` for embedded UIs  
- Use `MsRdpClient8` for TLS 1.2 support  

---

### **6. Quick Reference**  

#### **Property Cheat Sheet**  
| Property                  | Values        | Description                     |
|---------------------------|---------------|---------------------------------|
| `ColorDepth`              | 8,15,16,24,32| Session color depth             |
| `FullScreen`              | true/false    | Toggle fullscreen mode          |
| `GatewayCredsSource`      | 0-3           | Credential source for RD Gateway|
| `TransportType`           | 0-3           | HTTP/RDP transport protocol     |

#### **Key Methods**  
```javascript
rdp.Connect();                  // Initiate connection
rdp.Disconnect();               // Graceful disconnect
rdp.Reconnect(width, height);   // Session recovery
rdp.Suspend();                  // Pause transmission
```

#### **Event Matrix**  
| Event               | Trigger Condition                     |
|---------------------|---------------------------------------|
| `OnLoginComplete`   | After successful authentication       |
| `OnFatalError`      | Unrecoverable connection failure      |
| `OnChannelReceived` | Virtual channel data received         |

> **Note**: Always test with the included `Default.htm` sample page before custom deployment.

---

**Documentation References**  
- [Main Control Reference](https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control-reference)  
- [Virtual Channels Implementation](https://learn.microsoft.com/en-us/windows/win32/termserv/using-the-remote-desktop-activex-control-with-virtual-channels)  
- [Enumerations Guide](https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control-enumerations)  

*Last Updated: October 2023 | Compatible with Windows Server 2012+*

## Source References

1. **Remote Desktop ActiveX control - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control  
   Quality: 5/10  

2. **Remote Desktop ActiveX control reference - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control-reference  
   Quality: 5/10  

3. **Using the Remote Desktop ActiveX control - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/using-remote-desktop-web-connection  
   Quality: 8/10  

4. **Windows API - Win32 - Microsoft Q&A**  
   URL: https://learn.microsoft.com/answers/tags/224/windows-api-win32/  
   Quality: 5/10  

5. **Remote Desktop ActiveX control enumerations - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control-enumerations  
   Quality: 8/10  

6. **Remote Desktop ActiveX control classes - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control-classes  
   Quality: 6/10  

7. **Remote Desktop ActiveX control interfaces - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-web-connection-reference  
   Quality: 5/10  

8. **Sample webpage included with the Remote Desktop ActiveX control - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/sample-web-page-included-with-the-remote-desktop-activex-control  
   Quality: 6/10  

9. **Using the Remote Desktop ActiveX control with virtual channels - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/using-the-remote-desktop-activex-control-with-virtual-channels  
   Quality: 8/10  

10. **Embedding the Remote Desktop ActiveX control in a webpage - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/embedding-the-remote-desktop-activex-control-in-a-web-page  
   Quality: 8/10  

11. **Implementing scriptable virtual channels by using Remote Desktop Web Connection - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/implementing-scriptable-virtual-channels-using-remote-desktop-web-connection  
   Quality: 5/10  

12. **Providing for RDP client security - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/providing-for-rdp-client-security  
   Quality: 5/10  

13. **Disabling Remote Desktop Services features - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/disabling-terminal-services-features  
   Quality: 7/10  

14. **Calling Remote Desktop ActiveX control non-scriptable interfaces - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/calling-non-scriptable-interfaces  
   Quality: 7/10  

15. **Windows API - Win32 - Microsoft Q&A**  
   URL: https://learn.microsoft.com/answers/tags/224/windows-api-win32/?orderby=updatedat&page=1  
   Quality: 5/10  

16. **Windows API - Win32 - Microsoft Q&A**  
   URL: https://learn.microsoft.com/en-us/answers/tags/224/windows-api-win32?page=0  
   Quality: 5/10  

17. **Windows API - Win32 - Microsoft Q&A**  
   URL: https://learn.microsoft.com/answers/tags/224/windows-api-win32/?orderby=createdat&page=1  
   Quality: 5/10  

18. **Windows API - Win32 - Microsoft Q&A**  
   URL: https://learn.microsoft.com/en-us/answers/tags/224/windows-api-win32?page=3  
   Quality: 5/10  

19. **RemoteActionType (rdpappcontainerclient.h) - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/api/rdpappcontainerclient/ne-rdpappcontainerclient-remoteactiontype  
   Quality: 7/10  

20. **SnapshotEncodingType (rdpappcontainerclient.h) - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/api/rdpappcontainerclient/ne-rdpappcontainerclient-snapshotencodingtype  
   Quality: 5/10  

21. **ControlReconnectStatus enumeration - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/controlreconnectstatus  
   Quality: 8/10  

22. **IMsRdpClientAdvancedSettings8 ClientProtocolSpec property - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/imsrdpclientadvancedsettings8-clientprotocolspec  
   Quality: 8/10  

23. **RemoteWindowDisplayedAttribute enumeration - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/remotewindowdisplayedattribute  
   Quality: 6/10  

24. **IMsRdpClient8 Reconnect method - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/imsrdpclient8-reconnect  
   Quality: 8/10  

25. **ControlCloseStatus enumeration - Win32 apps | Microsoft Learn**  
   URL: https://learn.microsoft.com/en-us/windows/win32/termserv/controlclosestatus  
   Quality: 5/10  


---
*Documentation generated by SuperCrawler AI v1.0 on 2025-06-17 16:39:35*