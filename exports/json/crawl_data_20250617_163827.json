{"export_info": {"timestamp": "20250617_163827", "export_date": "2025-06-17T16:38:27.936277", "total_pages": 25, "crawler_version": "SuperCrawler-AI v1.0"}, "crawled_data": [{"ai_response": "**SCORE: 6**  \n**REASONING:**  \n1. **Content Depth and Technical Accuracy**: The page appears to be a technical reference from Microsoft, which suggests accuracy, but the preview indicates restricted access (\"Access to this page requires authorization\"). This limits its usefulness unless the user has the proper credentials. The visible content includes a structured outline (headings like \"Using the Remote Desktop ActiveX control\" and \"Remote Desktop ActiveX control reference\"), but without full access, the depth of technical details (e.g., code examples, API references) cannot be verified.  \n2. **Relevance to Focus Topic**: The title and headings confirm it covers the Remote Desktop ActiveX control in Windows, aligning closely with the focus topic. However, the lack of accessible content reduces its practical relevance.  \n3. **Actionable Information**: If accessible, the page likely provides programming elements and usage guidance (as hinted by the section titles), but the authorization barrier prevents actionability for most users.  \n4. **Usefulness for Developers**: For authorized users, this might be a valuable resource, but for the general public, its utility is limited due to restricted access.  \n\n**RELEVANT: yes** (if accessible; otherwise, partially relevant due to access limitations).", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nRemote Desktop ActiveX control\n\n\n\n\n\n\t\t\t\t2019-10-24\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nDescribes how to use the Remote Desktop ActiveX control.\nIn this section\n \nUsing the Remote Desktop ActiveX control\n \nHow you can use the Remote Desktop ActiveX control to customize the Remote Desktop Services user experience.\n \nRemote Desktop ActiveX control reference\n \nThe following programming elements are used with the Remote Desktop ActiveX control.\n \n \n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:26:25.514097", "title": "Remote Desktop ActiveX control - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control", "word_count": 133}, {"ai_response": "**SCORE: 7**  \n**REASONING:**  \n1. **Content Depth and Technical Accuracy**: The page provides a structured reference for the Remote Desktop ActiveX control, including interfaces, enumerations, and classes. However, the preview suggests limited depth (e.g., no visible code examples or detailed usage scenarios), and access restrictions may hinder usability.  \n2. **Relevance to Focus Topic**: Highly relevant—it directly addresses the Remote Desktop ActiveX control in Windows, its programming elements, and deployment.  \n3. **Actionable Information**: Lists programming elements but lacks practical examples or step-by-step guidance, reducing immediate utility for developers.  \n4. **Usefulness**: Useful as a reference for developers already familiar with the topic, but less so for beginners due to minimal explanatory content.  \n\n**RELEVANT: yes**", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk Learn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nRemote Desktop ActiveX control reference\n\n\n\n\n\n\t\t\t\t2019-10-24\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nThe following programming elements are used with the Remote Desktop ActiveX control.\nIn this section\n \nRemote Desktop ActiveX control interfaces\n \nInterfaces that you can use to create and deploy scriptable virtual channels within Remote Desktop Services web applications.\n \nRemote Desktop ActiveX control enumerations\n \nThe following enumerations are used with the Remote Desktop ActiveX control.\n \nRemote Desktop ActiveX control classes\n \nThe following classes implement the Remote Desktop ActiveX control.\n \n \n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:26:31.646270", "title": "Remote Desktop ActiveX control reference - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control-reference", "word_count": 150}, {"ai_response": "SCORE: 8  \nREASONING: The page provides detailed technical content about the Remote Desktop ActiveX control, including CLSIDs for different Windows versions, scriptable vs. nonscriptable controls, and usage contexts. The structured headings and clear organization enhance readability. However, the lack of code examples or step-by-step implementation guidance slightly limits its actionable value. The content is highly relevant to the focus topic but could benefit from more practical examples or troubleshooting tips.  \n\nRELEVANT: yes", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nUsing the Remote Desktop ActiveX control\n\n\n\n\n\n\t\t\t\t2023-09-26\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nThe following topics contain information about how you can use the Remote Desktop ActiveX control to customize the Remote Desktop Services user experience.\nThe Remote Desktop ActiveX control is available in the following forms:\n\nThe scriptable controlâimplements interfaces that are safe for scripting. This form of the control can be used by web clients or scripting (such as VBScript) hosts.\nThe nonscriptable controlâoffers additional functionality that is not safe for scripting. This form of the control can be used from native or managed code, but this form cannot be used by web clients or scripting-only hosts.\n\nThe following list contains the CLSIDs for the different controls for different operating system versions. Each of the CLSIDs is compatible with later system versions. For example, the CLSID for the scriptable control on Windows Vista will work on later system versions, such as Windows 7.\n\n\n\nSystem version\nScriptable control CLSID\nNonscriptable control CLSID\n\n\n\n\nWindows 8.1, Windows Server 2012 R2\n301B94BA-5D25-4A12-BFFE-3B6E7A616585\n8B918B82-7985-4C24-89DF-C33AD2BBFBCD\n\n\nWindows 8, Windows Server 2012\n5F681803-2900-4C43-A1CC-CF405404A676\nA3BC03A0-041D-42E3-AD22-882B7865C9C5\n\n\nWindows 7, Windows Server 2008\nA9D7038D-B5ED-472E-9C47-94BEA90A5910\n54D38BF7-B1EF-4479-9674-1BD6EA465258\n\n\nWindows Vista with Service Pack 1 (SP1)\n7390F3D8-0439-4C05-91E3-CF5CB290C3D0\nD2EA46A7-C2BF-426B-AF24-E19C44456399\n\n\nWindows Vista\n4EB89FF4-7F78-4A0F-8B8D-2BF02E94E4B2\n4EB2F086-C818-447E-B32C-C51CE2B30D31\n\n\n\n \nIn this section\n \nEmbedding the Remote Desktop ActiveX control in a webpage\n \nExample that demonstrates the use of the scriptable interfaces.\n \nImplementing scriptable virtual channels by using Remote Desktop Web Connection\n \nCode examples that show the steps for implementing scriptable virtual channels with Remote Desktop Web Connection.\n \nUsing the Remote Desktop ActiveX control with virtual channels\n \nIf you have enabled a virtual channels application in your Remote Desktop Services deployment, you can make this application available to client computers.\n \nSample webpage included with the Remote Desktop ActiveX control\n \nA sample webpage (Default.htm) is in the directory where Remote Desktop Web Connection is installed.\n \nProviding for RDP client security\n \nSome properties of the Remote Desktop ActiveX control object are restricted to specific Internet Explorer URL security zones.\n \nDisabling Remote Desktop Services features\n \nFor enhanced security, you might choose to disable Remote Desktop Services features.\n \nCalling Remote Desktop ActiveX control non-scriptable interfaces\n \nProvides guidance for calling non-scriptable Remote Desktop ActiveX controls from native and managed code.\n\n\nFor more information about the sample webpage that is included with the installation of the Remote Desktop ActiveX control, see Sample webpage included with the Remote Desktop ActiveX control.\n \n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides detailed technical content about the Remote Desktop ActiveX control, including CLSIDs for different Windows versions, scriptable vs. nonscriptable controls, and usage contexts. The structured headings and clear organization enhance readability. However, the lack of code examples or step-by-step implementation guidance slightly limits its actionable value. The content is highly relevant to the focus topic but could benefit from more practical examples or troubleshooting tips.", "relevant": true, "score": 8, "scraped_at": "2025-06-17 16:26:38.653985", "title": "Using the Remote Desktop ActiveX control - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/using-remote-desktop-web-connection", "word_count": 461}, {"ai_response": "**SCORE: 4**  \n**REASONING:**  \n1. **Content Depth and Technical Accuracy:** The page is a general Q&A hub for the Windows API (Win32), not specifically focused on `remote-desktop-activex-control`. While it may contain technical discussions, the preview shows no direct content or code examples related to the focus topic.  \n2. **Relevance to Focus Topic:** Low relevance—the page is a broad tag filter for Win32 API questions, not a dedicated resource for ActiveX controls or Remote Desktop.  \n3. **Actionable Information:** No immediate actionable information (e.g., tutorials, API references, or code snippets) for the focus topic is visible in the preview.  \n4. **Usefulness for Developers:** Potentially useful for general Win32 development but not for the specific query.  \n\n**RELEVANT: no**", "content": "Windows API - Win32\n\t\t\t\n\n\t\t\t\tA core set of Windows application programming interfaces (APIs) for desktop and server applications. Previously known as Win32 API.\n\t\t\t\n\n\n\n\n\t\t\t\t\t2,772 questions\n\t\t\t\t\n\n\n\n\n\n\n\t\t\t\t\t\tSign in to follow\n\t\t\t\t\t\n\n\n\n\t\tFollow", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:26:48.518128", "title": "Windows API - Win32 - Microsoft Q&A", "url": "https://learn.microsoft.com/answers/tags/224/windows-api-win32/", "word_count": 30}, {"ai_response": "SCORE: 8  \nREASONING: The page provides detailed technical content about Remote Desktop ActiveX control enumerations, including structured headings and clear descriptions of each enumeration. It is highly relevant to the focus topic, offering actionable information for developers working with the Remote Desktop ActiveX control. However, the lack of code examples or API usage snippets slightly limits its practical utility. The page is also behind an authorization wall, which may hinder accessibility for some users.  \n\nRELEVANT: yes", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nRemote Desktop ActiveX control enumerations\n\n\n\n\n\n\t\t\t\t2020-08-19\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nThe following enumerations are used with the Remote Desktop ActiveX control.\nIn this section\n \nControlCloseStatus\n \nIndicates whether the application containing the control can close the control immediately.\n \nControlReconnectStatus\n \nSpecifies the result of the IMsRdpClient8::Reconnect method.\n \nClientSpec\n \nUsed with the ClientProtocolSpec property to specify the remote desktop protocol used between the client and the server.\n \nExtendedDisconnectReasonCode\n \nDefines extended information about the control's reason for disconnection.\n \nRedirectDeviceType\n \nUsed to specify the type of a device.\n \nRemoteActionType\n \nThe action to send to the remote session.\n \nRemoteSessionActionType\n \nUsed to specify the type of remote action.\n \nRemoteWindowDisplayedAttribute\n \nUsed with the method to specify information about the event.\n \nSnapshotEncodingType\n \nThe type of encoding used for a Remote Desktop Protocol (RDP) app container client snapshot.\n \nSnapshotFormatType\n \nThe data format used for a Remote Desktop Protocol (RDP) app container client snapshot.\n \n \n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides detailed technical content about Remote Desktop ActiveX control enumerations, including structured headings and clear descriptions of each enumeration. It is highly relevant to the focus topic, offering actionable information for developers working with the Remote Desktop ActiveX control. However, the lack of code examples or API usage snippets slightly limits its practical utility. The page is also behind an authorization wall, which may hinder accessibility for some users.", "relevant": true, "score": 8, "scraped_at": "2025-06-17 16:26:53.810975", "title": "Remote Desktop ActiveX control enumerations - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control-enumerations", "word_count": 215}, {"ai_response": "SCORE: 6  \nREASONING: The page provides a structured list of Remote Desktop ActiveX control classes with version details, which is technically relevant. However, it lacks depth in explanations, code examples, or API usage details, reducing its usefulness for developers. The authorization barrier also limits accessibility.  \nRELEVANT: yes", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nRemote Desktop ActiveX control classes\n\n\n\n\n\n\t\t\t\t2019-08-23\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nThe following classes implement the Remote Desktop ActiveX control.\nIn this section\n \nMsRdpClient\n \nMicrosoft RDP Client Control (redistributable) - version 2\n \nMsRdpClientNotSafeForScripting\n \nMicrosoft RDP Client Control - version 2\n \nMsRdpClient2\n \nMicrosoft RDP Client Control (redistributable) - version 3\n \nMsRdpClient2a\n \nMicrosoft RDP Client Control (redistributable) - version 3a\n \nMsRdpClient2NotSafeForScripting\n \nMicrosoft RDP Client Control - version 3\n \nMsRdpClient3\n \nMicrosoft RDP Client Control (redistributable) - version 4\n \nMsRdpClient3a\n \nMicrosoft RDP Client Control (redistributable) - version 4a\n \nMsRdpClient3NotSafeForScripting\n \nMicrosoft RDP Client Control - version 4\n \nMsRdpClient4\n \nMicrosoft RDP Client Control (redistributable) - version 5\n \nMsRdpClient4a\n \nMicrosoft RDP Client Control (redistributable) - version 5a\n \nMsRdpClient4NotSafeForScripting\n \nMicrosoft RDP Client Control - version 5\n \nMsRdpClient5\n \nMicrosoft RDP Client Control (redistributable) - version 6\n \nMsRdpClient5NotSafeForScripting\n \nMicrosoft RDP Client Control - version 6\n \nMsRdpClient6\n \nMicrosoft RDP Client Control (redistributable) - version 7\n \nMsRdpClient6NotSafeForScripting\n \nMicrosoft RDP Client Control - version 7\n \nMsRdpClient7\n \nMicrosoft RDP Client Control (redistributable) - version 8\n \nMsRdpClient7NotSafeForScripting\n \n\"Microsoft RDP Client Control - version 8\n \nMsRdpClient8\n \nMicrosoft RDP Client Control (redistributable) - version 9\n \nMsRdpClient8NotSafeForScripting\n \nMicrosoft RDP Client Control - version 9\n \nMsRdpClient9\n \nMicrosoft RDP Client Control (redistributable) - version 10\n \nMsRdpClient9NotSafeForScripting\n \nMicrosoft RDP Client Control - version 10\n \nMsRdpClient10\n \nMicrosoft RDP Client Control (redistributable) - version 11\n \nMsRdpClient10NotSafeForScripting\n \nMicrosoft RDP Client Control - version 11\n \nMsRdpClient11\n \nMicrosoft RDP Client Control (redistributable) - version 12\n \nMsRdpClient11NotSafeForScripting\n \nMicrosoft RDP Client Control - version 12\n \nMsRdpClient12\n \nMicrosoft RDP Client Control (redistributable) - version 13\n \nMsRdpClient11NotSafeForScripting\n \nMicrosoft RDP Client Control - version 13\n\nMsTscAx\n \nMicrosoft Terminal Services Client Control (redistributable) - version 1\n \nMsTscAxNotSafeForScripting\n \nMicrosoft Terminal Services Client Control - version 1\n \nRemoteDesktopClient\n \nImplements the Microsoft Windows Store App Remote Desktop Client Control - version 1\n \n \n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides a structured list of Remote Desktop ActiveX control classes with version details, which is technically relevant. However, it lacks depth in explanations, code examples, or API usage details, reducing its usefulness for developers. The authorization barrier also limits accessibility.", "relevant": true, "score": 6, "scraped_at": "2025-06-17 16:27:02.650500", "title": "Remote Desktop ActiveX control classes - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control-classes", "word_count": 358}, {"ai_response": "**SCORE: 8**  \n**REASONING:**  \n1. **Content depth and technical accuracy**: The page provides a detailed list of Remote Desktop ActiveX control interfaces (e.g., `IMsRdpClient` to `IMsRdpClient9`), including their inheritance hierarchy and purpose. This is technically accurate and aligns with Microsoft's documentation standards. However, it lacks code examples or practical usage scenarios, which would improve depth.  \n\n2. **Relevance to the focus topic**: The content is highly relevant to the focus topic, as it directly addresses the Remote Desktop ActiveX control interfaces in Windows, their structure, and their roles.  \n\n3. **Presence of actionable information**: While the page lists interfaces and their relationships, it does not provide actionable examples (e.g., how to implement or call these interfaces in code). This limits its immediate utility for developers.  \n\n4. **Overall usefulness for developers**: The page is useful for developers needing reference material on the ActiveX control interfaces, but its value would increase significantly with code snippets, configuration guides, or troubleshooting tips.  \n\n**RELEVANT: yes**", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk Learn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nRemote Desktop ActiveX control interfaces\n\n\n\n\n\n\t\t\t\t2020-08-19\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nThe Remote Desktop ActiveX control supports the following scriptable and nonscriptable interfaces, which are grouped according to the inheritance hierarchy.\nIn this section\n \nIMsRdpClient\n \nProvides the methods and properties needed to configure and use the client control. Derives from the IMsTscAx interface.\n \nIMsRdpClient2\n \nProvides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient interface.\n \nIMsRdpClient3\n \nProvides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient2 interface.\n \nIMsRdpClient4\n \nProvides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient3 interface.\n \nIMsRdpClient5\n \nProvides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient4 interface.\n \nIMsRdpClient6\n \nProvides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient5 interface.\n \nIMsRdpClient7\n \nProvides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient6 interface.\n \nIMsRdpClient8\n \nProvides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient7 interface.\n \nIMsRdpClient9\n \nProvides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient8 interface.\n \nIMsRdpClient10\n \nProvides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient9 interface.\n \nIMsRdpClientAdvancedSettings\n \nManages advanced client settings. Derives from the IMsTscAdvancedSettings interface.\n \nIMsRdpClientAdvancedSettings2\n \nManages advanced client settings. Derives from the IMsRdpClientAdvancedSettings interface.\n \nIMsRdpClientAdvancedSettings3\n \nManages advanced client settings. Derives from the IMsRdpClientAdvancedSettings2 interface.\n \nIMsRdpClientAdvancedSettings4\n \nManages advanced client settings. Derives from the IMsRdpClientAdvancedSettings3 interface.\n \nIMsRdpClientAdvancedSettings5\n \nManages advanced client settings. Derives from the IMsRdpClientAdvancedSettings4 interface.\n \nIMsRdpClientAdvancedSettings6\n \nExposes properties that manage advanced ActiveX control settings.\n \nIMsRdpClientAdvancedSettings7\n \nExposes methods and properties that manage advanced settings of the ActiveX control.\n \nIMsRdpClientAdvancedSettings8\n \nExposes methods and properties that manage advanced settings of the Remote Desktop ActiveX control.\n \nIMsRdpClientNonScriptable\n \nProvides access to the nonscriptable properties of a client's remote session on the Remote Desktop ActiveX control. Derives from the IMsTscNonScriptable interface.\n \nIMsRdpClientNonScriptable2\n \nProvides access to the nonscriptable properties of a client's remote session on the Remote Desktop ActiveX control. Derives from the IMsRdpClientNonScriptable interface.\n \nIMsRdpClientNonScriptable3\n \nProvides access to the nonscriptable properties of a client's remote session on the Remote Desktop ActiveX control. Derives from the IMsRdpClientNonScriptable2 interface.\n \nIMsRdpClientNonScriptable4\n \nProvides access to the nonscriptable properties of a client's remote session on the Remote Desktop ActiveX control. Derives from the IMsRdpClientNonScriptable3 interface.\n \nIMsRdpClientNonScriptable5\n \nProvides access to the nonscriptable properties of a client's remote session on the Remote Desktop ActiveX control. Derives from the IMsRdpClientNonScriptable4 interface.\n \nIMsRdpClientSecuredSettings\n \nIncludes methods to retrieve and set properties of the Remote Desktop ActiveX control that are restricted to specific Internet Explorer URL security zones.\n \nIMsRdpClientSecuredSettings2\n \nDefines additional properties of the Remote Desktop ActiveX control that are restricted to specific Internet Explorer URL security zones.\n \nIMsRdpClientShell\n \nRemote Desktop Connection (RDC) client settings that are used to launch the client from Remote Desktop Web Access (RD Web Access) or from other web portals.\n \nIMsRdpClientTransportSettings\n \nManages client transport settings for the Remote Desktop Gateway (RD Gateway) server.\n \nIMsRdpClientTransportSettings2\n \nManages client transport settings for the RD Gateway server.\n \nIMsRdpClientTransportSettings3\n \nDefines additional properties for the RD Gatew", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:27:10.748460", "title": "Remote Desktop ActiveX control interfaces - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-web-connection-reference", "word_count": 992}, {"ai_response": "SCORE: 6  \nREASONING: The page provides technical content related to the Remote Desktop ActiveX control, including installation requirements, usage, and how the sample webpage (Default.htm) functions. It has structured headings and mentions the role of the control in initiating remote desktop sessions. However, it lacks detailed code examples or API references, and the content is somewhat superficial. The page is also partially restricted, which limits accessibility.  \n\nRELEVANT: yes", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk Learn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nSample webpage included with the Remote Desktop ActiveX control\n\n\n\n\n\n\t\t\t\t2019-08-23\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nWhen you install the Remote Desktop ActiveX control of Remote Desktop Web Connection, a sample webpage (Default.htm) is copied to the directory where Remote Desktop Web Connection is installed. This page can be run as-is or customized to fit the needs of a user or an organization.\nThe sample webpage must be installed on a web server running Windows Server and Internet Information Server 4.0 or later. Also, the browser on the client computer should be Internet Explorer version 4.0 or later. For more information, see Requirements for Remote Desktop Web Connection.\nDefault.htm is a default logon webpage that collects connection information from the user. The logon page contains one required field into which the user enters the name of the Remote Desktop Session Host (RD Session Host) server or remote computer to connect to. The logon page also includes optional fields for screen size and user logon information, including user name and domain.\nAfter collecting the user data, Default.htm passes it to the Remote Desktop ActiveX control (Msrdp.ocx) to initiate a remote desktop session. The steps involved in initiating the session are the following:\n\nIf necessary, Internet Explorer downloads the .cab file and installs it on the user's computer.\n\nThe user enters the fully qualified domain name or IP address of the remote computer in the appropriate field.\n\nThe user can optionally specify a screen size for the connection. If the user does not specify a screen size, the session opens in full-screen mode if the user is in a trusted URL security zone. Otherwise, the session opens in window mode.\nThe user can optionally supply automatic logon information (user name, domain) for the remote session.\n\n\nWhen the user clicks the Connect button, Default.htm passes the user-supplied data as parameters to the Remote Desktop ActiveX control.\n\nThe remote desktop opens in the browser window or in full-screen mode, as specified by the user.\n\n\nWhen Default.htm opens for the first time using Internet Explorer, a dialog box appears giving the user the option to download the Remote Desktop ActiveX control (Msrdp.ocx). The dialog box appears under the following conditions:\n\nThe computer that accessed the webpage does not have a full installation of the Remote Desktop Web Connection client (or the Remote Desktop Services Advanced Client) with web support.\nThe installed version of the .cab file on the client computer is older than the version on the Default.htm webpage.\n\nThe size of the remote desktop session that appears in the browser window is the size specified in the Default.htm webpage. To change the screen size, the user must disconnect from the session, return to the Default.htm webpage and edit the connection information. If no screen size was specified, the session opens in the browser window in the default full-screen mode. At this resolution the remote desktop expands to match the full dimensions of the user's screen and the Internet Explorer browser toolbars and window frames do not display. As with the full Remote Desktop Connection client, the user can toggle between full-screen and window mode by pressing the full-screen mode shortcut key combination (CTRL+ALT+BREAK).\nFor security reasons, some options that are available in the Remote Desktop Services Connection Manager are restricted in the Remote Desktop ActiveX control to certain Internet Explorer URL security zones. Refer to Providing for RDP Client Security for more information about these options and about specifying a program to run upon connection.\n \n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides technical content related to the Remote Desktop ActiveX control, including installation requirements, usage, and how the sample webpage (Default.htm) functions. It has structured headings and mentions the role of the control in initiating remote desktop sessions. However, it lacks detailed code examples or API references, and the content is somewhat superficial. The page is also partially restricted, which limits accessibility.", "relevant": true, "score": 6, "scraped_at": "2025-06-17 16:27:19.375547", "title": "Sample webpage included with the Remote Desktop ActiveX control - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/sample-web-page-included-with-the-remote-desktop-activex-control", "word_count": 651}, {"ai_response": "SCORE: 8  \nREASONING: The page provides technical content related to the Remote Desktop ActiveX control, including specific code examples for configuring virtual channels. It covers security considerations and deployment steps, which are useful for developers. However, the content is partially restricted due to authorization requirements, limiting accessibility. The structure is clear with headings and code snippets, but the lack of full visibility affects its overall usefulness.  \nRELEVANT: yes", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nUsing the Remote Desktop ActiveX control with virtual channels\n\n\n\n\n\n\t\t\t\t2019-08-23\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nIf you have enabled a virtual channels application in your Remote Desktop Services deployment, you can make this application available to client computers that access the Remote Desktop Session Host (RD Session Host) server by means of the Remote Desktop ActiveX control.\nTo make a virtual channel application available\n\nDeploy the server-side module of the application and make sure it is running on the RD Session Host server. In the connection page of the Remote Desktop Services web application running on your web server, access the PluginDlls property of the IMsTscAdvancedSettings interface to specify the name of your virtual channel DLL. If you have more than one plug-in, specify a comma-delimited list of DLL names. For instance, if your virtual channel plug-in is named \"MyPlugin.dll\", use the following code:\nMsRdpClient.AdvancedSettings.PluginDlls = \"myplugin.dll\"\n\nUse the following code if you have two virtual channel DLLs. In this example, the DLL file names are \"MyPlugin.dll\" and \"Vdriver.dll\":\nMsRdpClient.AdvancedSettings.PluginDlls = \"myplugin.dll,Vdriver.dll\"\n\nFor security reasons, the PluginDlls property only accepts a named list of virtual channel DLLs. The control returns an error if any form of file system or UNC path is specified. In addition, the names of the DLLs must contain only alphanumeric characters.\n\nEnsure that the client-side module is installed in the %windir%\\system32 directory.\n\n\nThe virtual channel API does not allow for multiple instances of the same virtual channel DLL to be loaded within a single process. Because of this, if there are multiple instances of the Remote Desktop ActiveX control running within the same process, only the first instance of the control will be able to load the virtual channel DLL. If you are designing a virtual channel application that must support multiple instances within a single process, you must use the Dynamic Virtual Channels API to implement your virtual channel application.\n\nNote\nBy default, the Remote Desktop ActiveX control loads virtual channel client DLLs from the %windir%\\system32 directory. It is possible for an administrator to change this default client plug-in DLL directory. To do so, edit the HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Terminal Server Client\\vdllpath registry key on the client computer. This directory path cannot be specified in the UNC format.\n\n \n \n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides technical content related to the Remote Desktop ActiveX control, including specific code examples for configuring virtual channels. It covers security considerations and deployment steps, which are useful for developers. However, the content is partially restricted due to authorization requirements, limiting accessibility. The structure is clear with headings and code snippets, but the lack of full visibility affects its overall usefulness.", "relevant": true, "score": 8, "scraped_at": "2025-06-17 16:27:26.476279", "title": "Using the Remote Desktop ActiveX control with virtual channels - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/using-the-remote-desktop-activex-control-with-virtual-channels", "word_count": 441}, {"ai_response": "SCORE: 8  \nREASONING: The page provides technical content related to embedding the Remote Desktop ActiveX control in a webpage, including a code example and styling details. It is structured with headings and metadata, indicating a formal Microsoft documentation standard. However, the preview suggests potential access restrictions, which could limit usability. The content is accurate and relevant to developers working with Windows Remote Desktop integration.  \n\nRELEVANT: yes", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON><PERSON>\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nEmbedding the Remote Desktop ActiveX control in a webpage\n\n\n\n\n\n\t\t\t\t2020-02-20\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nYou can embed the Remote Desktop ActiveX control in a web page by using code that is similar to the following.\n<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0 Transitional//EN\">\n<html>\n<head>\n    <title id=\"TSWEBTITLE\">Remote Desktop Web Connection</title>\n    <meta content=\"text/html; charset=windows-1252\" http-equiv=\"Content-Type\">\n    <style type=\"text/css\" media=\"screen\">\n        P\n        {\n            font-family: Verdana, Arial, Helvetica;\n            color: #000000;\n            font-size: 65%;\n        }\n        H1\n        {\n            margin-top: 0em;\n            font-family: verdana, arial, helvetica;\n            font-size: 100%;\n            font-weight: bold;\n        }\n        P.indent\n        {\n            line-height: 1.25em;\n            margin: 0.5em 1em 0.2em 3em;\n        }\n        .button\n        {\n            border-bottom-color: #6699ff;\n            background-color: #ffffff;\n            margin-top: 6pt;\n            border-top-color: #6699ff;\n            font-family: Verdana, Helvetica, Arial, Sans-Serif;\n            color: #000000;\n            border-right-color: #6699ff;\n            margin-left: 0.5em;\n            font-size: 70%;\n            border-left-color: #6699ff;\n            font-weight: normal;\n        }\n        .topspace\n        {\n            margin-top: 0.08em;\n        }\n    </style>\n    <meta name=\"GENERATOR\" content=\"MSHTML 8.00.7600.16625\">\n</head>\n<body bgcolor=\"#ffffff\">\n    <script type=\"text/vbscript\" language=\"vbscript\">\n<!--\nconst L_FullScreenWarn1_Text = \"Your current security settings do not allow automatically switching to fullscreen mode.\"\nconst L_FullScreenWarn2_Text = \"You can use ctrl-alt-pause to toggle your remote desktop session to fullscreen mode\"\nconst L_FullScreenTitle_Text = \"Remote Desktop Web Connection \"\nconst L_ErrMsg_Text         = \"Error connecting to remote computer: \"\nconst L_ClientNotSupportedWarning_Text = \"Remote Desktop 6.0 does not support CredSSP over TSWeb.\"\n\n' error messages\nconst L_RemoteDesktopCaption_ErrorMessage =  \"Remote Desktop Connection\"\nconst L_InvalidServerName_ErrorMessage = \"An invalid server name was specified.\"\n\nsub window_onload()\n   if not autoConnect() then\n       Document.all.editServer.Focus\n   end if\nend sub\n\nfunction autoConnect()\n    Dim sServer\n    Dim iFS, iAutoConnect\n\n    sServer = getQS (\"Server\")\n    iAutoConnect = getQS (\"AutoConnect\")\n    iFS = getQS (\"FS\")\n\n    if NOT IsNumeric ( iFS ) then\n        iFS = 0\n    else\n        iFS = CInt ( iFS )\n    end if\n\n    if iAutoConnect <> 1 then\n        autoConnect = false\n        exit function\n    else\n        if iFS < 0 or iFS >= Document.all.comboResolution.options.length then\n            iFS = 0\n        end if\n\n        if IsNull ( sServer ) or sServer = \"\" then\n            sServer = window.location.hostname\n        end if\n\n        Document.all.comboResolution.selectedIndex    = iFS\n        Document.all.Server.value = sServer\n\n        btnConnect ()\n\n        autoConnect = true\n    end if\n\nend function\n\nfunction getQS ( sKey )\n    Dim iKeyPos, iDelimPos, iEndPos\n    Dim sURL, sRetVal\n    iKeyPos = iDelimPos = iEndPos = 0\n    sURL = window.location.href\n\n    if sKey = \"\" Or Len(sKey) < 1 then\n        getQS = \"\"\n        exit function\n    end if\n\n    iKeyPos = InStr ( 1, sURL, sKey )\n\n    if iKeyPos = 0 then\n        sRetVal = \"\"\n        exit function\n    end if\n\n    iDelimPos = InStr ( iKeyPos, sURL, \"=\" )\n    iEndPos = InStr ( iDelimPos, sURL, \"&\" )\n\n    if iEndPos = 0 then\n        sRetVal = Mid ( sURL, iDelimPos + 1 )\n    else\n        sRetVal = Mid ( sURL, iDelimPos + 1, iEndPos - iDelimPos - 1 )\n    end if\n\n    getQS = sRetVal\nend function\n\nsub checkClick\n    if Document.all.Check1.Checked then\n        Document.all.tableLogonInfo.style.display = \"\"\n        Document.all.editUserName.Disabled = false\n        Document.all.editDomain.Disabled = false\n    else\n        Document.all.tableLogonInfo.style.display = \"none\"\n        Document.all.editUserName.Disabled = true\n        Document.all.editDomain.Disabled = true\n    end if\nend sub\n\nsub OnControlLoadError\n    Document.all.cerrmsg.style.display = \"block\"\n    Document.all.csafemsg.style.display = \"none\"\n    Document.all.Server.disabled = TRUE\n    Document.all.comboResolution.disabled = TRUE\nend sub\n\nsub OnControlLoad\n   set Control = Document.getElementById(\"MsRdpClient\")\n   if Not Control is Nothing then\n      if Control.readyState = 4 then\n ", "reasoning": "The page provides technical content related to embedding the Remote Desktop ActiveX control in a webpage, including a code example and styling details. It is structured with headings and metadata, indicating a formal Microsoft documentation standard. However, the preview suggests potential access restrictions, which could limit usability. The content is accurate and relevant to developers working with Windows Remote Desktop integration.", "relevant": true, "score": 8, "scraped_at": "2025-06-17 16:27:33.601210", "title": "Embedding the Remote Desktop ActiveX control in a webpage - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/embedding-the-remote-desktop-activex-control-in-a-web-page", "word_count": 1311}, {"ai_response": "**SCORE: 8**  \n**REASONING:**  \n1. **Content depth and technical accuracy**: The page provides clear, step-by-step instructions and code examples (VBScript) for implementing scriptable virtual channels using the Remote Desktop ActiveX control. The technical details are accurate and align with Microsoft's documentation standards.  \n2. **Relevance to the focus topic**: Highly relevant, as it directly addresses the usage of the Remote Desktop ActiveX control (`MsRdpClient`) for virtual channel scripting, a core aspect of the focus topic.  \n3. **Presence of actionable information**: Includes practical steps (e.g., `CreateVirtualChannels`, `SendOnVirtualChannel`) and event handling (`OnChannelReceivedData`), making it actionable for developers.  \n4. **Overall usefulness**: Useful for developers working with Remote Desktop Services, though the authorization requirement (note about restricted access) may limit immediate accessibility.  \n\n**RELEVANT: yes**", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nImplementing scriptable virtual channels by using Remote Desktop Web Connection\n\n\n\n\n\n\t\t\t\t2019-08-23\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nThe following procedure and code examples show the steps for implementing scriptable virtual channels with Remote Desktop Web Connection. The examples were written in Visual Basic Scripting Edition and assume that the Remote Desktop ActiveX control is named \"MsRdpClient\".\nTo create and deploy scriptable virtual channels\n\nDeploy the server side of the application and make sure it is running on the Remote Desktop Session Host (RD Session Host) server. For information about deploying virtual channels applications on the server, see Virtual Channel Server Application.\n\nIn your client script, call IMsTscAx::CreateVirtualChannels, passing a string that contains a comma-separated list of virtual channel names.\nMsRdpClient.CreateVirtualChannels(\"mychan1,mychan2\")\n\nFor information about virtual channel naming restrictions, see Virtual Channel Client Registration.\n\nCall IMsTscAx::Connect to create your Remote Desktop Services connection.\nMsRdpClient.connect\n\n\nUse the IMsTscAx::SendOnVirtualChannel method to send data to the server, passing a string that contains the virtual channel name and a second string that contains the data to be passed.\nMsRdpClient.SendOnVirtualChannel(\"mychan1\",\"hello from the client\")\n\n\nReceive data from the server on the IMsTscAxEvents::OnChannelReceivedData event.\nSub MsRdpClient.OnChannelReceivedData(chanName,data)\nMsgbox(\"received data:\" &data& \"on virtual channel:\" &chanName)\nEnd sub\n\n\n\n \n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:27:39.862870", "title": "Implementing scriptable virtual channels by using Remote Desktop Web Connection - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/implementing-scriptable-virtual-channels-using-remote-desktop-web-connection", "word_count": 266}, {"ai_response": "**SCORE: 8**  \n**REASONING:**  \n1. **Content depth and technical accuracy**: The page provides detailed technical information about RDP client security, including restricted properties and security zones, which indicates accuracy and depth. However, the lack of code examples or API snippets slightly limits its practical utility.  \n2. **Relevance to the focus topic**: Highly relevant, as it directly addresses the Remote Desktop ActiveX control, its security restrictions, and interfaces like `IMsTscSecuredSettings`.  \n3. **Presence of actionable information**: Offers actionable guidance (e.g., checking `SecuredSettingsEnabled`) but would benefit from concrete examples.  \n4. **Overall usefulness for developers**: Useful for developers working with RDP integrations, though the authorization barrier and absence of examples reduce accessibility.  \n\n**RELEVANT: yes**", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk Learn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nProviding for RDP client security\n\n\n\n\n\n\t\t\t\t2019-08-23\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nTo allow clients to protect themselves from potentially untrustworthy servers, some properties of the Remote Desktop ActiveX control object are restricted to specific Internet Explorer URL security zones. This means that, when a user browsing the web accesses the page and the page is in a higher URL security zone than the computer they are browsing the web with, these properties are disabled. These restricted properties are accessed using the IMsTscSecuredSettings interface and the IMsRdpClientSecuredSettings interface, and are available in the following Internet Explorer URL security zones:\n\nMy computer\nLocal intranet\nTrusted sites\n\nThey are disabled in these zones:\n\nInternet\nRestricted sites\n\nIf you call these restricted properties within your Remote Desktop Services web application, you should call IMsTscAx::get_SecuredSettings and IMsTscAx::get_SecuredSettingsEnabled to access the Secured Settings properties.\nThe restricted properties that the IMsTscSecuredSettings interface accesses are the following:\n\nStartProgram. This property specifies the program that will be started upon connection.\nWorkDir. This property specifies the working directory of the program specified in StartProgram.\nFullScreen. This property specifies whether the state of the control upon connection will be in full-screen or window mode. If the value of this property is TRUE, the connection will be opened in full-screen mode. Although the use of the FullScreen property is restricted to the Internet Explorer URL security zones listed earlier, a user can always change to full-screen mode after connection by pressing the full-screen mode shortcut key combination (CTRL+ALT+BREAK).\n\nThe properties that the IMsRdpClientSecuredSettings interface accesses are the following:\n\nAudioRedirectionMode. This property specifies whether to redirect sounds, or play sounds at the Remote Desktop Session Host (RD Session Host) server.\nKeyboardHookMode. This property specifies how and when to apply Windows key combinations; for example, ALT+TAB.\n\nThe following script launches Microsoft Notepad.exe upon connection. Run this script before calling the IMsTscAx::Connect method.\nif MsRdpClient.SecuredSettingsEnabled then\n    MsRdpClient.SecuredSettings.StartProgram = \"notepad.exe\"\nelse\n    msgbox \"Cannot access secured setting (startprogram) in the current browser zone\"\nend if\n\n \n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:27:47.913796", "title": "Providing for RDP client security - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/providing-for-rdp-client-security", "word_count": 399}, {"ai_response": "SCORE: 7  \nREASONING: The page provides technical content related to disabling Remote Desktop Services features, including registry edits, which aligns with the focus topic of Remote Desktop ActiveX Control usage. It offers actionable information (registry keys and values) and is structured with headings. However, the content is somewhat limited in depth, lacking broader context or examples of how these changes affect the ActiveX Control. The authorization requirement to access parts of the page may also hinder usability.  \n\nRELEVANT: yes", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nDisabling Remote Desktop Services features\n\n\n\n\n\n\t\t\t\t2020-12-01\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nFor enhanced security, you might choose to disable Remote Desktop Services features such as clipboard redirection and printer redirection for clients that connect to Remote Desktop Session Host (RD Session Host) servers using the Remote Desktop ActiveX Control.\nTo disable Remote Desktop Services features\n\nEdit the registry of the client computer and add the following keys:\n\nHKEY_LOCAL_MACHINE\\Software\\Microsoft\\Terminal Server\\DisableClipboardRedirection\nHKEY_LOCAL_MACHINE\\Software\\Microsoft\\Terminal Server\\DisableDriveRedirection\nHKEY_LOCAL_MACHINE\\Software\\Microsoft\\Terminal Server\\DisablePrinterRedirection\n\n\nSet the value of both keys to REG_DWORD 1.\n\n\n \n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides technical content related to disabling Remote Desktop Services features, including registry edits, which aligns with the focus topic of Remote Desktop ActiveX Control usage. It offers actionable information (registry keys and values) and is structured with headings. However, the content is somewhat limited in depth, lacking broader context or examples of how these changes affect the ActiveX Control. The authorization requirement to access parts of the page may also hinder usability.", "relevant": true, "score": 7, "scraped_at": "2025-06-17 16:27:55.823337", "title": "Disabling Remote Desktop Services features - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/disabling-terminal-services-features", "word_count": 153}, {"ai_response": "SCORE: 7  \nREASONING: The page provides technical content related to the Remote Desktop ActiveX control, including code examples for both native and managed code, which is useful for developers. However, the content is limited in depth, focusing only on calling non-scriptable interfaces without broader context or advanced use cases. The page also requires authorization, which may hinder accessibility. The relevance to the focus topic is high, but the lack of comprehensive coverage and potential access issues lower its overall quality score.  \nRELEVANT: yes", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nCalling non-scriptable interfaces from native code\n\n\n\n\n\n\t\t\t\t2023-09-26\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nThe Windows SDK does not provide header files for the Remote Desktop ActiveX control. This article provides guidance for calling the non-scriptable interfaces it exports from both native and managed code.\nCall non-scriptable interfaces from native code\nTo call non-scriptable interfaces from native code, use the #import directive supported by the Microsoft C++ (MSVC) compiler.\n#import \"libid:8C11EFA1-92C3-11D1-BC1E-00C04FA31489\"\n#include \"mstscax.tlh\"\n\nThe `#import`` directive will generate a header file named \"mstscax.tlh\" for the Remote Desktop ActiveX control type library. The generated header file can then be included. For this snippet to compile, the developer will need to ensure that the folder where the header file is generated is part of the directories searched for include files by the compiler.\nCall scriptable interfaces from managed code\nTo call the interfaces of the Remote Desktop ActiveX control from managed code, use the ActiveX Control Importer tool to generate a .NET assembly.\naximp.exe c:\\windows\\system32\\mstscax.dll\n\nThe generated assembly can then be referenced by a managed code project in Visual Studio.\nRelated topics\nUsing the Remote Desktop ActiveX control\n \n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides technical content related to the Remote Desktop ActiveX control, including code examples for both native and managed code, which is useful for developers. However, the content is limited in depth, focusing only on calling non-scriptable interfaces without broader context or advanced use cases. The page also requires authorization, which may hinder accessibility. The relevance to the focus topic is high, but the lack of comprehensive coverage and potential access issues lower its overall quality score.", "relevant": true, "score": 7, "scraped_at": "2025-06-17 16:28:00.929940", "title": "Calling Remote Desktop ActiveX control non-scriptable interfaces - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/calling-non-scriptable-interfaces", "word_count": 255}, {"ai_response": "**SCORE: 4**  \n**REASONING:**  \n1. **Content Depth and Technical Accuracy**: The page is a general overview of the Windows API (Win32) and does not specifically address the **Remote Desktop ActiveX Control**. While it may contain technical content, it lacks depth on the focus topic.  \n2. **Relevance to Focus Topic**: The page is not directly relevant to **Remote Desktop ActiveX Control**—it’s a broad Q&A forum for Win32 API questions. No specific mention of the control or its usage is found in the preview.  \n3. **Actionable Information**: The page lists questions (2,772 total), but without direct relevance to the focus topic, it does not provide actionable guidance on the ActiveX control.  \n4. **Usefulness for Developers**: While useful for general Win32 API queries, it does not serve developers seeking details on **Remote Desktop ActiveX Control**.  \n\n**RELEVANT: no**", "content": "Windows API - Win32\n\t\t\t\n\n\t\t\t\tA core set of Windows application programming interfaces (APIs) for desktop and server applications. Previously known as Win32 API.\n\t\t\t\n\n\n\n\n\t\t\t\t\t2,772 questions\n\t\t\t\t\n\n\n\n\n\n\n\t\t\t\t\t\tSign in to follow\n\t\t\t\t\t\n\n\n\n\t\tFollow", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:28:18.004653", "title": "Windows API - Win32 - Microsoft Q&A", "url": "https://learn.microsoft.com/answers/tags/224/windows-api-win32/?orderby=updatedat&page=1", "word_count": 30}, {"ai_response": "**SCORE: 4**  \n**REASONING:**  \n1. **Content Depth and Technical Accuracy:** The page is a general overview of the Windows API (Win32) and does not specifically address the **Remote Desktop ActiveX Control**. It lacks technical depth or detailed explanations on the focus topic.  \n2. **Relevance to Focus Topic:** The page is not directly relevant to **Remote Desktop ActiveX Control**—it’s a broad Q&A hub for Win32 API questions, with no clear mention of the specific control.  \n3. **Actionable Information:** While it may contain useful Win32-related discussions, there are no direct code examples, API references, or guidance on the ActiveX control.  \n4. **Usefulness for Developers:** Developers seeking Win32 API help may find value, but those looking for **Remote Desktop ActiveX Control** details will need to dig deeper (if such content exists in the Q&A threads).  \n\n**RELEVANT: no**", "content": "Windows API - Win32\n\t\t\t\n\n\t\t\t\tA core set of Windows application programming interfaces (APIs) for desktop and server applications. Previously known as Win32 API.\n\t\t\t\n\n\n\n\n\t\t\t\t\t2,772 questions\n\t\t\t\t\n\n\n\n\n\n\n\t\t\t\t\t\tSign in to follow\n\t\t\t\t\t\n\n\n\n\t\tFollow", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:28:23.176180", "title": "Windows API - Win32 - Microsoft Q&A", "url": "https://learn.microsoft.com/en-us/answers/tags/224/windows-api-win32?page=0", "word_count": 30}, {"ai_response": "**SCORE: 4**  \n**REASONING:**  \n1. **Content Depth and Technical Accuracy:** The page is a general overview of the Windows API (Win32) and does not specifically address the **Remote Desktop ActiveX Control**. It lacks technical depth or detailed explanations on the focus topic.  \n2. **Relevance to Focus Topic:** The page is not directly relevant to **Remote Desktop ActiveX Control**—it’s a broad Q&A forum for Win32 API questions, with no clear mention of the specific control.  \n3. **Actionable Information:** No code examples, API references, or usage details related to the focus topic are provided.  \n4. **Usefulness for Developers:** While useful for general Win32 API queries, it does not serve developers seeking information on **Remote Desktop ActiveX Control**.  \n\n**RELEVANT: no**", "content": "Windows API - Win32\n\t\t\t\n\n\t\t\t\tA core set of Windows application programming interfaces (APIs) for desktop and server applications. Previously known as Win32 API.\n\t\t\t\n\n\n\n\n\t\t\t\t\t2,772 questions\n\t\t\t\t\n\n\n\n\n\n\n\t\t\t\t\t\tSign in to follow\n\t\t\t\t\t\n\n\n\n\t\tFollow", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:28:33.267364", "title": "Windows API - Win32 - Microsoft Q&A", "url": "https://learn.microsoft.com/answers/tags/224/windows-api-win32/?orderby=createdat&page=1", "word_count": 30}, {"ai_response": "**SCORE: 4**  \n**REASONING:**  \n1. **Content Depth and Technical Accuracy**: The page is a Microsoft Q&A forum tagged for \"Windows API - Win32,\" which implies technical content, but the preview does not show specific details about the `remote-desktop-activex-control`. It may contain relevant discussions, but without direct visibility, accuracy and depth are uncertain.  \n2. **Relevance to Focus Topic**: The focus topic is not directly addressed in the preview. While Win32 API discussions could include ActiveX controls, there's no guarantee of relevance without searching further.  \n3. **Actionable Information**: Forum threads might include code examples or solutions, but the preview lacks explicit API references or actionable guidance on the focus topic.  \n4. **Usefulness for Developers**: Potentially useful for broader Win32 API questions, but not tailored to the specific query.  \n\n**RELEVANT: no** (Not directly relevant based on the preview; may require deeper exploration of the Q&A threads.)", "content": "Windows API - Win32\n\t\t\t\n\n\t\t\t\tA core set of Windows application programming interfaces (APIs) for desktop and server applications. Previously known as Win32 API.\n\t\t\t\n\n\n\n\n\t\t\t\t\t2,772 questions\n\t\t\t\t\n\n\n\n\n\n\n\t\t\t\t\t\tSign in to follow\n\t\t\t\t\t\n\n\n\n\t\tFollow", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:28:43.784512", "title": "Windows API - Win32 - Microsoft Q&A", "url": "https://learn.microsoft.com/en-us/answers/tags/224/windows-api-win32?page=3", "word_count": 30}, {"ai_response": "SCORE: 7  \nREASONING: The page provides technical content with structured headings, API references, and clear enumeration details for `RemoteActionType`. It is accurate and useful for developers working with remote desktop functionalities in Windows. However, it lacks code examples or deeper explanations of how these actions integrate with the remote-desktop-activex-control, which limits its depth. The relevance to the focus topic is partial, as it covers a specific enumeration but not the broader control.  \n\nRELEVANT: yes (but narrowly focused on one enumeration).", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nRemoteActionType enumeration (rdpappcontainerclient.h)\n\n\n\n\n\n\t\t\t\t2024-02-22\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nThe action to send to the remote session.\nSyntax\ntypedef enum __MIDL_IRemoteDesktopClientActions_0001 {\n  RemoteActionCharms = 0,\n  RemoteActionAppbar = 1,\n  RemoteActionSnap = 2,\n  RemoteActionStartScreen = 3,\n  RemoteActionAppSwitch = 4\n} RemoteActionType;\n\nConstants\n\n\n\n \n\n\n\n\nRemoteActionCharmsValue: 0Displays the charms in the remote session.\n\n\nRemoteActionAppbarValue: 1Displays the app bar in the remote session.\n\n\nRemoteActionSnapValue: 2Docks the application in the remote session.\n\n\nRemoteActionStartScreenValue: 3Causes the start screen to be displayed in the remote session.\n\n\nRemoteActionAppSwitchValue: 4Causes the application switch window to be displayed in the remote session. This is the same as the user pressing Alt+Tab.\n\n\n\nRequirements\n\n\n\nRequirement\nValue\n\n\n\n\nMinimum supported client\nWindows 8\n\n\nMinimum supported server\nWindows Server 2012\n\n\nHeader\nrdpappcontainerclient.h\n\n\n\nSee also\nExecuteRemoteAction\n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides technical content with structured headings, API references, and clear enumeration details for `RemoteActionType`. It is accurate and useful for developers working with remote desktop functionalities in Windows. However, it lacks code examples or deeper explanations of how these actions integrate with the remote-desktop-activex-control, which limits its depth. The relevance to the focus topic is partial, as it covers a specific enumeration but not the broader control.", "relevant": true, "score": 7, "scraped_at": "2025-06-17 16:29:15.448673", "title": "RemoteActionType (rdpappcontainerclient.h) - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/api/rdpappcontainerclient/ne-rdpappcontainerclient-remoteactiontype", "word_count": 189}, {"ai_response": "**SCORE: 6**  \n**REASONING:**  \n1. **Content Depth and Technical Accuracy**: The page provides a brief but accurate technical definition of the `SnapshotEncodingType` enumeration, including its syntax and constants. However, it lacks deeper explanations, use cases, or practical examples.  \n2. **Relevance to Focus Topic**: The content is tangentially related to Remote Desktop Protocol (RDP) but does not directly address the **Remote-Desktop-ActiveX-Control** or its usage. It focuses narrowly on a single enumeration type.  \n3. **Actionable Information**: The page includes minimal actionable information (e.g., the `SnapshotEncodingDataUri` constant) but lacks code snippets, implementation guidance, or links to broader RDP ActiveX control documentation.  \n4. **Usefulness for Developers**: Moderately useful for developers working with RDP app container snapshots, but not comprehensive enough for those seeking broader ActiveX control insights.  \n\n**RELEVANT: no**  \n(The page is too narrow and does not directly cover the focus topic of Remote-Desktop-ActiveX-Control usage or functionality.)", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk Learn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nSnapshotEncodingType enumeration (rdpappcontainerclient.h)\n\n\n\n\n\n\t\t\t\t2024-02-22\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nThe type of encoding used for a Remote Desktop Protocol (RDP) app container client snapshot.\nSyntax\ntypedef enum __MIDL_IRemoteDesktopClientActions_0002 {\n  SnapshotEncodingDataUri = 0\n} SnapshotEncodingType;\n\nConstants\n\n\n\n \n\n\n\n\nSnapshotEncodingDataUriValue: 0The snapshot will be taken and a data URI that contains the snapshot will be returned.\n\n\n\nRequirements\n\n\n\nRequirement\nValue\n\n\n\n\nMinimum supported client\nWindows 8\n\n\nMinimum supported server\nWindows Server 2012\n\n\nHeader\nrdpappcontainerclient.h\n\n\n\nSee also\nGetSnapshot\n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:29:29.485048", "title": "SnapshotEncodingType (rdpappcontainerclient.h) - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/api/rdpappcontainerclient/ne-rdpappcontainerclient-snapshotencodingtype", "word_count": 142}, {"ai_response": "SCORE: 8  \nREASONING: The page provides clear, technical documentation on the `ControlReconnectStatus` enumeration, which is part of the Windows Remote Desktop ActiveX Control API. It includes structured headings, syntax, constants, and requirements, making it useful for developers working with this API. However, the content is somewhat narrow in scope, focusing only on the `Reconnect` method's status codes, and lacks broader context or usage examples. The page is also behind an authorization wall, which may limit accessibility.  \n\nRELEVANT: yes  \n(The content directly relates to the focus topic of Remote Desktop ActiveX Control in Windows, though it is a specific subset of the broader topic.)", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk Learn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nControlReconnectStatus enumeration\n\n\n\n\n\n\t\t\t\t2020-12-11\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nSpecifies the result of the IMsRdpClient8::Reconnect method.\nSyntax\ntypedef enum  { \n  controlReconnectStarted  = 0x0000,\n  controlReconnectBlocked  = 0x0001\n} ControlReconnectStatus;\n\nConstants\n \ncontrolReconnectStarted\n \nThe reconnect operation was started.\n \ncontrolReconnectBlocked\n \nThe reconnect operation could not be started.\n \nRequirements\n\n\n\nRequirement\nValue\n\n\n\n\nMinimum supported client\nWindows 8\n\n\nMinimum supported server\nWindows Server 2012\n\n\nType library\n MsTscAx.dll \n\n\n\nSee also\n \nIMsRdpClient8::Reconnect\n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides clear, technical documentation on the `ControlReconnectStatus` enumeration, which is part of the Windows Remote Desktop ActiveX Control API. It includes structured headings, syntax, constants, and requirements, making it useful for developers working with this API. However, the content is somewhat narrow in scope, focusing only on the `Reconnect` method's status codes, and lacks broader context or usage examples. The page is also behind an authorization wall, which may limit accessibility.", "relevant": true, "score": 8, "scraped_at": "2025-06-17 16:29:41.711867", "title": "ControlReconnectStatus enumeration - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/controlreconnectstatus", "word_count": 133}, {"ai_response": "SCORE: 8  \nREASONING: The page provides detailed technical content about the `IMsRdpClientAdvancedSettings8::ClientProtocolSpec` property, including syntax, property values, and requirements. It is well-structured with clear headings and includes API references, making it useful for developers working with remote desktop protocols in Windows. However, the accessibility issue (requiring authorization) could hinder usability for some users.  \nRELEVANT: yes", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nIMsRdpClientAdvancedSettings8::ClientProtocolSpec property\n\n\n\n\n\n\t\t\t\t2020-12-11\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nSpecifies the remote desktop protocol used between the client and the server.\nThis property is read/write.\nSyntax\nHRESULT put_ClientProtocolSpec(\n  [in]  ClientSpec ClientMode\n);\n\nHRESULT get_ClientProtocolSpec(\n  [out] ClientSpec *pClientMode\n);\n\nProperty value\nA value of the ClientSpec enumeration that specifies the remote desktop protocol used between the client and the server.\nRequirements\n\n\n\nRequirement\nValue\n\n\n\n\nMinimum supported client\nWindows 8\n\n\nMinimum supported server\nWindows Server 2012\n\n\nType library\n MsTscAx.dll \n\n\nDLL\n MsTscAx.dll \n\n\n\nSee also\n \nClientSpec\n \nIMsRdpClientAdvancedSettings8\n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides detailed technical content about the `IMsRdpClientAdvancedSettings8::ClientProtocolSpec` property, including syntax, property values, and requirements. It is well-structured with clear headings and includes API references, making it useful for developers working with remote desktop protocols in Windows. However, the accessibility issue (requiring authorization) could hinder usability for some users.", "relevant": true, "score": 8, "scraped_at": "2025-06-17 16:29:47.977226", "title": "IMsRdpClientAdvancedSettings8 ClientProtocolSpec property - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/imsrdpclientadvancedsettings8-clientprotocolspec", "word_count": 152}, {"ai_response": "SCORE: 6  \nREASONING: The page provides technical content related to the `RemoteWindowDisplayedAttribute` enumeration, which is part of the Windows Remote Desktop ActiveX Control API. It includes structured headings, syntax, constants, and requirements, making it useful for developers working with this specific API. However, the content is limited to just this enumeration and lacks broader context or examples of how it integrates with the Remote Desktop ActiveX Control. The page also requires authorization, which may hinder accessibility.  \n\nRELEVANT: yes (but narrowly focused on a specific enumeration rather than broader Remote Desktop ActiveX Control usage).", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk Learn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nRemoteWindowDisplayedAttribute enumeration\n\n\n\n\n\n\t\t\t\t2020-12-11\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nUsed with the method to specify information about the event.\nSyntax\ntypedef enum  { \n  remoteAppWindowNone          = 0,\n  remoteAppWindowDisplayed     = 1,\n  remoteAppShellIconDisplayed  = 2\n} RemoteWindowDisplayedAttribute;\n\nConstants\n \nremoteAppWindowNone\n  \nremoteAppWindowDisplayed\n  \nremoteAppShellIconDisplayed\n  \nRequirements\n\n\n\nRequirement\nValue\n\n\n\n\nMinimum supported client\nWindows 8\n\n\nMinimum supported server\nWindows Server 2012\n\n\nType library\n MsTscAx.dll \n\n\n\nSee also\n \nOnRemoteWindowDisplayed\n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides technical content related to the `RemoteWindowDisplayedAttribute` enumeration, which is part of the Windows Remote Desktop ActiveX Control API. It includes structured headings, syntax, constants, and requirements, making it useful for developers working with this specific API. However, the content is limited to just this enumeration and lacks broader context or examples of how it integrates with the Remote Desktop ActiveX Control. The page also requires authorization, which may hinder accessibility.", "relevant": true, "score": 6, "scraped_at": "2025-06-17 16:29:53.684055", "title": "RemoteWindowDisplayedAttribute enumeration - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/remotewindowdisplayedattribute", "word_count": 128}, {"ai_response": "SCORE: 8  \nREASONING: The page provides detailed technical content about the `IMsRdpClient8::Reconnect` method, including syntax, parameters, return values, and requirements. It is well-structured with clear headings and includes API references, making it useful for developers working with remote desktop ActiveX controls. However, the content is somewhat narrow in scope, focusing only on the `Reconnect` method rather than broader usage of the control. The page also requires authorization, which may limit accessibility.  \n\nRELEVANT: yes", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nIMsRdpClient8::Reconnect method\n\n\n\n\n\n\t\t\t\t2020-12-11\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nReconnects to the remote session with the new desktop width and height. The control must already be connected to a remote session for this method to succeed.\nSyntax\nHRESULT Reconnect(\n  [in]          ULONG                  ulWidth,\n  [in]          ULONG                  ulHeight,\n  [out, retval] ControlReconnectStatus *pReconnectStatus\n);\n\nParameters\n \nulWidth [in]\n \nType: ULONG\nThe new desktop width, in pixels. See the DesktopWidth property for value restrictions.\n \nulHeight [in]\n \nType: ULONG\nThe new desktop height, in pixels. See the DesktopHeight property for value restrictions.\n \npReconnectStatus [out, retval]\n \nType: ControlReconnectStatus*\nA pointer to a ControlReconnectStatus variable that receives the status of the reconnect operation.\n \nReturn value\nType: HRESULT\nIf this method succeeds, it returns S_OK. Otherwise, it returns an HRESULT error code.\nRequirements\n\n\n\nRequirement\nValue\n\n\n\n\nMinimum supported client\nWindows 8\n\n\nMinimum supported server\nWindows Server 2012\n\n\nType library\n MsTscAx.dll \n\n\nDLL\n MsTscAx.dll \n\n\nIID\nIID_IMsRdpClient8 is defined as 4247E044-9271-43A9-BC49-E2AD9E855D62\n\n\n\nSee also\n \nIMsRdpClient8\n \nIMsRdpClient9\n \nIMsRdpClient10\n \nControlReconnectStatus\n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "The page provides detailed technical content about the `IMsRdpClient8::Reconnect` method, including syntax, parameters, return values, and requirements. It is well-structured with clear headings and includes API references, making it useful for developers working with remote desktop ActiveX controls. However, the content is somewhat narrow in scope, focusing only on the `Reconnect` method rather than broader usage of the control. The page also requires authorization, which may limit accessibility.", "relevant": true, "score": 8, "scraped_at": "2025-06-17 16:29:59.939570", "title": "IMsRdpClient8 Reconnect method - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/imsrdpclient8-reconnect", "word_count": 223}, {"ai_response": "**SCORE: 7**  \n**REASONING:**  \nThe page provides technical content related to the `ControlCloseStatus` enumeration, which is part of the Windows Remote Desktop ActiveX Control API. It includes structured headings, syntax, constants, and requirements, making it useful for developers working with this API. However, the content is somewhat limited in depth—it explains the enumeration values but lacks broader context or practical examples of how to use them in real-world scenarios. The page is also behind an authorization wall, which may limit accessibility.  \n\n**RELEVANT: yes**  \nThe content directly relates to the focus topic (Remote Desktop ActiveX Control in Windows) by documenting a specific enumeration used in its API. While it doesn’t cover broader usage or workflows, it is a relevant reference for developers needing details about `ControlCloseStatus`.", "content": "Table of contents \n\n\n\nExit focus mode\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAsk <PERSON>rn\n\n\n\n\n\nAsk Learn\n\n\n\n\n\n\n\n\n\n\n\n\nTable of contents\n\n\n\n\n\nRead in English\n\n\n\n\n\nAdd\n\n\n\n\n\nAdd to plan\n\n\n\n\n\nEdit\n\n\nShare via\n\n\n\n\nFacebook\n\n\n\n\n\nx.com\n\n\n\n\n\nLinkedIn\n\n\n\n\n\nEmail\n\n\n\n\n\n\nPrint\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\n\n\n\nControlCloseStatus enumeration\n\n\n\n\n\n\t\t\t\t2020-12-11\n\t\t\t\n\n\n\n\n\n\n\n\nFeedback\n\n\n\n\n\n\t\t\t\tIn this article\n\t\t\t\n\nIndicates whether the application containing the control can close the control immediately.\nSyntax\nenum ControlCloseStatus {\n  controlCloseCanProceed     = 0x0000, \n  controlCloseWaitForEvents  = 0x0001 \n\n};\n\nConstants\n \ncontrolCloseCanProceed\n \nContainer can go ahead with close immediately. This can happen if the control is not connected.\n \ncontrolCloseWaitForEvents\n \nContainer should wait for events IMsTscAxEvents::OnDisconnected or IMsTscAxEvents::OnConfirmClose.\n \nRequirements\n\n\n\nRequirement\nValue\n\n\n\n\nMinimum supported client\nWindows 8.1\n\n\nMinimum supported server\nWindows Server 2012 R2\n\n\nType library\n MsTscAx.dll \n\n\n\nSee also\n \nIMsRdpClient::RequestClose\n \nIMsTscAxEvents::OnConfirmClose\n \nIMsTscAxEvents::OnDisconnected\n \n\n\n\n\n\n\n\nFeedback\n\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\n\n\n\n\n\nYes\n\n\n\n\n\nNo\n\n\n\n\n\nProvide product feedback\n\n|\n\nGet help at Microsoft Q&A\n\n\n\n\n\n\n\n\n\t\t\t\tAdditional resources", "reasoning": "AI analysis completed", "relevant": true, "score": 5, "scraped_at": "2025-06-17 16:30:08.586783", "title": "ControlCloseStatus enumeration - Win32 apps | Microsoft Learn", "url": "https://learn.microsoft.com/en-us/windows/win32/termserv/controlclosestatus", "word_count": 152}]}