# ControlCloseStatus enumeration - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/controlclosestatus

**Quality Score:** 5/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** AI analysis completed

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



ControlCloseStatus enumeration





				2020-12-11
			








Feedback





				In this article
			

Indicates whether the application containing the control can close the control immediately.
Syntax
enum ControlCloseStatus {
  controlCloseCanProceed     = 0x0000, 
  controlCloseWaitForEvents  = 0x0001 

};

Constants
 
controlCloseCanProceed
 
Container can go ahead with close immediately. This can happen if the control is not connected.
 
controlCloseWaitForEvents
 
Container should wait for events IMsTscAxEvents::OnDisconnected or IMsTscAxEvents::OnConfirmClose.
 
Requirements



Requirement
Value




Minimum supported client
Windows 8.1


Minimum supported server
Windows Server 2012 R2


Type library
 MsTscAx.dll 



See also
 
IMsRdpClient::RequestClose
 
IMsTscAxEvents::OnConfirmClose
 
IMsTscAxEvents::OnDisconnected
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

