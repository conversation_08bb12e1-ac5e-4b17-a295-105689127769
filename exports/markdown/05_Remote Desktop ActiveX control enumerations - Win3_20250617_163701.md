# Remote Desktop ActiveX control enumerations - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control-enumerations

**Quality Score:** 8/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides detailed technical content about Remote Desktop ActiveX control enumerations, including structured headings and clear descriptions of each enumeration. It is highly relevant to the focus topic, offering actionable information for developers working with the Remote Desktop ActiveX control. However, the lack of code examples or API usage snippets slightly limits its practical utility. The page is also behind an authorization wall, which may hinder accessibility for some users.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



Remote Desktop ActiveX control enumerations





				2020-08-19
			








Feedback





				In this article
			

The following enumerations are used with the Remote Desktop ActiveX control.
In this section
 
ControlCloseStatus
 
Indicates whether the application containing the control can close the control immediately.
 
ControlReconnectStatus
 
Specifies the result of the IMsRdpClient8::Reconnect method.
 
ClientSpec
 
Used with the ClientProtocolSpec property to specify the remote desktop protocol used between the client and the server.
 
ExtendedDisconnectReasonCode
 
Defines extended information about the control's reason for disconnection.
 
RedirectDeviceType
 
Used to specify the type of a device.
 
RemoteActionType
 
The action to send to the remote session.
 
RemoteSessionActionType
 
Used to specify the type of remote action.
 
RemoteWindowDisplayedAttribute
 
Used with the method to specify information about the event.
 
SnapshotEncodingType
 
The type of encoding used for a Remote Desktop Protocol (RDP) app container client snapshot.
 
SnapshotFormatType
 
The data format used for a Remote Desktop Protocol (RDP) app container client snapshot.
 
 
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

