# Sample webpage included with the Remote Desktop ActiveX control - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/sample-web-page-included-with-the-remote-desktop-activex-control

**Quality Score:** 6/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides technical content related to the Remote Desktop ActiveX control, including installation requirements, usage, and how the sample webpage (Default.htm) functions. It has structured headings and mentions the role of the control in initiating remote desktop sessions. However, it lacks detailed code examples or API references, and the content is somewhat superficial. The page is also partially restricted, which limits accessibility.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



Sample webpage included with the Remote Desktop ActiveX control





				2019-08-23
			








Feedback





				In this article
			

When you install the Remote Desktop ActiveX control of Remote Desktop Web Connection, a sample webpage (Default.htm) is copied to the directory where Remote Desktop Web Connection is installed. This page can be run as-is or customized to fit the needs of a user or an organization.
The sample webpage must be installed on a web server running Windows Server and Internet Information Server 4.0 or later. Also, the browser on the client computer should be Internet Explorer version 4.0 or later. For more information, see Requirements for Remote Desktop Web Connection.
Default.htm is a default logon webpage that collects connection information from the user. The logon page contains one required field into which the user enters the name of the Remote Desktop Session Host (RD Session Host) server or remote computer to connect to. The logon page also includes optional fields for screen size and user logon information, including user name and domain.
After collecting the user data, Default.htm passes it to the Remote Desktop ActiveX control (Msrdp.ocx) to initiate a remote desktop session. The steps involved in initiating the session are the following:

If necessary, Internet Explorer downloads the .cab file and installs it on the user's computer.

The user enters the fully qualified domain name or IP address of the remote computer in the appropriate field.

The user can optionally specify a screen size for the connection. If the user does not specify a screen size, the session opens in full-screen mode if the user is in a trusted URL security zone. Otherwise, the session opens in window mode.
The user can optionally supply automatic logon information (user name, domain) for the remote session.


When the user clicks the Connect button, Default.htm passes the user-supplied data as parameters to the Remote Desktop ActiveX control.

The remote desktop opens in the browser window or in full-screen mode, as specified by the user.


When Default.htm opens for the first time using Internet Explorer, a dialog box appears giving the user the option to download the Remote Desktop ActiveX control (Msrdp.ocx). The dialog box appears under the following conditions:

The computer that accessed the webpage does not have a full installation of the Remote Desktop Web Connection client (or the Remote Desktop Services Advanced Client) with web support.
The installed version of the .cab file on the client computer is older than the version on the Default.htm webpage.

The size of the remote desktop session that appears in the browser window is the size specified in the Default.htm webpage. To change the screen size, the user must disconnect from the session, return to the Default.htm webpage and edit the connection information. If no screen size was specified, the session opens in the browser window in the default full-screen mode. At this resolution the remote desktop expands to match the full dimensions of the user's screen and the Internet Explorer browser toolbars and window frames do not display. As with the full Remote Desktop Connection client, the user can toggle between full-screen and window mode by pressing the full-screen mode shortcut key combination (CTRL+ALT+BREAK).
For security reasons, some options that are available in the Remote Desktop Services Connection Manager are restricted in the Remote Desktop ActiveX control to certain Internet Explorer URL security zones. Refer to Providing for RDP Client Security for more information about these options and about specifying a program to run upon connection.
 
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

