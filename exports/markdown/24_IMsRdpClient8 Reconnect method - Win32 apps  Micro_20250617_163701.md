# IMsRdpClient8 Reconnect method - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/imsrdpclient8-reconnect

**Quality Score:** 8/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides detailed technical content about the `IMsRdpClient8::Reconnect` method, including syntax, parameters, return values, and requirements. It is well-structured with clear headings and includes API references, making it useful for developers working with remote desktop ActiveX controls. However, the content is somewhat narrow in scope, focusing only on the `Reconnect` method rather than broader usage of the control. The page also requires authorization, which may limit accessibility.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



IMsRdpClient8::Reconnect method





				2020-12-11
			








Feedback





				In this article
			

Reconnects to the remote session with the new desktop width and height. The control must already be connected to a remote session for this method to succeed.
Syntax
HRESULT Reconnect(
  [in]          ULONG                  ulWidth,
  [in]          ULONG                  ulHeight,
  [out, retval] ControlReconnectStatus *pReconnectStatus
);

Parameters
 
ulWidth [in]
 
Type: ULONG
The new desktop width, in pixels. See the DesktopWidth property for value restrictions.
 
ulHeight [in]
 
Type: ULONG
The new desktop height, in pixels. See the DesktopHeight property for value restrictions.
 
pReconnectStatus [out, retval]
 
Type: ControlReconnectStatus*
A pointer to a ControlReconnectStatus variable that receives the status of the reconnect operation.
 
Return value
Type: HRESULT
If this method succeeds, it returns S_OK. Otherwise, it returns an HRESULT error code.
Requirements



Requirement
Value




Minimum supported client
Windows 8


Minimum supported server
Windows Server 2012


Type library
 MsTscAx.dll 


DLL
 MsTscAx.dll 


IID
IID_IMsRdpClient8 is defined as 4247E044-9271-43A9-BC49-E2AD9E855D62



See also
 
IMsRdpClient8
 
IMsRdpClient9
 
IMsRdpClient10
 
ControlReconnectStatus
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

