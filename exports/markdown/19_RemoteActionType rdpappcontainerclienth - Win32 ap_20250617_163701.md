# RemoteActionType (rdpappcontainerclient.h) - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/api/rdpappcontainerclient/ne-rdpappcontainerclient-remoteactiontype

**Quality Score:** 7/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides technical content with structured headings, API references, and clear enumeration details for `RemoteActionType`. It is accurate and useful for developers working with remote desktop functionalities in Windows. However, it lacks code examples or deeper explanations of how these actions integrate with the remote-desktop-activex-control, which limits its depth. The relevance to the focus topic is partial, as it covers a specific enumeration but not the broader control.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



RemoteActionType enumeration (rdpappcontainerclient.h)





				2024-02-22
			








Feedback





				In this article
			

The action to send to the remote session.
Syntax
typedef enum __MIDL_IRemoteDesktopClientActions_0001 {
  RemoteActionCharms = 0,
  RemoteActionAppbar = 1,
  RemoteActionSnap = 2,
  RemoteActionStartScreen = 3,
  RemoteActionAppSwitch = 4
} RemoteActionType;

Constants



 




RemoteActionCharmsValue: 0Displays the charms in the remote session.


RemoteActionAppbarValue: 1Displays the app bar in the remote session.


RemoteActionSnapValue: 2Docks the application in the remote session.


RemoteActionStartScreenValue: 3Causes the start screen to be displayed in the remote session.


RemoteActionAppSwitchValue: 4Causes the application switch window to be displayed in the remote session. This is the same as the user pressing Alt+Tab.



Requirements



Requirement
Value




Minimum supported client
Windows 8


Minimum supported server
Windows Server 2012


Header
rdpappcontainerclient.h



See also
ExecuteRemoteAction







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

