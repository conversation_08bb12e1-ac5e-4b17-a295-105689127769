# Remote Desktop ActiveX control classes - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-activex-control-classes

**Quality Score:** 6/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides a structured list of Remote Desktop ActiveX control classes with version details, which is technically relevant. However, it lacks depth in explanations, code examples, or API usage details, reducing its usefulness for developers. The authorization barrier also limits accessibility.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



Remote Desktop ActiveX control classes





				2019-08-23
			








Feedback





				In this article
			

The following classes implement the Remote Desktop ActiveX control.
In this section
 
MsRdpClient
 
Microsoft RDP Client Control (redistributable) - version 2
 
MsRdpClientNotSafeForScripting
 
Microsoft RDP Client Control - version 2
 
MsRdpClient2
 
Microsoft RDP Client Control (redistributable) - version 3
 
MsRdpClient2a
 
Microsoft RDP Client Control (redistributable) - version 3a
 
MsRdpClient2NotSafeForScripting
 
Microsoft RDP Client Control - version 3
 
MsRdpClient3
 
Microsoft RDP Client Control (redistributable) - version 4
 
MsRdpClient3a
 
Microsoft RDP Client Control (redistributable) - version 4a
 
MsRdpClient3NotSafeForScripting
 
Microsoft RDP Client Control - version 4
 
MsRdpClient4
 
Microsoft RDP Client Control (redistributable) - version 5
 
MsRdpClient4a
 
Microsoft RDP Client Control (redistributable) - version 5a
 
MsRdpClient4NotSafeForScripting
 
Microsoft RDP Client Control - version 5
 
MsRdpClient5
 
Microsoft RDP Client Control (redistributable) - version 6
 
MsRdpClient5NotSafeForScripting
 
Microsoft RDP Client Control - version 6
 
MsRdpClient6
 
Microsoft RDP Client Control (redistributable) - version 7
 
MsRdpClient6NotSafeForScripting
 
Microsoft RDP Client Control - version 7
 
MsRdpClient7
 
Microsoft RDP Client Control (redistributable) - version 8
 
MsRdpClient7NotSafeForScripting
 
"Microsoft RDP Client Control - version 8
 
MsRdpClient8
 
Microsoft RDP Client Control (redistributable) - version 9
 
MsRdpClient8NotSafeForScripting
 
Microsoft RDP Client Control - version 9
 
MsRdpClient9
 
Microsoft RDP Client Control (redistributable) - version 10
 
MsRdpClient9NotSafeForScripting
 
Microsoft RDP Client Control - version 10
 
MsRdpClient10
 
Microsoft RDP Client Control (redistributable) - version 11
 
MsRdpClient10NotSafeForScripting
 
Microsoft RDP Client Control - version 11
 
MsRdpClient11
 
Microsoft RDP Client Control (redistributable) - version 12
 
MsRdpClient11NotSafeForScripting
 
Microsoft RDP Client Control - version 12
 
MsRdpClient12
 
Microsoft RDP Client Control (redistributable) - version 13
 
MsRdpClient11NotSafeForScripting
 
Microsoft RDP Client Control - version 13

MsTscAx
 
Microsoft Terminal Services Client Control (redistributable) - version 1
 
MsTscAxNotSafeForScripting
 
Microsoft Terminal Services Client Control - version 1
 
RemoteDesktopClient
 
Implements the Microsoft Windows Store App Remote Desktop Client Control - version 1
 
 
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

