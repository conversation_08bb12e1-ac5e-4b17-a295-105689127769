# Embedding the Remote Desktop ActiveX control in a webpage - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/embedding-the-remote-desktop-activex-control-in-a-web-page

**Quality Score:** 8/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides technical content related to embedding the Remote Desktop ActiveX control in a webpage, including a code example and styling details. It is structured with headings and metadata, indicating a formal Microsoft documentation standard. However, the preview suggests potential access restrictions, which could limit usability. The content is accurate and relevant to developers working with Windows Remote Desktop integration.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



Embedding the Remote Desktop ActiveX control in a webpage





				2020-02-20
			








Feedback





				In this article
			

You can embed the Remote Desktop ActiveX control in a web page by using code that is similar to the following.
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
    <title id="TSWEBTITLE">Remote Desktop Web Connection</title>
    <meta content="text/html; charset=windows-1252" http-equiv="Content-Type">
    <style type="text/css" media="screen">
        P
        {
            font-family: Verdana, Arial, Helvetica;
            color: #000000;
            font-size: 65%;
        }
        H1
        {
            margin-top: 0em;
            font-family: verdana, arial, helvetica;
            font-size: 100%;
            font-weight: bold;
        }
        P.indent
        {
            line-height: 1.25em;
            margin: 0.5em 1em 0.2em 3em;
        }
        .button
        {
            border-bottom-color: #6699ff;
            background-color: #ffffff;
            margin-top: 6pt;
            border-top-color: #6699ff;
            font-family: Verdana, Helvetica, Arial, Sans-Serif;
            color: #000000;
            border-right-color: #6699ff;
            margin-left: 0.5em;
            font-size: 70%;
            border-left-color: #6699ff;
            font-weight: normal;
        }
        .topspace
        {
            margin-top: 0.08em;
        }
    </style>
    <meta name="GENERATOR" content="MSHTML 8.00.7600.16625">
</head>
<body bgcolor="#ffffff">
    <script type="text/vbscript" language="vbscript">
<!--
const L_FullScreenWarn1_Text = "Your current security settings do not allow automatically switching to fullscreen mode."
const L_FullScreenWarn2_Text = "You can use ctrl-alt-pause to toggle your remote desktop session to fullscreen mode"
const L_FullScreenTitle_Text = "Remote Desktop Web Connection "
const L_ErrMsg_Text         = "Error connecting to remote computer: "
const L_ClientNotSupportedWarning_Text = "Remote Desktop 6.0 does not support CredSSP over TSWeb."

' error messages
const L_RemoteDesktopCaption_ErrorMessage =  "Remote Desktop Connection"
const L_InvalidServerName_ErrorMessage = "An invalid server name was specified."

sub window_onload()
   if not autoConnect() then
       Document.all.editServer.Focus
   end if
end sub

function autoConnect()
    Dim sServer
    Dim iFS, iAutoConnect

    sServer = getQS ("Server")
    iAutoConnect = getQS ("AutoConnect")
    iFS = getQS ("FS")

    if NOT IsNumeric ( iFS ) then
        iFS = 0
    else
        iFS = CInt ( iFS )
    end if

    if iAutoConnect <> 1 then
        autoConnect = false
        exit function
    else
        if iFS < 0 or iFS >= Document.all.comboResolution.options.length then
            iFS = 0
        end if

        if IsNull ( sServer ) or sServer = "" then
            sServer = window.location.hostname
        end if

        Document.all.comboResolution.selectedIndex    = iFS
        Document.all.Server.value = sServer

        btnConnect ()

        autoConnect = true
    end if

end function

function getQS ( sKey )
    Dim iKeyPos, iDelimPos, iEndPos
    Dim sURL, sRetVal
    iKeyPos = iDelimPos = iEndPos = 0
    sURL = window.location.href

    if sKey = "" Or Len(sKey) < 1 then
        getQS = ""
        exit function
    end if

    iKeyPos = InStr ( 1, sURL, sKey )

    if iKeyPos = 0 then
        sRetVal = ""
        exit function
    end if

    iDelimPos = InStr ( iKeyPos, sURL, "=" )
    iEndPos = InStr ( iDelimPos, sURL, "&" )

    if iEndPos = 0 then
        sRetVal = Mid ( sURL, iDelimPos + 1 )
    else
        sRetVal = Mid ( sURL, iDelimPos + 1, iEndPos - iDelimPos - 1 )
    end if

    getQS = sRetVal
end function

sub checkClick
    if Document.all.Check1.Checked then
        Document.all.tableLogonInfo.style.display = ""
        Document.all.editUserName.Disabled = false
        Document.all.editDomain.Disabled = false
    else
        Document.all.tableLogonInfo.style.display = "none"
        Document.all.editUserName.Disabled = true
        Document.all.editDomain.Disabled = true
    end if
end sub

sub OnControlLoadError
    Document.all.cerrmsg.style.display = "block"
    Document.all.csafemsg.style.display = "none"
    Document.all.Server.disabled = TRUE
    Document.all.comboResolution.disabled = TRUE
end sub

sub OnControlLoad
   set Control = Document.getElementById("MsRdpClient")
   if Not Control is Nothing then
      if Control.readyState = 4 then
 

