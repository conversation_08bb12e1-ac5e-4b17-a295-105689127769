# Using the Remote Desktop ActiveX control - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/using-remote-desktop-web-connection

**Quality Score:** 8/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides detailed technical content about the Remote Desktop ActiveX control, including CLSIDs for different Windows versions, scriptable vs. nonscriptable controls, and usage contexts. The structured headings and clear organization enhance readability. However, the lack of code examples or step-by-step implementation guidance slightly limits its actionable value. The content is highly relevant to the focus topic but could benefit from more practical examples or troubleshooting tips.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



Using the Remote Desktop ActiveX control





				2023-09-26
			








Feedback





				In this article
			

The following topics contain information about how you can use the Remote Desktop ActiveX control to customize the Remote Desktop Services user experience.
The Remote Desktop ActiveX control is available in the following forms:

The scriptable controlâimplements interfaces that are safe for scripting. This form of the control can be used by web clients or scripting (such as VBScript) hosts.
The nonscriptable controlâoffers additional functionality that is not safe for scripting. This form of the control can be used from native or managed code, but this form cannot be used by web clients or scripting-only hosts.

The following list contains the CLSIDs for the different controls for different operating system versions. Each of the CLSIDs is compatible with later system versions. For example, the CLSID for the scriptable control on Windows Vista will work on later system versions, such as Windows 7.



System version
Scriptable control CLSID
Nonscriptable control CLSID




Windows 8.1, Windows Server 2012 R2
301B94BA-5D25-4A12-BFFE-3B6E7A616585
8B918B82-7985-4C24-89DF-C33AD2BBFBCD


Windows 8, Windows Server 2012
5F681803-2900-4C43-A1CC-CF405404A676
A3BC03A0-041D-42E3-AD22-882B7865C9C5


Windows 7, Windows Server 2008
A9D7038D-B5ED-472E-9C47-94BEA90A5910
54D38BF7-B1EF-4479-9674-1BD6EA465258


Windows Vista with Service Pack 1 (SP1)
7390F3D8-0439-4C05-91E3-CF5CB290C3D0
D2EA46A7-C2BF-426B-AF24-E19C44456399


Windows Vista
4EB89FF4-7F78-4A0F-8B8D-2BF02E94E4B2
4EB2F086-C818-447E-B32C-C51CE2B30D31



 
In this section
 
Embedding the Remote Desktop ActiveX control in a webpage
 
Example that demonstrates the use of the scriptable interfaces.
 
Implementing scriptable virtual channels by using Remote Desktop Web Connection
 
Code examples that show the steps for implementing scriptable virtual channels with Remote Desktop Web Connection.
 
Using the Remote Desktop ActiveX control with virtual channels
 
If you have enabled a virtual channels application in your Remote Desktop Services deployment, you can make this application available to client computers.
 
Sample webpage included with the Remote Desktop ActiveX control
 
A sample webpage (Default.htm) is in the directory where Remote Desktop Web Connection is installed.
 
Providing for RDP client security
 
Some properties of the Remote Desktop ActiveX control object are restricted to specific Internet Explorer URL security zones.
 
Disabling Remote Desktop Services features
 
For enhanced security, you might choose to disable Remote Desktop Services features.
 
Calling Remote Desktop ActiveX control non-scriptable interfaces
 
Provides guidance for calling non-scriptable Remote Desktop ActiveX controls from native and managed code.


For more information about the sample webpage that is included with the installation of the Remote Desktop ActiveX control, see Sample webpage included with the Remote Desktop ActiveX control.
 
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

