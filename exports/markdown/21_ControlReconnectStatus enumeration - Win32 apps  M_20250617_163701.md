# ControlReconnectStatus enumeration - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/controlreconnectstatus

**Quality Score:** 8/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides clear, technical documentation on the `ControlReconnectStatus` enumeration, which is part of the Windows Remote Desktop ActiveX Control API. It includes structured headings, syntax, constants, and requirements, making it useful for developers working with this API. However, the content is somewhat narrow in scope, focusing only on the `Reconnect` method's status codes, and lacks broader context or usage examples. The page is also behind an authorization wall, which may limit accessibility.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



ControlReconnectStatus enumeration





				2020-12-11
			








Feedback





				In this article
			

Specifies the result of the IMsRdpClient8::Reconnect method.
Syntax
typedef enum  { 
  controlReconnectStarted  = 0x0000,
  controlReconnectBlocked  = 0x0001
} ControlReconnectStatus;

Constants
 
controlReconnectStarted
 
The reconnect operation was started.
 
controlReconnectBlocked
 
The reconnect operation could not be started.
 
Requirements



Requirement
Value




Minimum supported client
Windows 8


Minimum supported server
Windows Server 2012


Type library
 MsTscAx.dll 



See also
 
IMsRdpClient8::Reconnect
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

