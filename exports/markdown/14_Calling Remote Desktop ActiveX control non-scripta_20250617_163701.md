# Calling Remote Desktop ActiveX control non-scriptable interfaces - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/calling-non-scriptable-interfaces

**Quality Score:** 7/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides technical content related to the Remote Desktop ActiveX control, including code examples for both native and managed code, which is useful for developers. However, the content is limited in depth, focusing only on calling non-scriptable interfaces without broader context or advanced use cases. The page also requires authorization, which may hinder accessibility. The relevance to the focus topic is high, but the lack of comprehensive coverage and potential access issues lower its overall quality score.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



Calling non-scriptable interfaces from native code





				2023-09-26
			








Feedback





				In this article
			

The Windows SDK does not provide header files for the Remote Desktop ActiveX control. This article provides guidance for calling the non-scriptable interfaces it exports from both native and managed code.
Call non-scriptable interfaces from native code
To call non-scriptable interfaces from native code, use the #import directive supported by the Microsoft C++ (MSVC) compiler.
#import "libid:8C11EFA1-92C3-11D1-BC1E-00C04FA31489"
#include "mstscax.tlh"

The `#import`` directive will generate a header file named "mstscax.tlh" for the Remote Desktop ActiveX control type library. The generated header file can then be included. For this snippet to compile, the developer will need to ensure that the folder where the header file is generated is part of the directories searched for include files by the compiler.
Call scriptable interfaces from managed code
To call the interfaces of the Remote Desktop ActiveX control from managed code, use the ActiveX Control Importer tool to generate a .NET assembly.
aximp.exe c:\windows\system32\mstscax.dll

The generated assembly can then be referenced by a managed code project in Visual Studio.
Related topics
Using the Remote Desktop ActiveX control
 
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

