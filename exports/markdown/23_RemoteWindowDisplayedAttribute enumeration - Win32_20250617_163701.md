# RemoteWindowDisplayedAttribute enumeration - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/remotewindowdisplayedattribute

**Quality Score:** 6/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides technical content related to the `RemoteWindowDisplayedAttribute` enumeration, which is part of the Windows Remote Desktop ActiveX Control API. It includes structured headings, syntax, constants, and requirements, making it useful for developers working with this specific API. However, the content is limited to just this enumeration and lacks broader context or examples of how it integrates with the Remote Desktop ActiveX Control. The page also requires authorization, which may hinder accessibility.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



RemoteWindowDisplayedAttribute enumeration





				2020-12-11
			








Feedback





				In this article
			

Used with the method to specify information about the event.
Syntax
typedef enum  { 
  remoteAppWindowNone          = 0,
  remoteAppWindowDisplayed     = 1,
  remoteAppShellIconDisplayed  = 2
} RemoteWindowDisplayedAttribute;

Constants
 
remoteAppWindowNone
  
remoteAppWindowDisplayed
  
remoteAppShellIconDisplayed
  
Requirements



Requirement
Value




Minimum supported client
Windows 8


Minimum supported server
Windows Server 2012


Type library
 MsTscAx.dll 



See also
 
OnRemoteWindowDisplayed
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

