# Implementing scriptable virtual channels by using Remote Desktop Web Connection - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/implementing-scriptable-virtual-channels-using-remote-desktop-web-connection

**Quality Score:** 5/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** AI analysis completed

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



Implementing scriptable virtual channels by using Remote Desktop Web Connection





				2019-08-23
			








Feedback





				In this article
			

The following procedure and code examples show the steps for implementing scriptable virtual channels with Remote Desktop Web Connection. The examples were written in Visual Basic Scripting Edition and assume that the Remote Desktop ActiveX control is named "MsRdpClient".
To create and deploy scriptable virtual channels

Deploy the server side of the application and make sure it is running on the Remote Desktop Session Host (RD Session Host) server. For information about deploying virtual channels applications on the server, see Virtual Channel Server Application.

In your client script, call IMsTscAx::CreateVirtualChannels, passing a string that contains a comma-separated list of virtual channel names.
MsRdpClient.CreateVirtualChannels("mychan1,mychan2")

For information about virtual channel naming restrictions, see Virtual Channel Client Registration.

Call IMsTscAx::Connect to create your Remote Desktop Services connection.
MsRdpClient.connect


Use the IMsTscAx::SendOnVirtualChannel method to send data to the server, passing a string that contains the virtual channel name and a second string that contains the data to be passed.
MsRdpClient.SendOnVirtualChannel("mychan1","hello from the client")


Receive data from the server on the IMsTscAxEvents::OnChannelReceivedData event.
Sub MsRdpClient.OnChannelReceivedData(chanName,data)
Msgbox("received data:" &data& "on virtual channel:" &chanName)
End sub



 
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

