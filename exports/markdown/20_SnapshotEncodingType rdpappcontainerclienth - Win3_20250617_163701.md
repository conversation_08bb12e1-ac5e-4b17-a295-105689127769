# SnapshotEncodingType (rdpappcontainerclient.h) - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/api/rdpappcontainerclient/ne-rdpappcontainerclient-snapshotencodingtype

**Quality Score:** 5/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** AI analysis completed

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



SnapshotEncodingType enumeration (rdpappcontainerclient.h)





				2024-02-22
			








Feedback





				In this article
			

The type of encoding used for a Remote Desktop Protocol (RDP) app container client snapshot.
Syntax
typedef enum __MIDL_IRemoteDesktopClientActions_0002 {
  SnapshotEncodingDataUri = 0
} SnapshotEncodingType;

Constants



 




SnapshotEncodingDataUriValue: 0The snapshot will be taken and a data URI that contains the snapshot will be returned.



Requirements



Requirement
Value




Minimum supported client
Windows 8


Minimum supported server
Windows Server 2012


Header
rdpappcontainerclient.h



See also
GetSnapshot







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

