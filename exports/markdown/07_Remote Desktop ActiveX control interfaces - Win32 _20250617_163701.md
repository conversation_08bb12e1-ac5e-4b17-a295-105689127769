# Remote Desktop ActiveX control interfaces - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/remote-desktop-web-connection-reference

**Quality Score:** 5/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** AI analysis completed

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



Remote Desktop ActiveX control interfaces





				2020-08-19
			








Feedback





				In this article
			

The Remote Desktop ActiveX control supports the following scriptable and nonscriptable interfaces, which are grouped according to the inheritance hierarchy.
In this section
 
IMsRdpClient
 
Provides the methods and properties needed to configure and use the client control. Derives from the IMsTscAx interface.
 
IMsRdpClient2
 
Provides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient interface.
 
IMsRdpClient3
 
Provides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient2 interface.
 
IMsRdpClient4
 
Provides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient3 interface.
 
IMsRdpClient5
 
Provides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient4 interface.
 
IMsRdpClient6
 
Provides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient5 interface.
 
IMsRdpClient7
 
Provides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient6 interface.
 
IMsRdpClient8
 
Provides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient7 interface.
 
IMsRdpClient9
 
Provides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient8 interface.
 
IMsRdpClient10
 
Provides the methods and properties needed to configure and use the client control. Derives from the IMsRdpClient9 interface.
 
IMsRdpClientAdvancedSettings
 
Manages advanced client settings. Derives from the IMsTscAdvancedSettings interface.
 
IMsRdpClientAdvancedSettings2
 
Manages advanced client settings. Derives from the IMsRdpClientAdvancedSettings interface.
 
IMsRdpClientAdvancedSettings3
 
Manages advanced client settings. Derives from the IMsRdpClientAdvancedSettings2 interface.
 
IMsRdpClientAdvancedSettings4
 
Manages advanced client settings. Derives from the IMsRdpClientAdvancedSettings3 interface.
 
IMsRdpClientAdvancedSettings5
 
Manages advanced client settings. Derives from the IMsRdpClientAdvancedSettings4 interface.
 
IMsRdpClientAdvancedSettings6
 
Exposes properties that manage advanced ActiveX control settings.
 
IMsRdpClientAdvancedSettings7
 
Exposes methods and properties that manage advanced settings of the ActiveX control.
 
IMsRdpClientAdvancedSettings8
 
Exposes methods and properties that manage advanced settings of the Remote Desktop ActiveX control.
 
IMsRdpClientNonScriptable
 
Provides access to the nonscriptable properties of a client's remote session on the Remote Desktop ActiveX control. Derives from the IMsTscNonScriptable interface.
 
IMsRdpClientNonScriptable2
 
Provides access to the nonscriptable properties of a client's remote session on the Remote Desktop ActiveX control. Derives from the IMsRdpClientNonScriptable interface.
 
IMsRdpClientNonScriptable3
 
Provides access to the nonscriptable properties of a client's remote session on the Remote Desktop ActiveX control. Derives from the IMsRdpClientNonScriptable2 interface.
 
IMsRdpClientNonScriptable4
 
Provides access to the nonscriptable properties of a client's remote session on the Remote Desktop ActiveX control. Derives from the IMsRdpClientNonScriptable3 interface.
 
IMsRdpClientNonScriptable5
 
Provides access to the nonscriptable properties of a client's remote session on the Remote Desktop ActiveX control. Derives from the IMsRdpClientNonScriptable4 interface.
 
IMsRdpClientSecuredSettings
 
Includes methods to retrieve and set properties of the Remote Desktop ActiveX control that are restricted to specific Internet Explorer URL security zones.
 
IMsRdpClientSecuredSettings2
 
Defines additional properties of the Remote Desktop ActiveX control that are restricted to specific Internet Explorer URL security zones.
 
IMsRdpClientShell
 
Remote Desktop Connection (RDC) client settings that are used to launch the client from Remote Desktop Web Access (RD Web Access) or from other web portals.
 
IMsRdpClientTransportSettings
 
Manages client transport settings for the Remote Desktop Gateway (RD Gateway) server.
 
IMsRdpClientTransportSettings2
 
Manages client transport settings for the RD Gateway server.
 
IMsRdpClientTransportSettings3
 
Defines additional properties for the RD Gatew

