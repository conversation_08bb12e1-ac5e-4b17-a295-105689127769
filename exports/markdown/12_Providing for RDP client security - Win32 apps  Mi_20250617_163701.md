# Providing for RDP client security - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/providing-for-rdp-client-security

**Quality Score:** 5/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** AI analysis completed

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



Providing for RDP client security





				2019-08-23
			








Feedback





				In this article
			

To allow clients to protect themselves from potentially untrustworthy servers, some properties of the Remote Desktop ActiveX control object are restricted to specific Internet Explorer URL security zones. This means that, when a user browsing the web accesses the page and the page is in a higher URL security zone than the computer they are browsing the web with, these properties are disabled. These restricted properties are accessed using the IMsTscSecuredSettings interface and the IMsRdpClientSecuredSettings interface, and are available in the following Internet Explorer URL security zones:

My computer
Local intranet
Trusted sites

They are disabled in these zones:

Internet
Restricted sites

If you call these restricted properties within your Remote Desktop Services web application, you should call IMsTscAx::get_SecuredSettings and IMsTscAx::get_SecuredSettingsEnabled to access the Secured Settings properties.
The restricted properties that the IMsTscSecuredSettings interface accesses are the following:

StartProgram. This property specifies the program that will be started upon connection.
WorkDir. This property specifies the working directory of the program specified in StartProgram.
FullScreen. This property specifies whether the state of the control upon connection will be in full-screen or window mode. If the value of this property is TRUE, the connection will be opened in full-screen mode. Although the use of the FullScreen property is restricted to the Internet Explorer URL security zones listed earlier, a user can always change to full-screen mode after connection by pressing the full-screen mode shortcut key combination (CTRL+ALT+BREAK).

The properties that the IMsRdpClientSecuredSettings interface accesses are the following:

AudioRedirectionMode. This property specifies whether to redirect sounds, or play sounds at the Remote Desktop Session Host (RD Session Host) server.
KeyboardHookMode. This property specifies how and when to apply Windows key combinations; for example, ALT+TAB.

The following script launches Microsoft Notepad.exe upon connection. Run this script before calling the IMsTscAx::Connect method.
if MsRdpClient.SecuredSettingsEnabled then
    MsRdpClient.SecuredSettings.StartProgram = "notepad.exe"
else
    msgbox "Cannot access secured setting (startprogram) in the current browser zone"
end if

 
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

