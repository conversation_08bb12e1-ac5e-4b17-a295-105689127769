# Disabling Remote Desktop Services features - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/disabling-terminal-services-features

**Quality Score:** 7/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides technical content related to disabling Remote Desktop Services features, including registry edits, which aligns with the focus topic of Remote Desktop ActiveX Control usage. It offers actionable information (registry keys and values) and is structured with headings. However, the content is somewhat limited in depth, lacking broader context or examples of how these changes affect the ActiveX Control. The authorization requirement to access parts of the page may also hinder usability.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



Disabling Remote Desktop Services features





				2020-12-01
			








Feedback





				In this article
			

For enhanced security, you might choose to disable Remote Desktop Services features such as clipboard redirection and printer redirection for clients that connect to Remote Desktop Session Host (RD Session Host) servers using the Remote Desktop ActiveX Control.
To disable Remote Desktop Services features

Edit the registry of the client computer and add the following keys:

HKEY_LOCAL_MACHINE\Software\Microsoft\Terminal Server\DisableClipboardRedirection
HKEY_LOCAL_MACHINE\Software\Microsoft\Terminal Server\DisableDriveRedirection
HKEY_LOCAL_MACHINE\Software\Microsoft\Terminal Server\DisablePrinterRedirection


Set the value of both keys to REG_DWORD 1.


 
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

