# IMsRdpClientAdvancedSettings8 ClientProtocolSpec property - Win32 apps | Microsoft Learn

**URL:** https://learn.microsoft.com/en-us/windows/win32/termserv/imsrdpclientadvancedsettings8-clientprotocolspec

**Quality Score:** 8/10

**Scraped:** 2025-06-17 16:37:01

**AI Assessment:** The page provides detailed technical content about the `IMsRdpClientAdvancedSettings8::ClientProtocolSpec` property, including syntax, property values, and requirements. It is well-structured with clear headings and includes API references, making it useful for developers working with remote desktop protocols in Windows. However, the accessibility issue (requiring authorization) could hinder usability for some users.

---

Table of contents 



Exit focus mode


























Ask Learn





Ask Learn












Table of contents





Read in English





Add





Add to plan





Edit


Share via




Facebook





x.com





LinkedIn





Email






Print
















Note


						Access to this page requires authorization. You can try signing in or changing directories.
					

						Access to this page requires authorization. You can try changing directories.
					



IMsRdpClientAdvancedSettings8::ClientProtocolSpec property





				2020-12-11
			








Feedback





				In this article
			

Specifies the remote desktop protocol used between the client and the server.
This property is read/write.
Syntax
HRESULT put_ClientProtocolSpec(
  [in]  ClientSpec ClientMode
);

HRESULT get_ClientProtocolSpec(
  [out] ClientSpec *pClientMode
);

Property value
A value of the ClientSpec enumeration that specifies the remote desktop protocol used between the client and the server.
Requirements



Requirement
Value




Minimum supported client
Windows 8


Minimum supported server
Windows Server 2012


Type library
 MsTscAx.dll 


DLL
 MsTscAx.dll 



See also
 
ClientSpec
 
IMsRdpClientAdvancedSettings8
 







Feedback


					Was this page helpful?
				





Yes





No





Provide product feedback

|

Get help at Microsoft Q&A








				Additional resources

