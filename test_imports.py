#!/usr/bin/env python3
"""
Test script to verify that critical IntelliCrawler imports work correctly
"""

import sys
import os

def test_imports():
    """Test critical imports"""
    print("Testing IntelliCrawler imports...")
    
    errors = []
    
    try:
        print("  ✓ Testing basic intellicrawler import...")
        import intellicrawler
        print(f"    IntelliCrawler version: {intellicrawler.__version__}")
    except ImportError as e:
        errors.append(f"Failed to import intellicrawler: {e}")
    
    try:
        print("  ✓ Testing DeepSeekAI (AIIntegration) import...")
        from intellicrawler import DeepSeekAI, AIIntegration
        print("    DeepSeekAI and AIIntegration classes imported successfully")
    except ImportError as e:
        errors.append(f"Failed to import DeepSeekAI/AIIntegration: {e}")
    
    try:
        print("  ✓ Testing Crawler import...")
        from intellicrawler import Crawler
        print("    Crawler class imported successfully")
    except ImportError as e:
        errors.append(f"Failed to import Crawler: {e}")
    
    try:
        print("  ✓ Testing utils imports...")
        from intellicrawler.utils.logger import get_logger
        from intellicrawler.utils.config import load_config
        print("    Utils modules imported successfully")
    except ImportError as e:
        errors.append(f"Failed to import utils: {e}")
    
    if errors:
        print("\n❌ Import errors found:")
        for error in errors:
            print(f"    • {error}")
        return False
    else:
        print("\n✅ All critical imports working correctly!")
        return True

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)