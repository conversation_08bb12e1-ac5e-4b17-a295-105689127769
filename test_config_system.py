#!/usr/bin/env python3
"""
Test script for IntelliCrawler configuration system

This script tests:
1. Environment variable loading and validation
2. Configuration file creation and loading
3. Error handling for missing/invalid variables
4. Graceful degradation when API keys are missing
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the IntelliCrawler module to the path
sys.path.insert(0, str(Path(__file__).parent))

def test_basic_config_loading():
    """Test basic configuration loading"""
    print("=" * 60)
    print("🔧 TESTING BASIC CONFIGURATION LOADING")
    print("=" * 60)
    
    try:
        from intellicrawler.utils.config import load_config, validate_environment_variables
        
        # Test basic config loading
        config = load_config()
        
        print(f"✅ Configuration loaded successfully")
        print(f"📋 Config keys: {list(config.keys())}")
        print(f"🔑 API key status: {'Set' if config.get('deepseek_api_key') else 'Not set'}")
        print(f"🌐 User agent: {config.get('user_agent', 'Not set')}")
        print(f"📊 Max pages default: {config.get('max_pages_default', 'Not set')}")
        print(f"🔍 Crawl depth default: {config.get('crawl_depth_default', 'Not set')}")
        
        # Test validation
        validation = validate_environment_variables()
        print(f"✅ Environment validation completed")
        print(f"📈 Validation status: {'PASSED' if validation.get('valid') else 'FAILED'}")
        print(f"❌ Errors: {len(validation.get('errors', []))}")
        print(f"⚠️  Warnings: {len(validation.get('warnings', []))}")
        print(f"ℹ️  Info messages: {len(validation.get('info', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration loading failed: {str(e)}")
        return False

def test_environment_variable_validation():
    """Test environment variable validation with various scenarios"""
    print("\n" + "=" * 60)
    print("🧪 TESTING ENVIRONMENT VARIABLE VALIDATION")
    print("=" * 60)
    
    # Test scenarios with different environment variable values
    test_scenarios = [
        {
            'name': 'Valid API key',
            'env_vars': {
                'DEEPSEEK_API_KEY': 'sk-1234567890abcdef1234567890abcdef',
                'MAX_PAGES_DEFAULT': '100',
                'CRAWL_DEPTH_DEFAULT': '3',
                'DELAY_DEFAULT': '1.0'
            },
            'expected_valid': True
        },
        {
            'name': 'Placeholder API key',
            'env_vars': {
                'DEEPSEEK_API_KEY': 'your_deepseek_api_key_here',
                'MAX_PAGES_DEFAULT': '50',
            },
            'expected_valid': True  # Should be valid but with warnings
        },
        {
            'name': 'Invalid numeric values',
            'env_vars': {
                'DEEPSEEK_API_KEY': 'sk-validkey123456789',
                'MAX_PAGES_DEFAULT': 'not_a_number',
                'CRAWL_DEPTH_DEFAULT': '-1',
                'DELAY_DEFAULT': 'invalid'
            },
            'expected_valid': False
        },
        {
            'name': 'Out of range values',
            'env_vars': {
                'DEEPSEEK_API_KEY': 'sk-validkey123456789',
                'MAX_PAGES_DEFAULT': '5000',  # Too high
                'CRAWL_DEPTH_DEFAULT': '20',  # Too high
                'DELAY_DEFAULT': '0.01'      # Too low
            },
            'expected_valid': True  # Valid but with warnings
        }
    ]
    
    original_env = dict(os.environ)
    
    try:
        from intellicrawler.utils.config import validate_environment_variables
        
        for scenario in test_scenarios:
            print(f"\n🧪 Testing scenario: {scenario['name']}")
            print("-" * 40)
            
            # Clear relevant environment variables
            env_keys_to_clear = ['DEEPSEEK_API_KEY', 'MAX_PAGES_DEFAULT', 'CRAWL_DEPTH_DEFAULT', 'DELAY_DEFAULT', 'CHROME_PATH']
            for key in env_keys_to_clear:
                os.environ.pop(key, None)
            
            # Set test environment variables
            for key, value in scenario['env_vars'].items():
                os.environ[key] = value
                print(f"  📝 Set {key}={value}")
            
            # Run validation
            validation = validate_environment_variables()
            
            print(f"  📊 Result: {'✅ VALID' if validation['valid'] else '❌ INVALID'}")
            print(f"  ❌ Errors: {len(validation['errors'])}")
            print(f"  ⚠️  Warnings: {len(validation['warnings'])}")
            print(f"  ℹ️  Info: {len(validation['info'])}")
            
            # Print details
            for error in validation['errors']:
                print(f"    ❌ {error['variable']}: {error['message']}")
            for warning in validation['warnings']:
                print(f"    ⚠️  {warning['variable']}: {warning['message']}")
                
        return True
        
    except Exception as e:
        print(f"❌ Environment variable validation test failed: {str(e)}")
        return False
        
    finally:
        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)

def test_graceful_degradation():
    """Test graceful degradation when API keys are missing"""
    print("\n" + "=" * 60)
    print("🛡️  TESTING GRACEFUL DEGRADATION")
    print("=" * 60)
    
    # Test without API key
    original_api_key = os.environ.get('DEEPSEEK_API_KEY')
    try:
        # Remove API key
        if 'DEEPSEEK_API_KEY' in os.environ:
            del os.environ['DEEPSEEK_API_KEY']
        
        from intellicrawler.utils.config import load_config
        from intellicrawler.ai_integration import DeepSeekAI
        
        # Test configuration loading without API key
        config = load_config()
        print(f"✅ Configuration loaded without API key")
        print(f"🔑 API key in config: {'Present' if config.get('deepseek_api_key') else 'Missing'}")
        
        # Test AI integration graceful handling
        try:
            ai = DeepSeekAI()
            api_key_set = ai.is_api_key_set()
            print(f"✅ AI integration initialized")
            print(f"🔑 AI reports API key set: {api_key_set}")
            
            # Test API key check method
            try:
                key_valid = ai.check_api_key()
                print(f"🔍 API key validation: {'✅ Valid' if key_valid else '❌ Invalid/Missing'}")
            except Exception as e:
                print(f"🔍 API key validation gracefully handled error: {str(e)}")
                
        except Exception as e:
            print(f"❌ AI integration failed: {str(e)}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Graceful degradation test failed: {str(e)}")
        return False
        
    finally:
        # Restore API key if it existed
        if original_api_key:
            os.environ['DEEPSEEK_API_KEY'] = original_api_key

def test_config_file_operations():
    """Test configuration file creation and loading"""
    print("\n" + "=" * 60)
    print("📁 TESTING CONFIG FILE OPERATIONS")
    print("=" * 60)
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # Mock the config directory
            import intellicrawler.utils.config as config_module
            original_config_dir = config_module.CONFIG_DIR
            original_config_file = config_module.CONFIG_FILE
            
            config_module.CONFIG_DIR = temp_dir
            config_module.CONFIG_FILE = os.path.join(temp_dir, "config.json")
            
            from intellicrawler.utils.config import create_default_config, save_config, load_config
            
            print("🏗️  Testing default config creation...")
            default_config = create_default_config()
            print(f"✅ Default config created with {len(default_config)} keys")
            
            print("🧪 Testing config modification and saving...")
            test_config = default_config.copy()
            test_config['test_key'] = 'test_value'
            test_config['deepseek_api_key'] = 'test_api_key_12345'
            
            save_result = save_config(test_config)
            print(f"💾 Save result: {'✅ Success' if save_result else '❌ Failed'}")
            
            print("🔄 Testing config loading...")
            loaded_config = load_config()
            print(f"📥 Loaded config with {len(loaded_config)} keys")
            print(f"🔍 Test key preserved: {'✅ Yes' if loaded_config.get('test_key') == 'test_value' else '❌ No'}")
            
            return True
            
        except Exception as e:
            print(f"❌ Config file operations test failed: {str(e)}")
            return False
            
        finally:
            # Restore original paths
            config_module.CONFIG_DIR = original_config_dir
            config_module.CONFIG_FILE = original_config_file

def main():
    """Run all configuration tests"""
    print("🚀 INTELLICRAWLER CONFIGURATION SYSTEM TESTS")
    print("=" * 80)
    
    tests = [
        ("Basic Configuration Loading", test_basic_config_loading),
        ("Environment Variable Validation", test_environment_variable_validation),
        ("Graceful Degradation", test_graceful_degradation),
        ("Config File Operations", test_config_file_operations)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running: {test_name}")
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: FAILED with exception: {str(e)}")
    
    print("\n" + "=" * 80)
    print(f"📊 TEST RESULTS")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success rate: {(passed / (passed + failed) * 100):.1f}%")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Configuration system is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())