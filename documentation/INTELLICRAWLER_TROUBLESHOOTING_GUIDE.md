# 🛠️ IntelliCrawler Troubleshooting Guide

> **Version**: 2.0.0  
> **Last Updated**: July 2025  
> **Focus**: Unified Interface & Common Issues

## Table of Contents

1. [Quick Diagnostics](#quick-diagnostics)
2. [Unified Interface Issues](#unified-interface-issues)
3. [AI Integration Problems](#ai-integration-problems)
4. [Crawling & Data Extraction Issues](#crawling--data-extraction-issues)
5. [Performance & System Issues](#performance--system-issues)
6. [Configuration & Setup Problems](#configuration--setup-problems)
7. [Advanced Troubleshooting](#advanced-troubleshooting)

---

## Quick Diagnostics

### 🔍 First Steps for Any Issue

**1. Check System Status**
```bash
# Run the built-in diagnostic
python run_diagnostic.py

# Check system requirements
python -c "import sys; print(f'Python: {sys.version}')"
python -c "import PyQt5; print('PyQt5: OK')"
```

**2. Verify Installation**
```bash
# Ensure all dependencies are installed
pip install -r requirements.txt

# Check for missing modules
python -c "import intellicrawler; print('IntelliCrawler: OK')"
```

**3. Review Recent Logs**
- Check `logs/` directory for recent error logs
- Look for patterns in error messages
- Note timestamps of when issues started

### 🚨 Emergency Quick Fixes

**Application Won't Start**:
```bash
# Reset configuration
rm -rf ~/.intellicrawler/config.json
python -m intellicrawler.main
```

**Unified Interface Not Loading**:
```bash
# Clear cache and restart
rm -rf ~/.intellicrawler/cache/
python -m intellicrawler.main
```

**AI Features Not Working**:
```bash
# Verify API key
python -c "from intellicrawler.utils.config import load_config; print(load_config().get('ai', {}).get('api_key', 'NOT SET')[:10] + '...')"
```

---

## Unified Interface Issues

### 🚀 Interface Loading Problems

**Issue**: Unified interface tab is blank or not loading

**Symptoms**:
- Empty tab content
- "Loading..." message that never completes
- Interface components not visible

**Solutions**:
```bash
# Solution 1: Clear interface cache
rm -rf ~/.intellicrawler/ui_cache/
python -m intellicrawler.main

# Solution 2: Reset UI settings
python -c "
from intellicrawler.utils.config import load_config, save_config
config = load_config()
config.pop('ui_settings', None)
save_config(config)
"

# Solution 3: Check for Qt/PyQt5 issues
python -c "
from PyQt5.QtWidgets import QApplication, QWidget
app = QApplication([])
widget = QWidget()
widget.show()
print('Qt/PyQt5 working correctly')
app.quit()
"
```

### 🎛️ Control Panel Issues

**Issue**: Buttons not responding or controls not working

**Symptoms**:
- Start button doesn't initiate crawling
- Settings controls don't save changes
- Progress bar not updating

**Diagnostic Steps**:
```python
# Test button functionality
python test_unified_interface.py

# Check for signal/slot connections
# Look for "signal not connected" warnings in logs
```

**Solutions**:
1. **Restart Application**: Close and reopen IntelliCrawler
2. **Check Permissions**: Ensure write access to config directory
3. **Verify Dependencies**: Reinstall PyQt5 if needed
4. **Reset Interface**: Delete UI cache and restart

### 📊 Data Display Problems

**Issue**: Data table not showing results or updating incorrectly

**Symptoms**:
- Empty data table after successful crawling
- Partial data display
- Formatting issues in results

**Solutions**:
```python
# Check data persistence
python -c "
from intellicrawler.utils.data_persistence import DataPersistenceManager
dm = DataPersistenceManager()
print(f'Data directory: {dm.base_dir}')
print(f'Sessions available: {len(dm.list_sessions())}')
"

# Verify table model
# Check if QTableWidget is properly initialized
# Look for data model connection issues
```

---

## AI Integration Problems

### 🤖 API Connection Issues

**Issue**: AI features not working or showing connection errors

**Symptoms**:
- "API key not configured" messages
- "Connection timeout" errors
- AI analysis not starting

**Diagnostic Commands**:
```bash
# Test API connection
python -c "
from intellicrawler.ai_integration import DeepSeekAI
ai = DeepSeekAI()
try:
    result = ai.test_connection()
    print(f'API Connection: {result}')
except Exception as e:
    print(f'API Error: {e}')
"
```

**Solutions**:

**1. API Key Configuration**:
```bash
# Set API key via environment
export DEEPSEEK_API_KEY="your_api_key_here"

# Or configure via settings
# Go to Settings tab → AI Configuration → Enter API key
```

**2. Network Connectivity**:
```bash
# Test internet connection
curl -I https://api.deepseek.com/

# Check firewall/proxy settings
# Ensure port 443 (HTTPS) is accessible
```

**3. API Quota Issues**:
- Check DeepSeek account dashboard
- Verify remaining API credits
- Consider upgrading plan if needed

### 🧠 Analysis Not Working

**Issue**: AI analysis fails or produces poor results

**Symptoms**:
- Analysis gets stuck at 0%
- Empty or nonsensical analysis results
- "Analysis failed" error messages

**Solutions**:

**1. Model Selection**:
```python
# Try different models
# deepseek-chat: General purpose
# deepseek-reasoner: Complex analysis
```

**2. Content Size Issues**:
- Reduce max pages for large crawls
- Split large content into smaller chunks
- Use summary analysis for overview

**3. Prompt Optimization**:
```python
# Use specific, clear prompts
# Example: "Extract product names and prices from this e-commerce page"
# Avoid: "Analyze this content"
```

---

## Crawling & Data Extraction Issues

### 🕷️ Crawling Failures

**Issue**: Crawling starts but fails to extract data

**Symptoms**:
- "No data found" messages
- Crawling completes with 0 results
- Timeout errors during crawling

**Diagnostic Steps**:
```bash
# Test with simple website
# Try: https://httpbin.org/html (test site)

# Check anti-scraping detection
python -c "
from intellicrawler.ui.universal_website_handler import UniversalWebsiteHandler
handler = UniversalWebsiteHandler()
score = handler.detect_anti_scraping_measures('https://example.com')
print(f'Anti-scraping score: {score}')
"
```

**Solutions by Website Type**:

**JavaScript-Heavy Sites**:
```python
# Enable dynamic crawling mode
# This uses Selenium for JavaScript execution
# Slower but more comprehensive
```

**Protected Sites**:
```python
# Reduce crawling speed
# Add delays between requests
# Use different user agents
# Consider proxy rotation
```

**Complex Navigation**:
```python
# Increase crawling depth
# Enable smart navigation
# Use universal handler patterns
```

### 📄 Content Extraction Problems

**Issue**: Pages crawled but content not extracted properly

**Symptoms**:
- Empty content fields
- Partial text extraction
- Encoding issues (garbled text)

**Solutions**:

**1. Encoding Issues**:
```python
# Check for encoding detection
# Look for charset in HTTP headers
# Try different encoding methods
```

**2. Content Structure**:
```python
# Use universal extraction patterns
# Enable multiple extraction methods
# Check for hidden or dynamic content
```

**3. Anti-Scraping Measures**:
```python
# Enable anti-scraping evasion
# Use rotating user agents
# Implement request delays
```

---

## Performance & System Issues

### ⚡ Slow Performance

**Issue**: Application running slowly or becoming unresponsive

**Symptoms**:
- Long startup times (>5 seconds)
- UI freezing during operations
- High memory usage

**Performance Diagnostics**:
```bash
# Check system resources
python -c "
import psutil
print(f'CPU: {psutil.cpu_percent()}%')
print(f'Memory: {psutil.virtual_memory().percent}%')
print(f'Disk: {psutil.disk_usage(\"/\").percent}%')
"

# Monitor during operation
python -c "
import time, psutil, os
pid = os.getpid()
process = psutil.Process(pid)
for i in range(10):
    print(f'Memory: {process.memory_info().rss / 1024 / 1024:.1f}MB')
    time.sleep(1)
"
```

**Optimization Solutions**:

**1. Reduce Resource Usage**:
- Lower max pages limit (10-50 instead of 100+)
- Disable auto-analysis for large crawls
- Close unused browser tabs
- Restart application periodically

**2. System Optimization**:
```bash
# Clear temporary files
rm -rf ~/.intellicrawler/temp/
rm -rf ~/.intellicrawler/cache/

# Increase virtual memory if needed
# Close other resource-intensive applications
```

**3. Configuration Tuning**:
```python
# Adjust crawling delays
# Reduce concurrent connections
# Optimize database queries
# Enable lazy loading
```

### 💾 Memory Issues

**Issue**: High memory usage or out-of-memory errors

**Symptoms**:
- Application crashes with memory errors
- System becomes sluggish
- "MemoryError" exceptions

**Solutions**:
```python
# Monitor memory usage
# Implement data streaming for large datasets
# Use pagination for large results
# Clear data periodically during long operations
```

---

## Configuration & Setup Problems

### ⚙️ Configuration File Issues

**Issue**: Settings not saving or loading incorrectly

**Symptoms**:
- Settings reset after restart
- "Configuration file not found" errors
- Invalid configuration warnings

**Solutions**:
```bash
# Check configuration directory
ls -la ~/.intellicrawler/

# Verify file permissions
chmod 755 ~/.intellicrawler/
chmod 644 ~/.intellicrawler/config.json

# Reset configuration
python -c "
from intellicrawler.utils.config import create_default_config
create_default_config()
print('Configuration reset to defaults')
"
```

### 🔑 API Key Problems

**Issue**: API key not working or not being saved

**Symptoms**:
- "Invalid API key" errors
- API key field empty after saving
- Authentication failures

**Solutions**:
```bash
# Verify API key format
# DeepSeek keys start with 'sk-'
# Should be 35+ characters long

# Test API key manually
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.deepseek.com/models

# Set via environment variable
export DEEPSEEK_API_KEY="your_key_here"
```

---

## Advanced Troubleshooting

### 🔬 Debug Mode

**Enable Debug Logging**:
```bash
# Set debug environment variable
export INTELLICRAWLER_DEBUG=true
python -m intellicrawler.main

# Or modify logging configuration
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
"
```

### 📋 System Information Collection

**Gather Diagnostic Information**:
```bash
# Create diagnostic report
python -c "
import sys, platform, PyQt5
print(f'Python: {sys.version}')
print(f'Platform: {platform.platform()}')
print(f'PyQt5: {PyQt5.QtCore.PYQT_VERSION_STR}')
print(f'Qt: {PyQt5.QtCore.QT_VERSION_STR}')

from intellicrawler.utils.config import load_config
config = load_config()
print(f'Config loaded: {config is not None}')
"
```

### 🆘 Getting Help

**Before Reporting Issues**:
1. Run the diagnostic script: `python run_diagnostic.py`
2. Check recent logs in `logs/` directory
3. Try the quick fixes mentioned above
4. Gather system information

**When Reporting Issues**:
- Include IntelliCrawler version
- Describe exact steps to reproduce
- Include error messages and logs
- Mention operating system and Python version
- Attach diagnostic report if possible

**Support Channels**:
- GitHub Issues: For bug reports and feature requests
- Documentation: Check comprehensive guides first
- Community Forums: For usage questions and tips

---

*This troubleshooting guide covers the most common issues with IntelliCrawler. For additional help, consult the comprehensive documentation or reach out through the support channels.*
