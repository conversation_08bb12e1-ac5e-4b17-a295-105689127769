# IntelliCrawler Diagnostic Tools

This document provides information about the diagnostic tools available in IntelliCrawler to help with troubleshooting and debugging.

## Overview

IntelliCrawler includes a comprehensive set of diagnostic tools to help identify and resolve issues with the application. These tools are designed to:

1. Verify system configuration and requirements
2. Test network connectivity
3. Check WebDriver availability for dynamic web crawling
4. Validate API keys and authentication
5. Ensure all components are working correctly

## Available Diagnostic Tools

### Main Diagnostic Tool

The main diagnostic tool runs a comprehensive set of tests across all components of IntelliCrawler.

**How to run:**

```bash
# CLI mode
python run_diagnostic.py

# GUI mode
python run_diagnostic.py --gui
```

### AI Integration Self-Test

Tests specifically focused on the DeepSeek AI integration.

**How to run:**

```bash
python -m intellicrawler.ai_self_test
```

### Crawler Self-Test

Tests specifically focused on the web crawler component.

**How to run:**

```bash
python -m intellicrawler.crawler_self_test
```

### API Key Test

Tests specifically focused on the API key configuration.

**How to run:**

```bash
python api_key_test.py
```

## Interpreting Test Results

The diagnostic tools will display detailed results for each test, including:

- Pass/fail status
- Detailed messages
- Timing information
- Summary statistics
- Recommendations based on failures

### Test Categories

1. **System Tests**: Operating system, Python version, required packages, file permissions
2. **Configuration Tests**: Config directory, file access, API key configuration
3. **Network Tests**: Internet connectivity, API endpoint accessibility, test URL accessibility
4. **Crawler Tests**: Crawler initialization, static and dynamic crawling modes, WebDriver availability
5. **AI Integration Tests**: AI module initialization, model availability, API authentication

## Common Issues and Resolutions

### Missing Required Packages

If the diagnostic shows missing packages:

```bash
pip install -r requirements.txt
```

### WebDriver Issues

If WebDriver tests fail:

1. Ensure you have Chrome or Microsoft Edge installed
2. Check that the browser is up to date
3. Verify that WebDriver-Manager can locate your browser

### API Key Configuration

If API key tests fail:

1. Open SuperCrawler and navigate to Settings
2. Enter a valid DeepSeek API key
3. Save the settings
4. Restart the application

### File Permission Issues

If file permission tests fail:

1. Ensure you have write access to your user directory
2. Try running the application as administrator
3. Check if antivirus software is blocking access

## Test Output Files

The diagnostic tools save test results and other useful information in the `test_output` directory. These files can be helpful for troubleshooting or when seeking support.

## Adding Diagnostic Tool to IntelliCrawler

The diagnostic tool is integrated into the IntelliCrawler application. You can access it from the Tools menu:

1. Open IntelliCrawler
2. Click on **Tools** in the menu bar
3. Select **Run Diagnostic Tests**

## Getting Help

If the diagnostic tools indicate issues that you cannot resolve, please contact support with the diagnostic output and the contents of the `test_output` directory. 