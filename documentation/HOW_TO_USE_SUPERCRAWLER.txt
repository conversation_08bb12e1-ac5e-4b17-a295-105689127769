IntelliCrawler – Quick Usage Guide
==================================

1. WHAT IS IntelliCrawler?
   ------------------------
   IntelliCrawler is a desktop application (Python 3.9+, PyQt5) that combines
   traditional web-scraping with DeepSeek AI analysis.  It can visit pages,
   extract their text/HTML, and run AI models to summarise, classify, or
   otherwise process the collected data – all from an intuitive tabbed GUI or
   via simple command-line entry points.

2. TYPICAL THINGS YOU CAN DO WITH IT
   ----------------------------------
   • Market-research – scrape competitor websites and let DeepSeek summarise
     product features or pricing tables.
   • Academic research – collect articles, papers, blog posts and generate
     thematic summaries, keyword lists, or sentiment scores.
   • Machine-learning data gathering – export crawled text to JSON/CSV as
     training corpora.
   • SEO/site audits – map out internal links, identify broken pages and gather
     on-page content for analysis.
   • Archiving & monitoring – schedule crawls (via scripts) to detect content
     changes over time.

3. QUICK START (GUI)
   -----------------
   Prerequisites
   • Windows 10/11, macOS, or Linux.
   • Python ≥ 3.8 installed and on PATH.
   • Google Chrome (for dynamic crawling) is recommended.

   Installation
     $ git clone https://github.com/yourusername/intellicrawler.git
     $ cd intellicrawler
     $ pip install -e .
     $ pip install -r requirements.txt

   Running the App
     $ python -m intellicrawler.main

   Core steps inside the UI
   1. Open the "Crawler" tab.
   2. Enter a starting URL (e.g., https://docs.firecrawl.dev/introduction).
   3. Choose depth / max pages and whether to enable Dynamic (Selenium) mode.
   4. Click "Start Crawling" – progress appears in real time; scraped pages are
      listed when finished.
   5. Switch to the "Analysis" tab to run DeepSeek AI on the collected content.
   6. Export results from the File → Export menu (JSON, CSV, Excel, XML, TXT).

4. COMMAND-LINE OPTIONS
   ---------------------
   • Debug/Headless version (no real crawling or API calls):
       $ python -m intellicrawler.debug
   • One-shot diagnostic to verify your environment:
       $ python intellicrawler/diagnostic.py
   • Launcher with extra flags (e.g., override Chrome path):
       $ python intellicrawler_launcher.py --url "https://example.com" \
           --chrome-path "C:\\Path\\To\\chrome.exe" --no-dynamic

5. CONFIGURATION & API KEY
   ------------------------
   • Settings are stored in ~/.intellicrawler/config.json.
   • Open the "Settings" tab or manually edit the JSON file to add your
     `deepseek_api_key`.
   • Without a key you can still crawl, but AI analysis will be disabled.

6. STATIC VS DYNAMIC CRAWLING
   ---------------------------
   • Static mode – fastest; uses requests + BeautifulSoup; ideal for pages that
     serve content without heavy JavaScript.
   • Dynamic mode – launches Chrome via Selenium; fetches fully rendered HTML
     including JS-generated elements; heavier but necessary for modern SPAs.

7. OUTPUT DIRECTORY
   -----------------
   • All diagnostic logs and example crawl outputs are written to the
     `test_output/` folder in the repository root.
   • GUI exports default to your chosen save location.

8. TROUBLESHOOTING
   ----------------
   • Run the diagnostic tool (`python intellicrawler/diagnostic.py`) to test
     internet, WebDriver and API connectivity.
   • Ensure Chrome is installed or point the launcher at a Chromium-based
     browser.
   • Missing packages?  Re-run `pip install -r requirements.txt`.

9. BEST PRACTICES
   ---------------
   Performance Optimization:
   • Start with small page limits (5-10) when testing new websites
   • Use static crawling for simple sites, dynamic only when necessary
   • Adjust delay settings based on website response times
   • Monitor memory usage in Task Manager during large crawls

   Ethical Crawling:
   • Always check robots.txt before crawling
   • Respect rate limits and don't overwhelm servers
   • Use appropriate delays between requests (1-3 seconds recommended)
   • Consider contacting website owners for permission on large-scale crawling

   AI Analysis Tips:
   • Use specific prompts for better analysis results
   • Choose appropriate models: R1 for reasoning, V3 for general analysis
   • Save analysis results before starting new crawls
   • Experiment with different temperature settings for varied outputs

   Data Management:
   • Export data regularly to avoid loss
   • Use descriptive filenames with timestamps
   • Keep configuration backups of successful setups
   • Organize exports by project or website

10. NEXT STEPS
    -----------
    • Experiment with crawl parameters to balance speed vs coverage.
    • Explore DeepSeek model options (R1/V3) in the Analysis tab.
    • Extend IntelliCrawler by writing plugins under `intellicrawler/` for custom
      post-processing or scheduling.

Happy crawling!