PHASE 1 — Roll back to backup & smoke-test
------------------------------------------------
1. Backup current code:
   Rename-Item supercrawler supercrawler_broken_%date:~10,4%%date:~4,2%%date:~7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
2. Restore backup copy:
   Copy-Item "SuperCrawlerbackup\supercrawler" -Destination "supercrawler" -Recurse
3. Quick tests:
   python -m supercrawler.main   # GUI launch
   python tests\proxy_test.py   # CLI proxy test

PHASE 2 — Proxy-scanner hardening
------------------------------------------------
A) JavaScript-encoded proxy support
   • Detect Base64 / hex / %-encoded strings in <script> blocks.
   • Decode candidates (limit 30 per page) and regex-extract IP:PORT.
   • If still empty and anti-scraping score ≥3, load page once in headless Chrome (undetected-chromedriver) and retry extraction.

B) Anti-scraping counter-measures
   • Proxy rotation for IP blocking.
   • Realistic header & User-Agent rotation.
   • 1-3 s random delays & limited parallelism (≤5 threads).
   • Ignore CSS-hidden honeypot elements.
   • Selenium+CAPTCHA solver fallback for CAPTCHA/JS challenges.
   • Optional cookie-based login support.

C) Logging crash fix
   • Replace emojis in logger messages with ASCII tags.
   • Introduce safe_log(msg) helper that strips non-ASCII before logging (Windows CP-1252 safe).

D) Missing method patch
   • Implement _intelligent_encoding_detector in ProxyScraperWorker, calling new JS/encoding detection and HTML entity / ROT13 / URL decode passes.

PHASE 3 — Tests & QA
------------------------------------------------
1. Unit tests:
   • tests/js_encoded_test.py – verify Base64-encoded string extraction.
   • tests/log_ascii_test.py – ensure no UnicodeEncodeError.
2. End-to-end scan on proxy-list.org, advanced.name, proxyscrape API → expect >2 000 proxies.
3. Git commit & tag:  git add . && git commit -m "Backup restore + proxy scanner hardening" && git tag backup_restore_20250612

PHASE 4 — Future enhancements
------------------------------------------------
• Integrate BrightData Unlocker for Cloudflare sites [BrightData blog].
• ML-powered encoding pattern ranking.
• YAML per-site anti-scraping profiles.

References
• Data Journal anti-scraping techniques (2025-01-27) – https://medium.com/@datajournal/anti-scraping-techniques-2cba92f700a6
• BrightData anti-scraping overview – https://brightdata.com/blog/web-data/anti-scraping-techniques 