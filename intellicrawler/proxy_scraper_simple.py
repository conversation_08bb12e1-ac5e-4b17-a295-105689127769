#!/usr/bin/env python3
"""
Simple Reliable Proxy Scraper
Handles easy cases first, complex scraping as fallback
"""

import requests
import json
import re
import time
import random
import logging
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# Setup simple logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SimpleProxyScraper:
    def __init__(self, max_proxies=5000):
        self.logger = logging.getLogger(__name__)
        self.max_proxies = max_proxies
        self.found_proxies = []
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # Simple sources that just return proxy lists
        self.simple_sources = {
            # Direct API endpoints that return plain text
            'proxyscrape_http': 'https://api.proxyscrape.com/v2/?request=get&format=textplain&protocol=http',
            'proxyscrape_socks4': 'https://api.proxyscrape.com/v2/?request=get&format=textplain&protocol=socks4', 
            'proxyscrape_socks5': 'https://api.proxyscrape.com/v2/?request=get&format=textplain&protocol=socks5',
            
            # GitHub proxy lists (data.txt files) - Proxifly
            'proxifly_all': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/all/data.txt',
            'proxifly_http': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/http/data.txt',
            'proxifly_socks4': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks4/data.txt',
            'proxifly_socks5': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/protocols/socks5/data.txt',
            
            # Proxifly - Popular Countries
            'proxifly_us': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/US/data.txt',
            'proxifly_uk': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/GB/data.txt',
            'proxifly_ca': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/CA/data.txt',
            'proxifly_de': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/DE/data.txt',
            'proxifly_fr': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/FR/data.txt',
            'proxifly_jp': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/JP/data.txt',
            'proxifly_kr': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/KR/data.txt',
            'proxifly_sg': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/SG/data.txt',
            'proxifly_nl': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/NL/data.txt',
            'proxifly_ru': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/RU/data.txt',
            'proxifly_cn': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/CN/data.txt',
            'proxifly_in': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/IN/data.txt',
            'proxifly_br': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/BR/data.txt',
            'proxifly_au': 'https://cdn.jsdelivr.net/gh/proxifly/free-proxy-list@main/proxies/countries/AU/data.txt',
            
            # Fresh Proxy List (vakhov)
            'fresh_proxy_http': 'https://raw.githubusercontent.com/vakhov/fresh-proxy-list/master/http.txt',
            'fresh_proxy_https': 'https://raw.githubusercontent.com/vakhov/fresh-proxy-list/master/https.txt',
            'fresh_proxy_socks4': 'https://raw.githubusercontent.com/vakhov/fresh-proxy-list/master/socks4.txt',
            'fresh_proxy_socks5': 'https://raw.githubusercontent.com/vakhov/fresh-proxy-list/master/socks5.txt',
            
            # Other simple API endpoints
            'proxy_list_download_http': 'https://www.proxy-list.download/api/v1/get?type=http',
            'proxy_list_download_socks4': 'https://www.proxy-list.download/api/v1/get?type=socks4',
            'proxy_list_download_socks5': 'https://www.proxy-list.download/api/v1/get?type=socks5',
            
            # GeoNode API
            'geonode_api': 'https://proxylist.geonode.com/api/proxy-list?limit=500&page=1&sort_by=lastChecked&sort_type=desc',
        }

    def scrape_all_sources(self, progress_callback=None):
        """Scrape all simple sources in parallel"""
        try:
            total_sources = len(self.simple_sources)
            completed = 0
            
            if progress_callback:
                progress_callback(0, f"Starting simple proxy scraping from {total_sources} sources...")
            
            with ThreadPoolExecutor(max_workers=8) as executor:
                # Submit all scraping tasks
                future_to_source = {
                    executor.submit(self._scrape_simple_source, name, url): name
                    for name, url in self.simple_sources.items()
                }
                
                # Collect results as they complete
                for future in as_completed(future_to_source):
                    source_name = future_to_source[future]
                    try:
                        source_proxies = future.result(timeout=30)
                        
                        # Add proxies without duplicates
                        added_count = 0
                        for proxy in source_proxies:
                            if len(self.found_proxies) >= self.max_proxies:
                                break
                            if not self._is_duplicate(proxy):
                                self.found_proxies.append(proxy)
                                added_count += 1
                        
                        completed += 1
                        progress = int((completed / total_sources) * 100)
                        
                        if progress_callback:
                            progress_callback(progress, f"[{source_name}] Found {added_count} proxies ({len(self.found_proxies)} total)")
                        
                        self.logger.info(f"[{source_name}] Found {added_count} new proxies")
                        
                    except Exception as e:
                        completed += 1
                        self.logger.error(f"Error scraping {source_name}: {str(e)}")
                        if progress_callback:
                            progress_callback(int((completed / total_sources) * 100), f"[{source_name}] Failed: {str(e)}")
            
            if progress_callback:
                progress_callback(100, f"Completed! Found {len(self.found_proxies)} total proxies")
            
            return self.found_proxies
            
        except Exception as e:
            error_msg = f"Scraping failed: {str(e)}"
            self.logger.error(error_msg)
            if progress_callback:
                progress_callback(100, error_msg)
            return self.found_proxies

    def _scrape_simple_source(self, source_name, url):
        """Scrape a single simple source"""
        try:
            self.logger.info(f"Scraping {source_name}: {url}")
            
            # Add random delay for rate limiting
            time.sleep(random.uniform(0.2, 0.8))
            
            response = self.session.get(url, timeout=20)
            response.raise_for_status()
            
            # Determine content type and parse accordingly
            content_type = response.headers.get('content-type', '').lower()
            
            if 'json' in content_type:
                return self._parse_json_response(response.text, source_name)
            else:
                return self._parse_text_response(response.text, source_name)
                
        except Exception as e:
            self.logger.error(f"Error scraping {source_name}: {str(e)}")
            return []

    def _parse_json_response(self, content, source_name):
        """Parse JSON proxy response"""
        proxies = []
        try:
            data = json.loads(content)
            
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict):
                        ip = item.get('ip') or item.get('host') or item.get('address')
                        port = str(item.get('port', ''))
                        
                        if ip and port and self._is_valid_ip(ip) and self._is_valid_port(port):
                            proxy = {
                                'ip': ip,
                                'port': port,
                                'country': item.get('country', item.get('countryCode', 'Unknown')),
                                'protocol': self._determine_protocol_from_source(source_name, item.get('protocol', '')),
                                'anonymity': item.get('anonymity', item.get('level', 'Unknown')),
                                'source': source_name,
                                'timestamp': datetime.now().isoformat()
                            }
                            proxies.append(proxy)
            
            elif isinstance(data, dict) and 'data' in data:
                # Handle nested data structure
                return self._parse_json_response(json.dumps(data['data']), source_name)
                
        except Exception as e:
            self.logger.error(f"JSON parsing error for {source_name}: {str(e)}")
        
        return proxies

    def _parse_text_response(self, content, source_name):
        """Parse plain text proxy response"""
        proxies = []
        
        # Handle different text formats
        lines = content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Handle IP:PORT format
            if ':' in line:
                # Remove protocol prefix if present
                clean_line = re.sub(r'^(https?|socks[45])://', '', line.lower())
                
                parts = clean_line.split(':')
                if len(parts) >= 2:
                    ip = parts[0].strip()
                    port = parts[1].split()[0].strip()  # Remove any trailing data
                    
                    if self._is_valid_ip(ip) and self._is_valid_port(port):
                        proxy = {
                            'ip': ip,
                            'port': port,
                            'country': 'Unknown',
                            'protocol': self._determine_protocol_from_source(source_name),
                            'anonymity': 'Unknown',
                            'source': source_name,
                            'timestamp': datetime.now().isoformat()
                        }
                        proxies.append(proxy)
        
        return proxies

    def _determine_protocol_from_source(self, source_name, protocol_hint=''):
        """Determine protocol from source name and hints"""
        source_lower = source_name.lower()
        protocol_lower = protocol_hint.lower()
        
        if 'socks5' in source_lower or 'socks5' in protocol_lower:
            return 'SOCKS5'
        elif 'socks4' in source_lower or 'socks4' in protocol_lower:
            return 'SOCKS4'
        elif 'https' in source_lower or 'https' in protocol_lower or 'ssl' in protocol_lower:
            return 'HTTPS'
        else:
            return 'HTTP'

    def _is_valid_ip(self, ip):
        """Validate IPv4 address"""
        try:
            parts = ip.split('.')
            return (len(parts) == 4 and 
                   all(0 <= int(part) <= 255 for part in parts) and
                   ip not in ['0.0.0.0', '127.0.0.1'])
        except:
            return False

    def _is_valid_port(self, port):
        """Validate port number"""
        try:
            return 1 <= int(port) <= 65535
        except:
            return False

    def _is_duplicate(self, proxy):
        """Check if proxy is already in found_proxies"""
        proxy_key = f"{proxy['ip']}:{proxy['port']}"
        return any(f"{p['ip']}:{p['port']}" == proxy_key for p in self.found_proxies)

    def export_proxies(self, filename, format='txt'):
        """Export proxies to file"""
        try:
            if format.lower() == 'json':
                with open(filename, 'w') as f:
                    json.dump(self.found_proxies, f, indent=2)
            else:
                with open(filename, 'w') as f:
                    for proxy in self.found_proxies:
                        f.write(f"{proxy['ip']}:{proxy['port']}\n")
            
            self.logger.info(f"Exported {len(self.found_proxies)} proxies to {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Export failed: {str(e)}")
            return False

# Test function
def test_simple_scraper():
    """Test the simple scraper"""
    scraper = SimpleProxyScraper(max_proxies=1000)
    
    def progress_callback(percent, message):
        print(f"[{percent:3d}%] {message}")
    
    print("Testing simple proxy scraper...")
    proxies = scraper.scrape_all_sources(progress_callback)
    
    print(f"\nResults:")
    print(f"Total proxies found: {len(proxies)}")
    
    # Show breakdown by protocol
    protocols = {}
    for proxy in proxies:
        protocol = proxy['protocol']
        protocols[protocol] = protocols.get(protocol, 0) + 1
    
    print("Protocol breakdown:")
    for protocol, count in protocols.items():
        print(f"  {protocol}: {count}")
    
    # Show first 5 proxies as examples
    print("\nFirst 5 proxies:")
    for i, proxy in enumerate(proxies[:5]):
        print(f"  {i+1}. {proxy['ip']}:{proxy['port']} ({proxy['protocol']}) from {proxy['source']}")

# Add alias for backward compatibility
ProxyScraper = SimpleProxyScraper

if __name__ == "__main__":
    test_simple_scraper()
