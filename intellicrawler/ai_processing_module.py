#!/usr/bin/env python3
"""
AI Processing Module for IntelliCrawler
This module integrates advanced AI-based content processing and enhanced web scraping capabilities.
It uses DeepSeek API for deep analysis and integrates with external tools such as FireCrawl,
Jina AI, and ScrapeGraphAI for optimized data extraction.

References:
- DeepSeek API Documentation: https://api-docs.deepseek.com/
"""

import os
import json
import time
import logging
import requests
from typing import Dict, Any
from requests.exceptions import RequestException, Timeout

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('supercrawler.ai_module')

class APIError(Exception):
    """Exception raised for API errors."""
    pass

class AIProcessingModule:
    def __init__(self, config_file=None):
        """
        Initialize the AI Processing Module.
        
        Args:
            config_file: Optional path to a JSON configuration file with API keys and endpoints.
        """
        # Load configuration
        self.config = self._load_config(config_file)
        
        # DeepSeek API configuration
        self.deepseek_api_endpoint = self.config.get("DEEPSEEK_API_ENDPOINT", "https://api.deepseek.com")
        self.deepseek_api_key = self.config.get("DEEPSEEK_API_KEY", "")
        self.deepseek_model = self.config.get("DEEPSEEK_MODEL", "deepseek-chat")
        
        # External tools configuration
        self.firecrawl_api_endpoint = self.config.get("FIRECRAWL_API_ENDPOINT", "https://api.firecrawl.dev/v1") 
        self.firecrawl_api_key = self.config.get("FIRECRAWL_API_KEY", "")
        
        self.jina_api_endpoint = self.config.get("JINA_API_ENDPOINT", "https://api.jina.ai/v1")
        self.jina_api_key = self.config.get("JINA_API_KEY", "")
        
        self.scrapegraphai_endpoint = self.config.get("SCRAPEGRAPHAI_ENDPOINT", "https://api.scrapegraph.ai/v1")
        self.scrapegraphai_api_key = self.config.get("SCRAPEGRAPHAI_API_KEY", "")
        
        # Verify configuration
        self._verify_configuration()
        
        logger.info("AI Processing Module initialized successfully")
    
    def _load_config(self, config_file) -> Dict[str, Any]:
        """Load configuration from environment variables or config file."""
        config = {}
        
        # First try to load from config file if provided
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    file_config = json.load(f)
                    config.update(file_config)
                logger.info(f"Loaded configuration from {config_file}")
            except Exception as e:
                logger.error(f"Error loading config file: {e}")
        
        # Then override with environment variables (environment takes precedence)
        env_vars = [
            "DEEPSEEK_API_ENDPOINT", "DEEPSEEK_API_KEY", "DEEPSEEK_MODEL",
            "FIRECRAWL_API_ENDPOINT", "FIRECRAWL_API_KEY",
            "JINA_API_ENDPOINT", "JINA_API_KEY",
            "SCRAPEGRAPHAI_ENDPOINT", "SCRAPEGRAPHAI_API_KEY"
        ]
        
        for var in env_vars:
            if os.environ.get(var):
                config[var] = os.environ.get(var)
        
        return config
    
    def _verify_configuration(self):
        """Verify that required API keys are present."""
        if not self.deepseek_api_key:
            logger.warning("DeepSeek API key not found. DeepSeek analysis will not be available.")
        
        # Log warning about missing keys but don't fail - the module will degrade gracefully
        for name, key in [
            ("FireCrawl", self.firecrawl_api_key),
            ("Jina AI", self.jina_api_key),
            ("ScrapeGraphAI", self.scrapegraphai_api_key)
        ]:
            if not key:
                logger.warning(f"{name} API key not found. {name} features will not be available.")
    
    def analyze_content_with_deepseek(self, content: str) -> Dict[str, Any]:
        """
        Call DeepSeek API to analyze content and generate insights.
        
        Args:
            content: The webpage content to analyze
            
        Returns:
            Dict containing analysis results or error information
        """
        if not self.deepseek_api_key:
            return {"error": "DeepSeek API key not configured"}
        
        url = f"{self.deepseek_api_endpoint}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.deepseek_api_key}",
            "Content-Type": "application/json"
        }
        
        # Structure the request according to DeepSeek's API documentation
        payload = {
            "model": self.deepseek_model,
            "messages": [
                {"role": "system", "content": "Analyze the following webpage content and extract key information, topics, and insights."},
                {"role": "user", "content": content}
            ],
            "temperature": 0.3,
            "max_tokens": 500
        }
        
        logger.info("Sending analysis request to DeepSeek API")
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=60)
            response.raise_for_status()
            result = response.json()
            logger.info("Successfully received analysis from DeepSeek API")
            return result
            
        except Timeout:
            error_msg = "DeepSeek API request timed out"
            logger.error(error_msg)
            return {"error": error_msg}
            
        except RequestException as e:
            error_msg = f"Error calling DeepSeek API: {str(e)}"
            logger.error(error_msg)
            
            if hasattr(e, 'response') and e.response is not None:
                return {
                    "error": error_msg,
                    "status_code": e.response.status_code,
                    "details": e.response.text
                }
            return {"error": error_msg}
            
        except Exception as e:
            error_msg = f"Unexpected error during DeepSeek analysis: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def invoke_firecrawl(self, url: str) -> Dict[str, Any]:
        """
        Call FireCrawl API for dynamic scraping.
        
        Args:
            url: Target webpage URL to scrape
            
        Returns:
            Dict containing scraping results or error information
        """
        if not self.firecrawl_api_key:
            logger.warning("FireCrawl API key not configured - skipping")
            return {"status": "skipped", "reason": "API key not configured"}
        
        try:
            headers = {
                "Authorization": f"Bearer {self.firecrawl_api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "url": url,
                "options": {
                    "javascript_enabled": True,
                    "extract_metadata": True
                }
            }
            
            logger.info(f"Calling FireCrawl API for URL: {url}")
            response = requests.post(
                f"{self.firecrawl_api_endpoint}/extract", 
                headers=headers, 
                json=payload,
                timeout=90  # Dynamic scraping may take longer
            )
            
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            error_msg = f"FireCrawl API error: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def invoke_jina_ai(self, url: str) -> Dict[str, Any]:
        """
        Call Jina AI for content extraction and analysis.
        
        Args:
            url: Target webpage URL to analyze
            
        Returns:
            Dict containing extraction results or error information
        """
        if not self.jina_api_key:
            logger.warning("Jina AI API key not configured - skipping")
            return {"status": "skipped", "reason": "API key not configured"}
            
        try:
            headers = {
                "Authorization": f"Bearer {self.jina_api_key}",
                "Content-Type": "application/json" 
            }
            
            payload = {
                "url": url,
                "extraction_type": "structured_data"
            }
            
            logger.info(f"Calling Jina AI API for URL: {url}")
            response = requests.post(
                f"{self.jina_api_endpoint}/extract", 
                headers=headers, 
                json=payload,
                timeout=45
            )
            
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            error_msg = f"Jina AI API error: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def invoke_scrapegraphai(self, url: str) -> Dict[str, Any]:
        """
        Call ScrapeGraphAI for enhanced data extraction.
        
        Args:
            url: Target webpage URL to extract data from
            
        Returns:
            Dict containing extraction results or error information
        """
        if not self.scrapegraphai_api_key:
            logger.warning("ScrapeGraphAI API key not configured - skipping")
            return {"status": "skipped", "reason": "API key not configured"}
            
        try:
            headers = {
                "Authorization": f"Bearer {self.scrapegraphai_api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "url": url,
                "extract_options": {
                    "include_schema": True,
                    "include_entities": True
                }
            }
            
            logger.info(f"Calling ScrapeGraphAI for URL: {url}")
            response = requests.post(
                f"{self.scrapegraphai_endpoint}/scrape", 
                headers=headers, 
                json=payload,
                timeout=60
            )
            
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            error_msg = f"ScrapeGraphAI API error: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def enhanced_web_scraping_strategy(self, url: str) -> Dict[str, Any]:
        """
        Use multiple tools for advanced web scraping and analysis.
        
        Args:
            url: Target webpage URL
            
        Returns:
            Dict containing combined results from all available scraping tools
        """
        results = {}
        start_time = time.time()
        
        logger.info(f"Starting enhanced web scraping for URL: {url}")
        
        # We'll use available APIs based on configuration
        # If an API key is missing, that service will be skipped
        
        results['firecrawl'] = self.invoke_firecrawl(url)
        results['jina'] = self.invoke_jina_ai(url)
        results['scrapegraphai'] = self.invoke_scrapegraphai(url)
        
        # Calculate execution time
        execution_time = time.time() - start_time
        results['metadata'] = {
            'execution_time_seconds': execution_time,
            'timestamp': time.time(),
            'url': url
        }
        
        logger.info(f"Completed enhanced web scraping in {execution_time:.2f} seconds")
        return results

    def process_webpage(self, url: str, content: str) -> Dict[str, Any]:
        """
        Process a webpage's content through AI pipelines.
        Combines analysis from DeepSeek and enhanced scraping strategies.
        
        Args:
            url: The URL of the webpage
            content: The raw content of the webpage
            
        Returns:
            Dict containing combined analysis results
        """
        logger.info(f"Processing webpage: {url}")
        
        result = {
            "url": url,
            "timestamp": time.time(),
            "status": "success"
        }
        
        try:
            # Run DeepSeek analysis
            deepseek_result = self.analyze_content_with_deepseek(content)
            result["deepseek_analysis"] = deepseek_result
            
            # Run enhanced scraping if content analysis was successful
            if "error" not in deepseek_result:
                enhanced_result = self.enhanced_web_scraping_strategy(url)
                result["enhanced_scraping"] = enhanced_result
            
            return result
            
        except Exception as e:
            error_msg = f"Error processing webpage {url}: {str(e)}"
            logger.error(error_msg)
            return {
                "url": url,
                "timestamp": time.time(),
                "status": "error",
                "error": error_msg
            }


if __name__ == "__main__":
    # For testing the module directly
    import argparse
    
    parser = argparse.ArgumentParser(description="Test the AI Processing Module with a URL")
    parser.add_argument("--url", type=str, default="https://example.com", help="URL to process")
    parser.add_argument("--config", type=str, help="Path to config file with API keys")
    parser.add_argument("--content", type=str, help="Optional: Path to file with HTML content to analyze")
    args = parser.parse_args()
    
    # Initialize the module
    module = AIProcessingModule(config_file=args.config)
    
    # Get content either from file or fetch from URL
    if args.content and os.path.exists(args.content):
        with open(args.content, 'r', encoding='utf-8') as f:
            content = f.read()
    else:
        # Simple fetch of the URL content
        try:
            response = requests.get(args.url, timeout=30)
            response.raise_for_status()
            content = response.text
        except Exception as e:
            logger.error(f"Error fetching URL content: {e}")
            content = f"Failed to fetch content from {args.url}"
    
    # Process the webpage
    result = module.process_webpage(args.url, content)
    
    # Output the result
    print(json.dumps(result, indent=2))
    logger.info(f"Results saved to 'ai_analysis_result_{int(time.time())}.json'")
    
    # Save the result to a file
    with open(f"ai_analysis_result_{int(time.time())}.json", 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2) 