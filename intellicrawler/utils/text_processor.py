#!/usr/bin/env python3
"""
Text Processing Utility for IntelliCrawler
Implements intelligent content chunking, splitting, and preparation for AI analysis.
Inspired by <PERSON><PERSON><PERSON><PERSON>'s text splitting strategies but tailored for web content.
"""

import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
from intellicrawler.utils.logger import get_logger

class ChunkingStrategy(Enum):
    """Different strategies for chunking content"""
    SIMPLE = "simple"                # Basic character-based chunking
    SEMANTIC = "semantic"            # Content-aware chunking (paragraphs, sections)
    RECURSIVE = "recursive"          # Hierarchical chunking with multiple separators
    WEBPAGE = "webpage"              # Web-specific chunking (HTML-aware)
    SUMMARIZATION = "summarization"  # For summarization tasks (preserve context)

@dataclass
class ChunkMetadata:
    """Metadata for a content chunk"""
    chunk_id: int
    source_page: str
    source_url: str
    chunk_type: str
    word_count: int
    char_count: int
    overlap_with_previous: int
    
@dataclass
class ProcessedChunk:
    """A processed content chunk with metadata"""
    content: str
    metadata: ChunkMetadata
    context_summary: Optional[str] = None

class ContentProcessor:
    """
    Advanced content processor for AI analysis preparation.
    Handles chunking, context preservation, and content optimization.
    """
    
    # Token/character limits for different models (with safety margins)
    MODEL_LIMITS = {
        "deepseek-reasoner": {"max_chars": 55000, "max_tokens": 16000},
        "deepseek-chat": {"max_chars": 55000, "max_tokens": 16000},
        "default": {"max_chars": 50000, "max_tokens": 15000}
    }
    
    # Separators for different chunking strategies (ordered by priority)
    SEPARATORS = {
        ChunkingStrategy.SEMANTIC: [
            "\n\n\n",      # Triple newlines (strong section breaks)
            "\n\n",        # Double newlines (paragraph breaks)
            "\n",          # Single newlines
            ". ",          # Sentence endings
            "! ",          # Exclamation sentences
            "? ",          # Question sentences
            "; ",          # Semicolons
            ", ",          # Commas
            " ",           # Spaces
            ""             # Character-level (last resort)
        ],
        ChunkingStrategy.WEBPAGE: [
            "\n=== PAGE ",  # Page separators
            "\n---",        # Section separators
            "\n##",         # Markdown headers
            "\n#",          # Main headers
            "\n\n",         # Paragraphs
            "\n",           # Lines
            ". ",           # Sentences
            " ",            # Words
            ""              # Characters
        ]
    }
    
    def __init__(self, model: str = "default"):
        self.logger = get_logger()
        self.model = model
        self.limits = self.MODEL_LIMITS.get(model, self.MODEL_LIMITS["default"])
        
    def process_crawled_data(self, 
                           data: List[Dict[str, Any]], 
                           strategy: ChunkingStrategy = ChunkingStrategy.SEMANTIC,
                           preserve_context: bool = True) -> List[ProcessedChunk]:
        """
        Process crawled data into optimally-sized chunks for AI analysis.
        """
        self.logger.info(f"Processing {len(data)} pages with {strategy.value} strategy")
        
        # Step 1: Prepare and normalize content
        normalized_content = self._normalize_content(data)
        
        # Step 2: Check if chunking is needed
        total_chars = sum(len(item['content']) for item in normalized_content)
        if total_chars <= self.limits["max_chars"]:
            return self._create_single_chunk(normalized_content)
        
        # Step 3: Apply chunking strategy
        if strategy == ChunkingStrategy.WEBPAGE:
            return self._chunk_webpage_content(normalized_content)
        elif strategy == ChunkingStrategy.SEMANTIC:
            return self._chunk_semantic_content(normalized_content)
        else:
            return self._chunk_simple_content(normalized_content)
    
    def _normalize_content(self, data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """Normalize and clean content from crawled data"""
        normalized = []
        
        for i, page in enumerate(data):
            title = page.get('title', f'Page {i+1}')
            url = page.get('url', 'Unknown URL')
            content = page.get('content', '')
            
            # Clean and normalize content
            content = self._clean_content(content)
            
            normalized.append({
                'title': title,
                'url': url,
                'content': content,
                'original_index': i
            })
            
        return normalized
    
    def _clean_content(self, content: str) -> str:
        """Clean and normalize text content"""
        if not content:
            return ""
            
        # Remove excessive whitespace
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        content = re.sub(r' +', ' ', content)
        content = re.sub(r'\t+', ' ', content)
        
        # Remove common noise
        content = re.sub(r'^\s+|\s+$', '', content, flags=re.MULTILINE)
        
        return content.strip()
    
    def _create_single_chunk(self, content_list: List[Dict[str, str]]) -> List[ProcessedChunk]:
        """Create a single chunk when content fits within limits"""
        combined_content = self._combine_pages_with_separators(content_list)
        
        metadata = ChunkMetadata(
            chunk_id=0,
            source_page="All Pages Combined",
            source_url="Multiple URLs",
            chunk_type="single",
            word_count=len(combined_content.split()),
            char_count=len(combined_content),
            overlap_with_previous=0
        )
        
        return [ProcessedChunk(content=combined_content, metadata=metadata)]
    
    def _combine_pages_with_separators(self, content_list: List[Dict[str, str]]) -> str:
        """Combine multiple pages with clear separators"""
        parts = []
        
        for i, page in enumerate(content_list):
            parts.append(f"=== PAGE {i+1}: {page['title']} ===")
            parts.append(f"URL: {page['url']}")
            parts.append("")
            parts.append(page['content'])
            parts.append("")
            
        return "\n".join(parts)
    
    def _chunk_webpage_content(self, content_list: List[Dict[str, str]]) -> List[ProcessedChunk]:
        """Chunk content with webpage-specific logic"""
        chunks = []
        current_chunk = ""
        current_pages = []
        
        for page in content_list:
            page_content = f"=== PAGE: {page['title']} ===\nURL: {page['url']}\n\n{page['content']}\n\n"
            
            if len(current_chunk) + len(page_content) > self.limits["max_chars"]:
                if current_chunk:
                    chunk = self._create_chunk_from_content(current_chunk, current_pages, len(chunks))
                    chunks.append(chunk)
                    
                if len(page_content) > self.limits["max_chars"]:
                    page_chunks = self._split_large_page(page, len(chunks))
                    chunks.extend(page_chunks)
                    current_chunk = ""
                    current_pages = []
                else:
                    current_chunk = page_content
                    current_pages = [page]
            else:
                current_chunk += page_content
                current_pages.append(page)
        
        if current_chunk and current_pages:
            chunk = self._create_chunk_from_content(current_chunk, current_pages, len(chunks))
            chunks.append(chunk)
        
        return chunks
    
    def _split_large_page(self, page: Dict[str, str], chunk_id_start: int) -> List[ProcessedChunk]:
        """Split a single page that's too large into multiple chunks"""
        content = page['content']
        chunks = []
        target_size = self.limits["max_chars"] - 500  # Leave room for headers
        
        separators = self.SEPARATORS[ChunkingStrategy.SEMANTIC]
        text_chunks = self._recursive_split(content, separators, target_size)
        
        for i, chunk_content in enumerate(text_chunks):
            full_content = f"=== PAGE: {page['title']} (Part {i+1}) ===\nURL: {page['url']}\n\n{chunk_content}"
            
            metadata = ChunkMetadata(
                chunk_id=chunk_id_start + i,
                source_page=f"{page['title']} (Part {i+1})",
                source_url=page['url'],
                chunk_type="page_split",
                word_count=len(full_content.split()),
                char_count=len(full_content),
                overlap_with_previous=0
            )
            
            chunks.append(ProcessedChunk(content=full_content, metadata=metadata))
        
        return chunks
    
    def _chunk_semantic_content(self, content_list: List[Dict[str, str]]) -> List[ProcessedChunk]:
        """Chunk content using semantic boundaries"""
        combined_content = self._combine_pages_with_separators(content_list)
        separators = self.SEPARATORS[ChunkingStrategy.SEMANTIC]
        text_chunks = self._recursive_split(combined_content, separators, self.limits["max_chars"])
        
        chunks = []
        for i, chunk_content in enumerate(text_chunks):
            metadata = ChunkMetadata(
                chunk_id=i,
                source_page="Multiple Pages",
                source_url="Multiple URLs",
                chunk_type="semantic",
                word_count=len(chunk_content.split()),
                char_count=len(chunk_content),
                overlap_with_previous=0
            )
            
            chunks.append(ProcessedChunk(content=chunk_content, metadata=metadata))
        
        return chunks
    
    def _chunk_simple_content(self, content_list: List[Dict[str, str]]) -> List[ProcessedChunk]:
        """Simple character-based chunking"""
        combined_content = self._combine_pages_with_separators(content_list)
        chunks = []
        target_size = self.limits["max_chars"]
        
        start = 0
        chunk_id = 0
        
        while start < len(combined_content):
            end = min(start + target_size, len(combined_content))
            
            # Try to break at word boundary
            if end < len(combined_content):
                last_space = combined_content.rfind(' ', start, end)
                if last_space > start:
                    end = last_space + 1
            
            chunk_content = combined_content[start:end]
            
            metadata = ChunkMetadata(
                chunk_id=chunk_id,
                source_page="Multiple Pages",
                source_url="Multiple URLs",
                chunk_type="simple",
                word_count=len(chunk_content.split()),
                char_count=len(chunk_content),
                overlap_with_previous=0
            )
            
            chunks.append(ProcessedChunk(content=chunk_content, metadata=metadata))
            
            start = end
            chunk_id += 1
        
        return chunks
    
    def _recursive_split(self, text: str, separators: List[str], max_length: int) -> List[str]:
        """Recursively split text using multiple separators"""
        if len(text) <= max_length:
            return [text]
        
        if not separators:
            # Character-level splitting (last resort)
            chunks = []
            for i in range(0, len(text), max_length):
                chunk = text[i:i + max_length]
                if chunk.strip():
                    chunks.append(chunk)
            return chunks
        
        separator = separators[0]
        remaining_separators = separators[1:]
        
        splits = text.split(separator)
        chunks = []
        current_chunk = ""
        
        for i, split in enumerate(splits):
            test_chunk = current_chunk + (separator if current_chunk else "") + split
            
            if len(test_chunk) <= max_length:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                
                if len(split) > max_length:
                    sub_chunks = self._recursive_split(split, remaining_separators, max_length)
                    chunks.extend(sub_chunks)
                    current_chunk = ""
                else:
                    current_chunk = split
        
        if current_chunk:
            chunks.append(current_chunk)
        
        return [chunk for chunk in chunks if chunk.strip()]
    
    def _create_chunk_from_content(self, content: str, pages: List[Dict[str, str]], chunk_id: int) -> ProcessedChunk:
        """Create a ProcessedChunk from content and page information"""
        if len(pages) == 1:
            source_page = pages[0]['title']
            source_url = pages[0]['url']
        else:
            source_page = f"Multiple Pages ({len(pages)} pages)"
            source_url = "Multiple URLs"
        
        metadata = ChunkMetadata(
            chunk_id=chunk_id,
            source_page=source_page,
            source_url=source_url,
            chunk_type="webpage",
            word_count=len(content.split()),
            char_count=len(content),
            overlap_with_previous=0
        )
        
        return ProcessedChunk(content=content, metadata=metadata)
    
    def get_processing_summary(self, chunks: List[ProcessedChunk]) -> Dict[str, Any]:
        """Generate a summary of the processing results"""
        total_chars = sum(chunk.metadata.char_count for chunk in chunks)
        total_words = sum(chunk.metadata.word_count for chunk in chunks)
        
        return {
            "total_chunks": len(chunks),
            "total_characters": total_chars,
            "total_words": total_words,
            "average_chunk_size": total_chars // len(chunks) if chunks else 0,
            "chunk_types": list(set(chunk.metadata.chunk_type for chunk in chunks)),
            "model_used": self.model,
            "max_limit": self.limits["max_chars"]
        } 