import os
import json
from intellicrawler.utils.logger import get_logger

# Try to import dotenv, handle gracefully if not available
try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

CONFIG_DIR = os.path.expanduser("~/.intellicrawler")
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")
ENV_FILE = os.path.join(CONFIG_DIR, ".env")
ROOT_ENV_FILE = ".env"  # Project root .env file

# Removed local get_logger to avoid circular imports

def validate_environment_variables():
    """Validate critical environment variables and provide clear error messages"""
    logger = get_logger()
    validation_results = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'info': []
    }
    
    # Check for required variables
    deepseek_key = os.getenv("DEEPSEEK_API_KEY", "").strip()
    if not deepseek_key or deepseek_key == "your_deepseek_api_key_here":
        validation_results['warnings'].append({
            'variable': 'DEEPSEEK_API_KEY',
            'message': 'DeepSeek API key not configured. AI features will be disabled.',
            'recommendation': 'Get your API key from https://platform.deepseek.com/api_keys and set it in your .env file'
        })
    elif len(deepseek_key) < 10:
        validation_results['errors'].append({
            'variable': 'DEEPSEEK_API_KEY',
            'message': 'DeepSeek API key appears invalid (too short).',
            'recommendation': 'Verify your API key from https://platform.deepseek.com/api_keys'
        })
    else:
        validation_results['info'].append({
            'variable': 'DEEPSEEK_API_KEY',
            'message': f'Valid API key configured (length: {len(deepseek_key)})'
        })
    
    # Validate numeric settings
    try:
        max_pages = int(os.getenv("MAX_PAGES_DEFAULT", "100"))
        if max_pages < 1 or max_pages > 1000:
            validation_results['warnings'].append({
                'variable': 'MAX_PAGES_DEFAULT',
                'message': f'MAX_PAGES_DEFAULT ({max_pages}) is outside recommended range (1-1000).',
                'recommendation': 'Use a value between 10-200 for optimal performance'
            })
    except ValueError:
        validation_results['errors'].append({
            'variable': 'MAX_PAGES_DEFAULT',
            'message': 'MAX_PAGES_DEFAULT must be a valid integer.',
            'recommendation': 'Set to a number like 100'
        })
    
    try:
        crawl_depth = int(os.getenv("CRAWL_DEPTH_DEFAULT", "3"))
        if crawl_depth < 1 or crawl_depth > 10:
            validation_results['warnings'].append({
                'variable': 'CRAWL_DEPTH_DEFAULT',
                'message': f'CRAWL_DEPTH_DEFAULT ({crawl_depth}) is outside recommended range (1-10).',
                'recommendation': 'Use a value between 2-5 for optimal results'
            })
    except ValueError:
        validation_results['errors'].append({
            'variable': 'CRAWL_DEPTH_DEFAULT',
            'message': 'CRAWL_DEPTH_DEFAULT must be a valid integer.',
            'recommendation': 'Set to a number like 3'
        })
    
    try:
        delay = float(os.getenv("DELAY_DEFAULT", "1.0"))
        if delay < 0.1 or delay > 10.0:
            validation_results['warnings'].append({
                'variable': 'DELAY_DEFAULT',
                'message': f'DELAY_DEFAULT ({delay}) is outside recommended range (0.1-10.0).',
                'recommendation': 'Use a value between 0.5-3.0 seconds for respectful crawling'
            })
    except ValueError:
        validation_results['errors'].append({
            'variable': 'DELAY_DEFAULT',
            'message': 'DELAY_DEFAULT must be a valid number.',
            'recommendation': 'Set to a decimal like 1.0'
        })
    
    # Check Chrome path if specified
    chrome_path = os.getenv("CHROME_PATH", "").strip()
    if chrome_path and not os.path.exists(chrome_path):
        validation_results['warnings'].append({
            'variable': 'CHROME_PATH',
            'message': f'Specified Chrome path does not exist: {chrome_path}',
            'recommendation': 'Either correct the path or leave empty for auto-detection'
        })
    
    # Log validation results
    if validation_results['errors']:
        validation_results['valid'] = False
        logger.error("Environment variable validation failed:")
        for error in validation_results['errors']:
            logger.error(f"  ❌ {error['variable']}: {error['message']} → {error['recommendation']}")
    
    if validation_results['warnings']:
        logger.warning("Environment variable warnings:")
        for warning in validation_results['warnings']:
            logger.warning(f"  ⚠️  {warning['variable']}: {warning['message']} → {warning['recommendation']}")
    
    if validation_results['info']:
        logger.info("Environment variable status:")
        for info in validation_results['info']:
            logger.info(f"  ✅ {info['variable']}: {info['message']}")
    
    return validation_results

def load_env_variables():
    """Load environment variables from .env files with validation"""
    logger = get_logger()
    
    if not DOTENV_AVAILABLE:
        logger.warning("python-dotenv not available. Install it for .env file support")
        return {}
    
    env_vars = {}
    
    # List of .env files to check
    env_files = [
        (ROOT_ENV_FILE, "project root"),
        (ENV_FILE, "user config directory")
    ]
    
    for env_file_path, location_name in env_files:
        if os.path.exists(env_file_path):
            try:
                logger.info(f"Attempting to load .env file from {location_name}: {env_file_path}")
                with open(env_file_path, 'r', encoding='utf-8-sig') as f:
                    load_dotenv(stream=f)
                logger.info(f"Successfully loaded .env file from {location_name}")
            except UnicodeDecodeError:
                logger.error(
                    f"CRITICAL: Failed to decode .env file at {env_file_path}. "
                    "The file may have an incorrect encoding (e.g., UTF-16 instead of UTF-8). "
                    "IntelliCrawler will skip loading this file. Please re-save it as UTF-8."
                )
            except Exception as e:
                logger.error(f"Failed to load .env file from {location_name} due to an unexpected error: {e}")

    # Validate environment variables
    validation = validate_environment_variables()
    
    # Get relevant environment variables with safe conversion
    try:
        env_vars = {
            "deepseek_api_key": os.getenv("DEEPSEEK_API_KEY", ""),
            "chrome_path": os.getenv("CHROME_PATH", ""),
            "user_agent": os.getenv("USER_AGENT", "IntelliCrawler/2.0 (AI-powered web analysis)"),
            "max_pages_default": max(1, min(1000, int(os.getenv("MAX_PAGES_DEFAULT", "100")))),
            "crawl_depth_default": max(1, min(10, int(os.getenv("CRAWL_DEPTH_DEFAULT", "3")))),
            "delay_default": max(0.1, min(10.0, float(os.getenv("DELAY_DEFAULT", "1.0")))),
            "respect_robots": os.getenv("RESPECT_ROBOTS", "true").lower() == "true",
            "enable_javascript": os.getenv("ENABLE_JAVASCRIPT", "true").lower() == "true",
            "request_timeout": max(5, min(120, int(os.getenv("REQUEST_TIMEOUT", "30")))),
            "max_retries": max(0, min(10, int(os.getenv("MAX_RETRIES", "3")))),
            "verbose_logging": os.getenv("VERBOSE_LOGGING", "false").lower() == "true",
            "debug_mode": os.getenv("DEBUG_MODE", "false").lower() == "true",
        }
    except (ValueError, TypeError) as e:
        logger.error(f"Error parsing environment variables: {str(e)}")
        # Fallback to safe defaults
        env_vars = {
            "deepseek_api_key": os.getenv("DEEPSEEK_API_KEY", ""),
            "chrome_path": os.getenv("CHROME_PATH", ""),
            "user_agent": "IntelliCrawler/2.0 (AI-powered web analysis)",
            "max_pages_default": 100,
            "crawl_depth_default": 3,
            "delay_default": 1.0,
            "respect_robots": True,
            "enable_javascript": True,
            "request_timeout": 30,
            "max_retries": 3,
            "verbose_logging": False,
            "debug_mode": False,
        }
    
    # Log API key status
    if env_vars["deepseek_api_key"]:
        if env_vars["deepseek_api_key"] != "your_deepseek_api_key_here":
            logger.info(f"Loaded API key from environment (length: {len(env_vars['deepseek_api_key'])})")
        else:
            logger.warning("DEEPSEEK_API_KEY is set to placeholder value - AI features will not work")
    else:
        logger.warning("DEEPSEEK_API_KEY is not set - AI features will be disabled")
    
    # Add validation results to env_vars for access by other components
    env_vars["_validation_results"] = validation
    
    return env_vars

def create_env_template():
    """Create a .env template file"""
    logger = get_logger()
    
    env_template = """# IntelliCrawler Environment Variables
# Copy this file to .env and fill in your values
# DO NOT commit .env files to version control!

# DeepSeek API Key (required for AI features)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Chrome browser path (optional, auto-detected if empty)
CHROME_PATH=

# User agent string for web requests
USER_AGENT=IntelliCrawler/2.0 (AI-powered web analysis)

# Default crawling parameters
MAX_PAGES_DEFAULT=100
CRAWL_DEPTH_DEFAULT=3
DELAY_DEFAULT=1.0
"""
    
    try:
        # Create .env template in config directory
        env_template_file = os.path.join(CONFIG_DIR, ".env.template")
        with open(env_template_file, 'w', encoding='utf-8') as f:
            f.write(env_template)
        logger.info(f"Created .env template at: {env_template_file}")
        
        # Also create in project root if we're in the project directory
        if os.path.exists("supercrawler"):  # We're in project root
            root_template = ".env.template"
            with open(root_template, 'w', encoding='utf-8') as f:
                f.write(env_template)
            logger.info(f"Created .env template at: {root_template}")
            
        return True
    except Exception as e:
        logger.error(f"Error creating .env template: {str(e)}")
        return False

def create_default_config():
    """Create default configuration file"""
    logger = get_logger()
    logger.info("Creating default configuration")
    
    # Create config directory if it doesn't exist
    os.makedirs(CONFIG_DIR, exist_ok=True)
    
    # Default configuration with enhanced settings
    default_config = {
        "deepseek_api_key": "",
        "create_shortcut": True,
        "max_pages_default": 100,
        "crawl_depth_default": 4,  # Increased from 3 to 4 for better documentation coverage
        "dynamic_default": False,
        "delay_default": 1.0,
        "user_agent": "IntelliCrawler/2.0 (AI-powered web analysis)",
        "theme": "dark",
        "export_formats": ["json", "csv", "excel", "xml", "text"],
        "report_formats": ["md", "pdf", "docx"],
        "log_level": "INFO",
        "respect_robots": True,
        "enable_javascript": True,
        "request_timeout": 30,
        "max_retries": 3,
        "verbose_logging": False,
        "debug_mode": False,
        "environment_validated": False
    }
    
    # Write default config
    with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(default_config, f, ensure_ascii=False, indent=2)
    
    # Create .env template
    create_env_template()
    
    return default_config

def load_config():
    """Load configuration from .env files and config.json (env takes priority)"""
    logger = get_logger()
    
    try:
        # Ensure config directory exists
        if not os.path.exists(CONFIG_DIR):
            logger.info(f"Creating config directory: {CONFIG_DIR}")
            os.makedirs(CONFIG_DIR, exist_ok=True)
        
        # Start with default config
        config = create_default_config() if not os.path.exists(CONFIG_FILE) else {}
        
        # Load from config.json if it exists
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8-sig') as f:
                    json_config = json.load(f)
                config.update(json_config)
                logger.info("Configuration loaded from config.json")
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON in config file: {CONFIG_FILE}")
                config = create_default_config()
            except Exception as e:
                logger.error(f"Error reading config file: {str(e)}")
                config = create_default_config()
        
        # Load environment variables (.env files take priority)
        env_vars = load_env_variables()
        if env_vars:
            # Extract validation results before merging
            validation_results = env_vars.pop("_validation_results", None)
            
            # Only override config values if env vars are not empty (except booleans)
            for key, value in env_vars.items():
                if isinstance(value, bool) or value:  # Include boolean values even if False
                    config[key] = value
            
            # Store validation results in config
            if validation_results:
                config["environment_validated"] = validation_results.get("valid", False)
                config["validation_results"] = validation_results
                
            logger.info("Environment variables loaded and merged with config")
        
        # Log final API key status with enhanced messaging
        api_key = config.get("deepseek_api_key", "")
        if api_key and api_key != "your_deepseek_api_key_here":
            logger.info(f"✅ Valid API key configured (length: {len(api_key)})")
        elif api_key == "your_deepseek_api_key_here":
            logger.warning("⚠️  API key is set to placeholder value - please update with your actual DeepSeek API key")
        else:
            logger.warning("⚠️  No API key found - AI features will be disabled until API key is configured")
            
        # Log configuration validation status
        if config.get("environment_validated", False):
            logger.info("✅ Environment configuration validation passed")
        else:
            logger.warning("⚠️  Environment configuration has issues - check logs for details")
            
        return config
        
    except Exception as e:
        logger.error(f"Error in load_config: {str(e)}")
        # Return a minimal default config
        return {
            "deepseek_api_key": "",
            "user_agent": "IntelliCrawler/2.0 (AI-powered web analysis)",
        }

def save_config(config):
    """Save configuration to file"""
    logger = get_logger()
    
    try:
        # Create config directory if it doesn't exist
        if not os.path.exists(CONFIG_DIR):
            logger.info(f"Creating config directory: {CONFIG_DIR}")
            os.makedirs(CONFIG_DIR, exist_ok=True)
        
        # Check write permissions
        try:
            test_file = os.path.join(CONFIG_DIR, ".test_write")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            logger.info("Write permission verified for config directory")
        except Exception as perm_err:
            logger.error(f"Write permission error in config directory: {str(perm_err)}")
            return False
        
        # Save API key status for debugging
        api_key = config.get("deepseek_api_key", "")
        if api_key:
            logger.info(f"Saving API key to config (length: {len(api_key)})")
        else:
            logger.warning("No API key to save in config")
            
        # Write config to file
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info(f"Configuration saved successfully to: {CONFIG_FILE}")
        
        # Verify the file was written
        if os.path.exists(CONFIG_FILE):
            return True
        else:
            logger.error(f"Config file was not created: {CONFIG_FILE}")
            return False
    except Exception as e:
        logger.error(f"Error saving config: {str(e)}")
        return False 