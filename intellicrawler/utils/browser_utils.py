import os
import logging

def find_browser_binary(preferred_path=None, is_edge=False):
    """:return path to Chrome/Edge binary, preferring provided path"""
    logger = logging.getLogger(__name__)
    
    if preferred_path and os.path.exists(preferred_path):
        logger.info(f"Using preferred browser path: {preferred_path}")
        return preferred_path
    
    common_locations = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        os.path.join(os.environ.get('LOCALAPPDATA', ''), r"Google\Chrome\Application\chrome.exe"),
        r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
    ]
    
    for location in common_locations:
        if os.path.exists(location):
            if 'edge' in location.lower() or 'msedge' in location.lower():
                is_edge = True
            logger.info(f"Found browser at: {location}")
            return location
    
    logger.warning("No browser found in common locations")
    return None 