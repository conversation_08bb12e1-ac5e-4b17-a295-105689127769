"""
Logger module for SuperCrawler
"""
import os
import logging
import traceback
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
import sys
import inspect
from datetime import datetime
import uuid

# Singleton logger instances
_logger = None
_error_logger = None

# Set up logging directory
_log_dir = os.path.expanduser("~/.intellicrawler/logs")
os.makedirs(_log_dir, exist_ok=True)

# Configure the root logger
_log_file = os.path.join(_log_dir, "supercrawler.log")
_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Configure the file handler
_file_handler = logging.FileHandler(_log_file)
_file_handler.setFormatter(_formatter)

# Configure the stream handler for console output
_stream_handler = logging.StreamHandler()
_stream_handler.setFormatter(_formatter)

def setup_logger():
    """Setup and configure the application logger"""
    global _logger
    
    if _logger is not None:
        return _logger
    
    from intellicrawler.utils.config import load_config
    config = load_config()  # Load config
    log_level = config.get('log_level', 'INFO')  # Default to INFO
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Create logs directory in user home
    log_dir = os.path.expanduser("~/.intellicrawler/logs")
    os.makedirs(log_dir, exist_ok=True)
    
    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d")
    log_file = os.path.join(log_dir, f"supercrawler_{timestamp}.log")
    
    # Setup logger
    logger = logging.getLogger("SuperCrawler")
    logger.setLevel(level)
    
    # File handler with rotation
    file_handler = RotatingFileHandler(
        log_file, maxBytes=5*1024*1024, backupCount=5, encoding="utf-8"
    )
    file_handler.setLevel(level if level == logging.DEBUG else logging.INFO)  # File always detailed
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    
    # Create formatter
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    console_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Set formatters
    file_handler.setFormatter(file_formatter)
    console_handler.setFormatter(console_formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    _logger = logger
    
    # Also set up the error logger
    setup_error_logger()
    
    return logger

def setup_error_logger():
    """Setup and configure a dedicated error logger"""
    global _error_logger
    
    if _error_logger is not None:
        return _error_logger
    
    # Create error logs directory in user home
    error_log_dir = os.path.expanduser("~/.intellicrawler/logs/errors")
    os.makedirs(error_log_dir, exist_ok=True)
    
    # Create error log filename with timestamp
    error_log_file = os.path.join(error_log_dir, "supercrawler_errors.log")
    
    # Setup error logger
    error_logger = logging.getLogger("SuperCrawler.Errors")
    error_logger.setLevel(logging.ERROR)
    
    # File handler with time-based rotation (daily)
    file_handler = TimedRotatingFileHandler(
        error_log_file, when='midnight', interval=1, backupCount=30, encoding="utf-8"
    )
    file_handler.setLevel(logging.ERROR)
    file_handler.suffix = "%Y%m%d"
    
    # Console handler for errors
    console_handler = logging.StreamHandler(sys.stderr)
    console_handler.setLevel(logging.ERROR)
    
    # Create detailed error formatter
    error_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s\n'
        '%(traceback)s\n'
        '----------------------------------------'
    )
    
    # Standard formatter for console
    console_formatter = logging.Formatter(
        '%(asctime)s - ERROR - %(message)s'
    )
    
    # Set formatters
    file_handler.setFormatter(error_formatter)
    console_handler.setFormatter(console_formatter)
    
    # Add handlers to logger
    error_logger.addHandler(file_handler)
    error_logger.addHandler(console_handler)
    
    # Make sure error logger doesn't propagate to parent
    error_logger.propagate = False
    
    _error_logger = error_logger
    return error_logger

def get_logger(name=None):
    """
    Get a logger configured with file and console handlers
    
    Args:
        name: Name of the logger (optional)
        
    Returns:
        Configured logger instance
    """
    logger_name = name if name else 'intellicrawler'
    logger = logging.getLogger(logger_name)
    
    # Only add handlers if they haven't been added yet
    if not logger.handlers:
        logger.addHandler(_file_handler)
        logger.addHandler(_stream_handler)
        logger.setLevel(logging.INFO)
    
    return logger

def get_error_logger():
    """Get or create error logger instance"""
    global _error_logger
    
    if _error_logger is None:
        return setup_error_logger()
    
    return _error_logger

def log_error(error, message=None, include_stack=True):
    """
    Log an error with detailed information
    
    Args:
        error (Exception): The exception object
        message (str, optional): Additional message to include with the error
        include_stack (bool): Whether to include the full stack trace
    
    Returns:
        str: Error ID for reference
    """
    # Generate a unique error ID
    error_id = str(uuid.uuid4())[:8]
    
    # Get error logger
    error_logger = get_error_logger()
    
    # Get calling frame info for better context
    frame = inspect.currentframe().f_back
    filename = frame.f_code.co_filename
    line_number = frame.f_lineno
    function_name = frame.f_code.co_name
    
    # Format the error message
    if message:
        error_msg = f"[{error_id}] {message}: {str(error)}"
    else:
        error_msg = f"[{error_id}] {str(error)}"
    
    # Generate traceback info
    if include_stack:
        tb_info = "".join(traceback.format_exception(type(error), error, error.__traceback__))
    else:
        tb_info = f"Exception in {filename}, line {line_number}, in {function_name}: {str(error)}"
    
    # Add error info to the log record
    extra = {
        'traceback': tb_info,
        'error_id': error_id,
        'src_filename': filename,
        'src_lineno': line_number,
        'src_function': function_name
    }
    
    # Log the error
    error_logger.error(error_msg, extra=extra)
    
    # Also log to main logger
    get_logger().error(f"Error occurred: {error_msg}. See error log for details (ID: {error_id})")
    
    return error_id

def exception_handler(func):
    """
    Decorator to automatically catch and log exceptions
    
    Usage:
        @exception_handler
        def some_function():
            # function code here
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            log_error(e, f"Exception in {func.__name__}")
            raise  # Re-raise the exception after logging
    
    return wrapper

def set_log_level(level):
    """Set the log level for all loggers
    
    Args:
        level: Logging level (e.g. logging.DEBUG, logging.INFO)
    """
    root_logger = logging.getLogger('intellicrawler')
    root_logger.setLevel(level)
    
    # Update handlers as well
    _file_handler.setLevel(level)
    _stream_handler.setLevel(level)

def log_exception(logger, exception, context=None):
    """
    Log an exception with context
    
    Args:
        logger: Logger instance
        exception: The exception to log
        context: Additional context information
    """
    msg = f"Exception: {str(exception)}"
    if context:
        msg = f"{context}: {msg}"
    
    logger.error(msg, exc_info=True)
    
def create_timestamped_log_file(prefix="log_"):
    """
    Create a new timestamped log file
    
    Args:
        prefix: Prefix for the log file name
    
    Returns:
        Path to the created log file
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(_log_dir, f"{prefix}{timestamp}.log")
    
    # Create an empty file
    with open(log_file, 'w') as f:
        f.write(f"Log created at {datetime.now().isoformat()}\n")
    
    return log_file 