"""
Data persistence manager for IntelliCrawler
Handles session data storage, export functionality, and data management
"""

import os
import json
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
import sqlite3
import pickle

from intellicrawler.utils.logger import get_logger
from intellicrawler.utils.config import load_config

class DataPersistenceManager:
    """Manages data persistence for IntelliCrawler sessions and exports"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.config = load_config()
        
        # Set up data directories
        self.data_dir = os.path.expanduser("~/.intellicrawler/data")
        self.sessions_dir = os.path.join(self.data_dir, "sessions")
        self.exports_dir = os.path.join(self.data_dir, "exports")
        
        # Ensure directories exist
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.sessions_dir, exist_ok=True)
        os.makedirs(self.exports_dir, exist_ok=True)
        
        # Initialize database
        self.db_path = os.path.join(self.data_dir, "intellicrawler.db")
        self._init_database()
    
    def _init_database(self):
        """Initialize the SQLite database for session management"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create sessions table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sessions (
                        id TEXT PRIMARY KEY,
                        name TEXT,
                        created_at TEXT,
                        updated_at TEXT,
                        url TEXT,
                        pages_count INTEGER,
                        total_content_size INTEGER,
                        metadata TEXT,
                        data_file_path TEXT
                    )
                """)
                
                # Create exports table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS exports (
                        id TEXT PRIMARY KEY,
                        session_id TEXT,
                        export_type TEXT,
                        created_at TEXT,
                        file_path TEXT,
                        file_size INTEGER,
                        metadata TEXT,
                        FOREIGN KEY (session_id) REFERENCES sessions (id)
                    )
                """)
                
                conn.commit()
                self.logger.info("Database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {str(e)}")
    
    def save_session_data(self, data: List[Dict[str, Any]], session_name: str = None, 
                         url: str = None, metadata: Dict[str, Any] = None) -> str:
        """
        Save session data with metadata
        
        Args:
            data: List of scraped page data
            session_name: Optional name for the session
            url: Starting URL for the session
            metadata: Additional metadata
            
        Returns:
            Session ID
        """
        try:
            session_id = str(uuid.uuid4())
            timestamp = datetime.now().isoformat()
            
            # Generate session name if not provided
            if not session_name:
                session_name = f"Crawl Session {timestamp[:19].replace(':', '-')}"
            
            # Calculate statistics
            pages_count = len(data)
            total_content_size = sum(len(item.get('content', '')) for item in data)
            
            # Prepare metadata
            session_metadata = {
                'pages_count': pages_count,
                'total_content_size': total_content_size,
                'average_page_size': total_content_size / pages_count if pages_count > 0 else 0,
                'crawl_duration': metadata.get('crawl_duration', 0) if metadata else 0,
                'crawler_settings': metadata.get('crawler_settings', {}) if metadata else {},
                'ai_analysis_enabled': metadata.get('ai_analysis_enabled', False) if metadata else False
            }
            
            # Save data to file
            data_filename = f"session_{session_id}.json"
            data_file_path = os.path.join(self.sessions_dir, data_filename)
            
            session_data = {
                'session_id': session_id,
                'name': session_name,
                'created_at': timestamp,
                'updated_at': timestamp,
                'url': url,
                'data': data,
                'metadata': session_metadata
            }
            
            with open(data_file_path, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            # Save to database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO sessions 
                    (id, name, created_at, updated_at, url, pages_count, total_content_size, metadata, data_file_path)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session_id, session_name, timestamp, timestamp, url,
                    pages_count, total_content_size,
                    json.dumps(session_metadata), data_file_path
                ))
                conn.commit()
            
            self.logger.info(f"Session saved: {session_id} ({pages_count} pages)")
            return session_id
            
        except Exception as e:
            self.logger.error(f"Failed to save session data: {str(e)}")
            raise
    
    def load_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Load session data by ID
        
        Args:
            session_id: Session ID to load
            
        Returns:
            Session data dictionary or None if not found
        """
        try:
            # First try to get file path from database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT data_file_path FROM sessions WHERE id = ?", (session_id,))
                result = cursor.fetchone()
                
                if not result:
                    self.logger.warning(f"Session {session_id} not found in database")
                    return None
                
                data_file_path = result[0]
            
            # Load data from file
            if os.path.exists(data_file_path):
                with open(data_file_path, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                
                self.logger.info(f"Session loaded: {session_id}")
                return session_data
            else:
                self.logger.error(f"Session data file not found: {data_file_path}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to load session {session_id}: {str(e)}")
            return None
    
    def list_sessions(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        List available sessions
        
        Args:
            limit: Maximum number of sessions to return
            
        Returns:
            List of session information dictionaries
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, name, created_at, updated_at, url, pages_count, total_content_size, metadata
                    FROM sessions
                    ORDER BY created_at DESC
                    LIMIT ?
                """, (limit,))
                
                sessions = []
                for row in cursor.fetchall():
                    session_info = {
                        'id': row[0],
                        'name': row[1],
                        'created_at': row[2],
                        'updated_at': row[3],
                        'url': row[4],
                        'pages_count': row[5],
                        'total_content_size': row[6],
                        'metadata': json.loads(row[7]) if row[7] else {}
                    }
                    sessions.append(session_info)
                
                return sessions
                
        except Exception as e:
            self.logger.error(f"Failed to list sessions: {str(e)}")
            return []
    
    def delete_session(self, session_id: str) -> bool:
        """
        Delete a session and its data
        
        Args:
            session_id: Session ID to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get file path before deleting from database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT data_file_path FROM sessions WHERE id = ?", (session_id,))
                result = cursor.fetchone()
                
                if result:
                    data_file_path = result[0]
                    
                    # Delete data file
                    if os.path.exists(data_file_path):
                        os.remove(data_file_path)
                    
                    # Delete from database
                    cursor.execute("DELETE FROM sessions WHERE id = ?", (session_id,))
                    cursor.execute("DELETE FROM exports WHERE session_id = ?", (session_id,))
                    conn.commit()
                    
                    self.logger.info(f"Session deleted: {session_id}")
                    return True
                else:
                    self.logger.warning(f"Session {session_id} not found for deletion")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Failed to delete session {session_id}: {str(e)}")
            return False
    
    def export_session(self, session_id: str, export_type: str = 'json', 
                      export_path: str = None) -> Optional[str]:
        """
        Export session data in various formats
        
        Args:
            session_id: Session ID to export
            export_type: Type of export ('json', 'csv', 'markdown', 'html')
            export_path: Optional custom export path
            
        Returns:
            Path to exported file or None if failed
        """
        try:
            # Load session data
            session_data = self.load_session_data(session_id)
            if not session_data:
                return None
            
            # Generate export filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_name = session_data.get('name', 'session').replace(' ', '_').replace('/', '_')
            
            if export_type == 'json':
                filename = f"{session_name}_{timestamp}.json"
                content = json.dumps(session_data, indent=2, ensure_ascii=False)
                
            elif export_type == 'csv':
                filename = f"{session_name}_{timestamp}.csv"
                content = self._convert_to_csv(session_data['data'])
                
            elif export_type == 'markdown':
                filename = f"{session_name}_{timestamp}.md"
                content = self._convert_to_markdown(session_data)
                
            elif export_type == 'html':
                filename = f"{session_name}_{timestamp}.html"
                content = self._convert_to_html(session_data)
                
            else:
                raise ValueError(f"Unsupported export type: {export_type}")
            
            # Determine export path
            if export_path:
                export_file_path = export_path
            else:
                export_file_path = os.path.join(self.exports_dir, filename)
            
            # Write export file
            with open(export_file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Record export in database
            export_id = str(uuid.uuid4())
            file_size = os.path.getsize(export_file_path)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO exports 
                    (id, session_id, export_type, created_at, file_path, file_size, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    export_id, session_id, export_type, datetime.now().isoformat(),
                    export_file_path, file_size, json.dumps({'filename': filename})
                ))
                conn.commit()
            
            self.logger.info(f"Session exported: {session_id} -> {export_file_path}")
            return export_file_path
            
        except Exception as e:
            self.logger.error(f"Failed to export session {session_id}: {str(e)}")
            return None
    
    def _convert_to_csv(self, data: List[Dict[str, Any]]) -> str:
        """Convert session data to CSV format"""
        import csv
        import io
        
        if not data:
            return "No data to export"
        
        output = io.StringIO()
        
        # Get all unique keys from all items
        all_keys = set()
        for item in data:
            all_keys.update(item.keys())
        
        # Sort keys for consistent column order
        sorted_keys = sorted(all_keys)
        
        writer = csv.DictWriter(output, fieldnames=sorted_keys)
        writer.writeheader()
        
        for item in data:
            # Convert complex values to strings
            row = {}
            for key in sorted_keys:
                value = item.get(key, '')
                if isinstance(value, (dict, list)):
                    value = json.dumps(value, ensure_ascii=False)
                elif value is None:
                    value = ''
                else:
                    value = str(value)
                row[key] = value
            writer.writerow(row)
        
        return output.getvalue()
    
    def _convert_to_markdown(self, session_data: Dict[str, Any]) -> str:
        """Convert session data to Markdown format"""
        md_content = []
        
        # Header
        md_content.append(f"# {session_data.get('name', 'Crawl Session')}")
        md_content.append("")
        
        # Metadata
        md_content.append("## Session Information")
        md_content.append("")
        md_content.append(f"- **Session ID:** {session_data.get('session_id', 'Unknown')}")
        md_content.append(f"- **Created:** {session_data.get('created_at', 'Unknown')}")
        md_content.append(f"- **Starting URL:** {session_data.get('url', 'Unknown')}")
        md_content.append(f"- **Pages Crawled:** {len(session_data.get('data', []))}")
        
        metadata = session_data.get('metadata', {})
        if metadata:
            md_content.append(f"- **Total Content Size:** {metadata.get('total_content_size', 0):,} characters")
            md_content.append(f"- **Average Page Size:** {metadata.get('average_page_size', 0):,} characters")
        
        md_content.append("")
        
        # Table of Contents
        data = session_data.get('data', [])
        if data:
            md_content.append("## Table of Contents")
            md_content.append("")
            for i, item in enumerate(data, 1):
                title = item.get('title', f'Page {i}')
                clean_title = title.replace('(', '').replace(')', '').replace('[', '').replace(']', '')
                md_content.append(f"{i}. [{title}](#{clean_title.lower().replace(' ', '-').replace('/', '-')})")
            md_content.append("")
            
            # Page content
            md_content.append("## Crawled Content")
            md_content.append("")
            
            for i, item in enumerate(data, 1):
                title = item.get('title', f'Page {i}')
                url = item.get('url', 'Unknown URL')
                content = item.get('content', 'No content available')
                
                md_content.append(f"### {i}. {title}")
                md_content.append("")
                md_content.append(f"**Source:** [{url}]({url})")
                md_content.append("")
                md_content.append(content)
                md_content.append("")
                md_content.append("---")
                md_content.append("")
        
        return "\n".join(md_content)
    
    def _convert_to_html(self, session_data: Dict[str, Any]) -> str:
        """Convert session data to HTML format"""
        html_content = []
        
        # HTML header
        html_content.append("<!DOCTYPE html>")
        html_content.append("<html lang='en'>")
        html_content.append("<head>")
        html_content.append("    <meta charset='UTF-8'>")
        html_content.append("    <meta name='viewport' content='width=device-width, initial-scale=1.0'>")
        html_content.append(f"    <title>{session_data.get('name', 'Crawl Session')}</title>")
        html_content.append("    <style>")
        html_content.append("        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }")
        html_content.append("        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 30px; }")
        html_content.append("        .page { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 5px; }")
        html_content.append("        .page-title { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }")
        html_content.append("        .page-url { color: #666; font-size: 14px; margin-bottom: 15px; }")
        html_content.append("        .toc { background: #f9f9f9; padding: 20px; border-radius: 5px; margin-bottom: 30px; }")
        html_content.append("        .toc ul { list-style-type: none; padding-left: 0; }")
        html_content.append("        .toc li { margin: 5px 0; }")
        html_content.append("        .toc a { text-decoration: none; color: #007cba; }")
        html_content.append("        .toc a:hover { text-decoration: underline; }")
        html_content.append("    </style>")
        html_content.append("</head>")
        html_content.append("<body>")
        
        # Header
        html_content.append("    <div class='header'>")
        html_content.append(f"        <h1>{session_data.get('name', 'Crawl Session')}</h1>")
        html_content.append(f"        <p><strong>Session ID:</strong> {session_data.get('session_id', 'Unknown')}</p>")
        html_content.append(f"        <p><strong>Created:</strong> {session_data.get('created_at', 'Unknown')}</p>")
        html_content.append(f"        <p><strong>Starting URL:</strong> <a href='{session_data.get('url', '#')}'>{session_data.get('url', 'Unknown')}</a></p>")
        html_content.append(f"        <p><strong>Pages Crawled:</strong> {len(session_data.get('data', []))}</p>")
        html_content.append("    </div>")
        
        # Table of Contents
        data = session_data.get('data', [])
        if data:
            html_content.append("    <div class='toc'>")
            html_content.append("        <h2>Table of Contents</h2>")
            html_content.append("        <ul>")
            for i, item in enumerate(data, 1):
                title = item.get('title', f'Page {i}')
                anchor = f"page-{i}"
                html_content.append(f"            <li><a href='#{anchor}'>{i}. {title}</a></li>")
            html_content.append("        </ul>")
            html_content.append("    </div>")
            
            # Page content
            for i, item in enumerate(data, 1):
                title = item.get('title', f'Page {i}')
                url = item.get('url', 'Unknown URL')
                content = item.get('content', 'No content available')
                anchor = f"page-{i}"
                
                html_content.append(f"    <div class='page' id='{anchor}'>")
                html_content.append(f"        <h2 class='page-title'>{i}. {title}</h2>")
                html_content.append(f"        <p class='page-url'><strong>Source:</strong> <a href='{url}'>{url}</a></p>")
                html_content.append(f"        <div class='page-content'>{content.replace(chr(10), '<br>')}</div>")
                html_content.append("    </div>")
        
        # HTML footer
        html_content.append("</body>")
        html_content.append("</html>")
        
        return "\n".join(html_content)
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """Get overall statistics for all sessions"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Total sessions
                cursor.execute("SELECT COUNT(*) FROM sessions")
                total_sessions = cursor.fetchone()[0]
                
                # Total pages
                cursor.execute("SELECT SUM(pages_count) FROM sessions")
                total_pages = cursor.fetchone()[0] or 0
                
                # Total content size
                cursor.execute("SELECT SUM(total_content_size) FROM sessions")
                total_content_size = cursor.fetchone()[0] or 0
                
                # Recent activity
                cursor.execute("""
                    SELECT created_at FROM sessions 
                    ORDER BY created_at DESC 
                    LIMIT 1
                """)
                last_session = cursor.fetchone()
                last_session_date = last_session[0] if last_session else None
                
                return {
                    'total_sessions': total_sessions,
                    'total_pages': total_pages,
                    'total_content_size': total_content_size,
                    'last_session_date': last_session_date,
                    'data_directory': self.data_dir
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get session statistics: {str(e)}")
            return {}
    
    def cleanup_old_sessions(self, days_old: int = 30) -> int:
        """
        Clean up sessions older than specified number of days
        
        Args:
            days_old: Age in days to delete sessions
            
        Returns:
            Number of sessions deleted
        """
        try:
            cutoff_date = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get old sessions
                cursor.execute("""
                    SELECT id, data_file_path FROM sessions 
                    WHERE strftime('%s', created_at) < ?
                """, (cutoff_date,))
                
                old_sessions = cursor.fetchall()
                deleted_count = 0
                
                for session_id, data_file_path in old_sessions:
                    try:
                        # Delete data file
                        if os.path.exists(data_file_path):
                            os.remove(data_file_path)
                        
                        # Delete from database
                        cursor.execute("DELETE FROM sessions WHERE id = ?", (session_id,))
                        cursor.execute("DELETE FROM exports WHERE session_id = ?", (session_id,))
                        deleted_count += 1
                        
                    except Exception as e:
                        self.logger.error(f"Failed to delete session {session_id}: {str(e)}")
                
                conn.commit()
                
                if deleted_count > 0:
                    self.logger.info(f"Cleaned up {deleted_count} old sessions (older than {days_old} days)")
                
                return deleted_count
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup old sessions: {str(e)}")
            return 0
