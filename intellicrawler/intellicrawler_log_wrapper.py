#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IntelliCrawler Log Wrapper

This script wraps the IntelliCrawler launcher with comprehensive logging
to help debug issues when the launcher appears to do nothing.
"""

import os
import sys
import logging
import traceback
import subprocess
import platform
from datetime import datetime
import time

# Setup logging directory
log_dir = os.path.expanduser("~/.intellicrawler/logs")
os.makedirs(log_dir, exist_ok=True)

# Create a timestamped log file name
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = os.path.join(log_dir, f"intellicrawler_launcher_{timestamp}.log")

# Configure the root logger
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

# Get the logger for this module
logger = logging.getLogger("IntelliCrawlerLogWrapper")

# Create a class to capture and redirect stdout/stderr
class OutputRedirector:
    """
    Class to redirect stdout and stderr to both console and log file
    """
    def __init__(self, stream_name):
        self.terminal = getattr(sys, stream_name)
        self.stream_name = stream_name
        self.log_level = logging.ERROR if stream_name == 'stderr' else logging.INFO
    
    def write(self, message):
        # Write to both terminal and log
        if message and not message.isspace():
            self.terminal.write(message)
            self.terminal.flush()
            
            # Remove trailing newlines for cleaner logging
            msg = message.rstrip()
            if msg:
                logger.log(self.log_level, f"[{self.stream_name.upper()}] {msg}")
    
    def flush(self):
        self.terminal.flush()


def log_system_info():
    """Log detailed system information for debugging"""
    logger.info("=" * 80)
    logger.info("SUPERCRAWLER LOG WRAPPER STARTED")
    logger.info("=" * 80)
    logger.info(f"Log file: {log_file}")
    logger.info(f"Current time: {datetime.now().isoformat()}")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Platform: {platform.platform()}")
    logger.info(f"Operating system: {platform.system()} {platform.release()}")
    logger.info(f"Current directory: {os.getcwd()}")
    
    # Log environment variables that might be relevant
    env_vars = ["PATH", "PYTHONPATH", "VIRTUAL_ENV", "HOME", "USERPROFILE"]
    for var in env_vars:
        if var in os.environ:
            logger.info(f"Environment {var}: {os.environ.get(var)}")
    
    # Check if the launcher script exists
    launcher_path = os.path.join(os.getcwd(), "intellicrawler_launcher.py")
    if os.path.exists(launcher_path):
        logger.info(f"Launcher found at: {launcher_path}")
        logger.info(f"Launcher size: {os.path.getsize(launcher_path)} bytes")
        logger.info(f"Launcher modified: {datetime.fromtimestamp(os.path.getmtime(launcher_path))}")
    else:
        logger.warning(f"Launcher not found at: {launcher_path}")
        # Try to find the launcher elsewhere
        for root, dirs, files in os.walk(os.getcwd()):
            if "intellicrawler_launcher.py" in files:
                alt_path = os.path.join(root, "intellicrawler_launcher.py")
                logger.info(f"Alternative launcher found at: {alt_path}")
                launcher_path = alt_path
                break
    
    # Log installed packages
    try:
        logger.info("Checking installed packages...")
        result = subprocess.run([sys.executable, "-m", "pip", "freeze"], 
                               capture_output=True, text=True, check=False)
        if result.returncode == 0:
            packages = result.stdout.strip().split('\n')
            relevant_packages = [pkg for pkg in packages if any(name in pkg.lower() for name 
                                in ['pyqt', 'requests', 'beautifulsoup', 'openai', 'numpy', 'pandas', 'selenium'])]
            if relevant_packages:
                logger.info("Relevant installed packages:")
                for pkg in relevant_packages:
                    logger.info(f"  - {pkg}")
    except Exception as e:
        logger.error(f"Error checking installed packages: {str(e)}")
    
    logger.info("=" * 80)


def run_launcher_with_logging():
    """Run the IntelliCrawler launcher with comprehensive logging"""
    launcher_path = "intellicrawler_launcher.py"

    # If launcher is not in current directory, try to find it
    if not os.path.exists(launcher_path):
        for root, _, files in os.walk(os.getcwd()):
            if "intellicrawler_launcher.py" in files:
                launcher_path = os.path.join(root, "intellicrawler_launcher.py")
                break

        if not os.path.exists(launcher_path):
            logger.error("Could not find intellicrawler_launcher.py")
            return 1
    
    logger.info(f"Running launcher: {launcher_path}")
    
    try:
        # Create a subprocess and capture output in real-time
        logger.info("Starting subprocess...")
        
        # Start the subprocess
        process = subprocess.Popen(
            [sys.executable, launcher_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,  # Line buffered
            universal_newlines=True
        )
        
        logger.info(f"Subprocess started with PID: {process.pid}")
        
        # Monitor stdout and stderr in real-time
        while True:
            # Check if process has terminated
            if process.poll() is not None:
                break
                
            # Read from stdout (non-blocking)
            stdout_line = process.stdout.readline()
            if stdout_line:
                logger.info(f"[LAUNCHER] {stdout_line.rstrip()}")
            
            # Read from stderr (non-blocking)
            stderr_line = process.stderr.readline()
            if stderr_line:
                logger.error(f"[LAUNCHER-ERROR] {stderr_line.rstrip()}")
            
            # Small sleep to prevent high CPU usage
            if not stdout_line and not stderr_line:
                time.sleep(0.1)
        
        # Process any remaining output
        remaining_stdout, remaining_stderr = process.communicate()
        
        if remaining_stdout:
            for line in remaining_stdout.splitlines():
                if line.strip():
                    logger.info(f"[LAUNCHER] {line.rstrip()}")
        
        if remaining_stderr:
            for line in remaining_stderr.splitlines():
                if line.strip():
                    logger.error(f"[LAUNCHER-ERROR] {line.rstrip()}")
        
        # Get the return code
        return_code = process.returncode
        logger.info(f"Launcher process completed with exit code: {return_code}")
        
        return return_code
        
    except Exception as e:
        logger.error(f"Error running launcher: {str(e)}")
        logger.error(traceback.format_exc())
        return 1


def main():
    """Main function"""
    try:
        # Redirect stdout and stderr
        sys.stdout = OutputRedirector('stdout')
        sys.stderr = OutputRedirector('stderr')
        
        # Log system information
        log_system_info()
        
        # Run the launcher with logging
        logger.info("Starting IntelliCrawler launcher with logging wrapper")
        exit_code = run_launcher_with_logging()

        # Log completion
        if exit_code == 0:
            logger.info("IntelliCrawler launcher completed successfully")
        else:
            logger.error(f"IntelliCrawler launcher exited with code: {exit_code}")
        
        logger.info(f"Log file has been saved to: {log_file}")
        print(f"\nLog file has been saved to: {log_file}")
        
        return exit_code
        
    except Exception as e:
        logger.critical(f"Critical error in log wrapper: {str(e)}")
        logger.critical(traceback.format_exc())
        print(f"\nA critical error occurred in the log wrapper. See log at: {log_file}")
        return 1
    finally:
        # Restore original stdout and stderr
        sys.stdout = sys.__stdout__
        sys.stderr = sys.__stderr__
        
        # Keep console open on Windows if there was an error
        if platform.system() == "Windows" and 'exit_code' in locals() and exit_code != 0:
            input("\nPress Enter to exit...")


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
