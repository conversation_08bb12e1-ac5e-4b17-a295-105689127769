#!/usr/bin/env python3
"""
IntelliCrawler AI Analysis Tab
Enhanced analysis functionality with multiple output formats
"""

import os
import json
import tempfile
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QPushButton, QTextEdit,
                            QGroupBox, QSlider, QComboBox, QFormLayout,
                            QRadioButton, QButtonGroup, QListWidget,
                            QListWidgetItem, QSplitter, QProgressBar,
                            QTabWidget, QFileDialog, QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont

from intellicrawler.utils.error_handler import try_except_with_dialog
from intellicrawler.utils.logger import get_logger

class AnalysisWorker(QThread):
    """Worker thread for running analysis without blocking UI"""
    progress_updated = pyqtSignal(int, str)
    analysis_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, ai_instance, content, analysis_type, model, temperature, **kwargs):
        super().__init__()
        self.ai = ai_instance
        self.content = content
        self.analysis_type = analysis_type
        self.model = model
        self.temperature = temperature
        self.kwargs = kwargs
        
    def run(self):
        """Execute analysis in background thread"""
        try:
            self.progress_updated.emit(25, "Initializing AI analysis...")
            
            if self.analysis_type == "summary_generation":
                # Use our new summary generation functionality
                result = self._generate_enhanced_summary()
            elif self.analysis_type == "document_enhancement":
                # Use document enhancement functionality
                result = self._run_ai_analysis()
            else:
                # Use traditional AI analysis
                result = self._run_ai_analysis()
                
            self.progress_updated.emit(100, "Analysis completed")
            self.analysis_completed.emit(result)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
            
    def _generate_enhanced_summary(self):
        """Generate enhanced summary using our new functionality"""
        from intellicrawler.utils.summary_generator import CrawlerSummaryGenerator
        
        # Create temporary JSON file with the content
        temp_data = []
        if isinstance(self.content, list):
            temp_data = self.content
        else:
            # Single content item
            temp_data = [{
                "content": self.content,
                "depth": 0,
                "html": "",
                "url": "Combined Content"
            }]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(temp_data, f, indent=2, ensure_ascii=False)
            temp_file = f.name
            
        try:
            self.progress_updated.emit(50, "Generating summary...")
            
            # Use our summary generator
            generator = CrawlerSummaryGenerator(temp_file)
            generator.load_data()
            
            self.progress_updated.emit(75, "Creating human-readable summary...")
            human_summary = generator.generate_human_summary()
            
            self.progress_updated.emit(85, "Creating AI-optimized format...")
            ai_json = generator.generate_ai_optimized_json()
            
            return {
                "type": "enhanced_summary",
                "human_summary": human_summary,
                "ai_optimized": ai_json,
                "timestamp": datetime.now().isoformat(),
                "status": "success"
            }
            
        finally:
            # Clean up temp file
            if os.path.exists(temp_file):
                os.unlink(temp_file)
                
    def _run_ai_analysis(self):
        """Run traditional AI analysis using the new integrated chunking system"""
        self.progress_updated.emit(50, f"Running {self.analysis_type} analysis...")

        if not self.ai:
            raise Exception("AI service not available - no AI instance provided")

        # Check API key before attempting analysis
        if not self.ai.is_api_key_set():
            raise Exception("AI service not available - DeepSeek API key not configured")

        # The AI integration now handles chunking automatically
        # Pass the content as-is (list or string) - the AI will handle it properly
        result = self.ai.analyze_content(
            self.content,
            self.model,
            self.analysis_type,
            self.temperature
        )
        
        # Special handling for document enhancement to preserve all details
        if self.analysis_type == "document_enhancement" and result:
            # For document enhancement, return the full result with all details
            enhanced_result = {
                "type": self.analysis_type,
                "result": result.get('content', 'No enhanced content available'),
                "timestamp": result.get('timestamp', datetime.now().isoformat()),
                "status": "success" if result.get('enhancement_status') == 'success' else "failed",
                "enhancement_status": result.get('enhancement_status', 'unknown'),
                "enhancement_details": result.get('enhancement_details', {}),
                "source_urls": result.get('source_urls', []),
                "topics_researched": result.get('topics_researched', [])
            }
            return enhanced_result
        else:
            # Traditional analysis format
            return {
                "type": self.analysis_type,
                "result": result.get('content', 'No result') if result else 'Analysis failed',
                "timestamp": datetime.now().isoformat(),
                "status": "success" if result else "failed",
                "chunking_info": result.get('chunking_info') if result else None
            }

class AnalysisTab(QWidget):
    """Enhanced AI Analysis tab with multiple analysis types and output formats"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.logger = get_logger()
        self.crawled_data = []
        self.last_analysis_result = None
        self.ai = None
        self.analysis_worker = None
        
        # Auto-refresh timer for detecting new crawl data
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.check_for_new_crawl_data)
        self.auto_refresh_timer.start(3000)  # Check every 3 seconds
        
        # Workflow tracking
        self.workflow_step = 0
        self.last_crawl_count = 0
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the enhanced user interface"""
        layout = QVBoxLayout(self)
        
        # Header section
        self._setup_header(layout)
        
        # Main content area with splitter
        self._setup_main_content(layout)
        
    def _setup_header(self, layout):
        """Setup enhanced header with workflow integration"""
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        
        # Title and status row
        title_layout = QHBoxLayout()
        
        # Title
        title_label = QLabel("🤖 AI Analysis & Reports")
        title_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #2E86C1;")
        title_layout.addWidget(title_label)
        
        # Integration status indicator
        self.integration_status = QLabel("🔗 Ready for crawler data...")
        self.integration_status.setStyleSheet("""
            QLabel {
                background-color: #FFF3E0;
                color: #E65100;
                padding: 6px 12px;
                border-radius: 4px;
                border: 1px solid #FFB74D;
                font-weight: bold;
                font-size: 11px;
            }
        """)
        title_layout.addWidget(self.integration_status)
        
        title_layout.addStretch()
        
        # Quick action buttons
        self.quick_summary_btn = QPushButton("🚀 Quick Summary")
        self.quick_summary_btn.setToolTip("Generate instant summary of all crawled content")
        self.quick_summary_btn.clicked.connect(self.quick_summary)
        self.quick_summary_btn.setEnabled(False)
        self.quick_summary_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #45a049; }
            QPushButton:disabled { background-color: #CCCCCC; }
        """)
        title_layout.addWidget(self.quick_summary_btn)
        
        self.auto_analyze_btn = QPushButton("⚡ Auto-Analyze")
        self.auto_analyze_btn.setToolTip("Automatically choose and run the best analysis type")
        self.auto_analyze_btn.clicked.connect(self.auto_analyze)
        self.auto_analyze_btn.setEnabled(False)
        self.auto_analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #7B1FA2; }
            QPushButton:disabled { background-color: #CCCCCC; }
        """)
        title_layout.addWidget(self.auto_analyze_btn)
        
        header_layout.addLayout(title_layout)
        
        # Workflow guide
        self.workflow_guide = QLabel("📋 Workflow: 1) Crawl content → 2) Review data → 3) Choose analysis → 4) Export results")
        self.workflow_guide.setStyleSheet("""
            QLabel {
                background-color: #E3F2FD;
                color: #1976D2;
                padding: 8px;
                border-radius: 4px;
                border-left: 4px solid #2196F3;
                font-weight: bold;
                margin: 4px 0px;
            }
        """)
        header_layout.addWidget(self.workflow_guide)
        
        # Data status with better styling
        self.data_status = QLabel("📊 Ready for analysis - Load data using the options below")
        self.data_status.setStyleSheet("""
            QLabel {
                background-color: #FFF3E0;
                color: #F57C00;
                padding: 8px;
                border-radius: 4px;
                border-left: 4px solid #FF9800;
                font-weight: bold;
            }
        """)
        header_layout.addWidget(self.data_status)
        
        # Smart recommendations panel
        self.smart_recommendations = QLabel("")
        self.smart_recommendations.setWordWrap(True)
        self.smart_recommendations.setStyleSheet("""
            QLabel {
                background-color: #E8F5E8;
                color: #2E7D32;
                padding: 8px;
                border-radius: 4px;
                border-left: 4px solid #4CAF50;
                font-weight: bold;
                margin: 4px 0px;
            }
        """)
        self.smart_recommendations.setVisible(False)
        header_layout.addWidget(self.smart_recommendations)
        
        layout.addWidget(header_widget)
        
    def _setup_main_content(self, layout):
        """Setup main content area with splitter"""
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Analysis options
        self._setup_options_panel(splitter)
        
        # Right panel - Results
        self._setup_results_panel(splitter)
        
        # Set splitter proportions (30% options, 70% results)
        splitter.setSizes([300, 700])
        
        layout.addWidget(splitter)
        
        # Load saved crawls after UI is fully set up
        self.refresh_saved_crawls()
        
    def _setup_options_panel(self, splitter):
        """Setup left options panel"""
        options_widget = QWidget()
        options_layout = QVBoxLayout(options_widget)
        
        # File Loader Group (NEW)
        file_group = QGroupBox("📁 Project Exports & Saved Data")
        file_layout = QVBoxLayout()
        
        # Project directory info
        try:
            project_root = self.get_project_directory()
            project_name = os.path.basename(project_root)
            project_info = QLabel(f"📂 Looking in: {project_name}/exports/ (JSON, Documentation, Markdown)")
            project_info.setStyleSheet("color: #7F8C8D; font-size: 10px; font-style: italic; margin: 2px 0px;")
            project_info.setWordWrap(True)
            file_layout.addWidget(project_info)
        except Exception as e:
            self.logger.debug(f"Could not show project info: {e}")
        
        # Saved crawls dropdown
        saved_layout = QHBoxLayout()
        self.saved_crawls_combo = QComboBox()
        self.saved_crawls_combo.addItem("Select a saved crawl...")
        saved_layout.addWidget(self.saved_crawls_combo)
        
        self.refresh_saved_btn = QPushButton("🔄")
        self.refresh_saved_btn.setToolTip("Refresh saved crawls list")
        self.refresh_saved_btn.setMaximumWidth(30)
        self.refresh_saved_btn.clicked.connect(self.refresh_saved_crawls)
        saved_layout.addWidget(self.refresh_saved_btn)
        
        file_layout.addLayout(saved_layout)
        
        # Load buttons
        load_layout = QHBoxLayout()
        self.load_saved_btn = QPushButton("📂 Load Selected")
        self.load_saved_btn.clicked.connect(self.load_selected_crawl)
        self.load_saved_btn.setEnabled(False)
        load_layout.addWidget(self.load_saved_btn)
        
        self.browse_file_btn = QPushButton("📁 Browse...")
        self.browse_file_btn.clicked.connect(self.browse_for_file)
        load_layout.addWidget(self.browse_file_btn)
        
        file_layout.addLayout(load_layout)
        
        # Connect combo box selection
        self.saved_crawls_combo.currentTextChanged.connect(self.on_saved_crawl_selected)
        
        file_group.setLayout(file_layout)
        options_layout.addWidget(file_group)

        # Analysis Configuration Group
        config_group = QGroupBox("📊 Analysis Configuration")
        config_layout = QVBoxLayout()
        
        # Model selection
        model_layout = QFormLayout()
        self.model_selector = QComboBox()
        self.model_selector.addItems([
            "deepseek-reasoner (R1) - Best for detailed analysis",
            "deepseek-chat (V3) - Faster, more concise analysis"
        ])
        self.model_selector.setCurrentIndex(0)  # Default to reasoner
        model_layout.addRow("🤖 AI Model:", self.model_selector)
        config_layout.addLayout(model_layout)
        
        # Temperature setting
        temp_layout = QFormLayout()
        temp_container = QHBoxLayout()
        self.temperature_slider = QSlider(Qt.Horizontal)
        self.temperature_slider.setRange(1, 10)
        self.temperature_slider.setValue(5)  # Default 0.5
        self.temperature_slider.setTickPosition(QSlider.TicksBelow)
        self.temperature_slider.setTickInterval(1)
        temp_container.addWidget(self.temperature_slider)
        
        self.temp_value_label = QLabel("0.5")
        self.temp_value_label.setMinimumWidth(30)
        temp_container.addWidget(self.temp_value_label)
        
        self.temperature_slider.valueChanged.connect(self.update_temp_label)
        temp_layout.addRow("🌡️ Temperature:", temp_container)
        config_layout.addLayout(temp_layout)
        
        config_group.setLayout(config_layout)
        options_layout.addWidget(config_group)

        # Analysis Type Group
        type_group = QGroupBox("🔍 Analysis Type")
        type_layout = QVBoxLayout()
        
        self.analysis_types = QButtonGroup()
        
        analysis_options = [
            ("document_enhancement", "🚀 Document Enhancement", "Intelligently enhance documentation by following links and web searches"),
            ("summary_generation", "📋 Enhanced Summary", "Generate comprehensive human & AI-readable summaries"),
            ("summary", "📝 AI Summary", "Generate a concise summary using AI"),
            ("key_points", "🔑 Key Points", "Extract the most important points"),
            ("sentiment", "😊 Sentiment Analysis", "Analyze emotional tone and sentiment"),
            ("entities", "🏷️ Entity Extraction", "Identify people, places, organizations"),
            ("json", "🗂️ Structured Data (JSON)", "Extract structured data"),
            ("detailed_report", "📊 Detailed Report", "Comprehensive analysis report")
        ]
        
        for i, (value, text, tooltip) in enumerate(analysis_options):
            radio = QRadioButton(text)
            radio.setToolTip(tooltip)
            if i == 0:  # Default to enhanced summary
                radio.setChecked(True)
            self.analysis_types.addButton(radio, i)
            type_layout.addWidget(radio)
        
        type_group.setLayout(type_layout)
        options_layout.addWidget(type_group)

        # Analysis Scope Group
        scope_group = QGroupBox("📏 Analysis Scope")
        scope_layout = QVBoxLayout()
        
        self.scope_all = QRadioButton("All Crawled Pages (Combined)")
        self.scope_all.setChecked(True)
        scope_layout.addWidget(self.scope_all)
        
        self.scope_selected = QRadioButton("Selected Pages Only")
        scope_layout.addWidget(self.scope_selected)
        
        # Page selection list
        self.page_list = QListWidget()
        self.page_list.setSelectionMode(QListWidget.ExtendedSelection)
        self.page_list.setEnabled(False)
        self.page_list.setMaximumHeight(150)
        scope_layout.addWidget(self.page_list)
        
        # Connect scope selection
        self.scope_selected.toggled.connect(self.page_list.setEnabled)
        
        scope_group.setLayout(scope_layout)
        options_layout.addWidget(scope_group)

        # Action buttons
        action_layout = QVBoxLayout()
        
        self.analyze_button = QPushButton("🔬 Analyze Content")
        self.analyze_button.setStyleSheet("""
            QPushButton {
                background-color: #2E86C1;
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #3498DB;
            }
            QPushButton:disabled {
                background-color: #BDC3C7;
            }
        """)
        self.analyze_button.clicked.connect(self.analyze_content)
        self.analyze_button.setEnabled(False)
        action_layout.addWidget(self.analyze_button)
        
        # Export options
        export_layout = QHBoxLayout()
        self.export_txt_button = QPushButton("💾 Export TXT")
        self.export_txt_button.clicked.connect(self.export_as_txt)
        self.export_txt_button.setEnabled(False)
        export_layout.addWidget(self.export_txt_button)
        
        self.export_json_button = QPushButton("💾 Export JSON")
        self.export_json_button.clicked.connect(self.export_as_json)
        self.export_json_button.setEnabled(False)
        export_layout.addWidget(self.export_json_button)
        
        action_layout.addLayout(export_layout)
        options_layout.addLayout(action_layout)
        
        # Add stretch to push everything to top
        options_layout.addStretch()
        
        splitter.addWidget(options_widget)
        
    def _setup_results_panel(self, splitter):
        """Setup right results panel"""
        results_widget = QWidget()
        results_layout = QVBoxLayout(results_widget)
        
        # Results header
        results_header = QHBoxLayout()
        results_label = QLabel("📈 Analysis Results")
        results_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #27AE60;")
        results_header.addWidget(results_label)
        
        # Clear results button
        self.clear_button = QPushButton("🗑️ Clear")
        self.clear_button.clicked.connect(self.clear_results)
        self.clear_button.setEnabled(False)
        results_header.addWidget(self.clear_button)
        
        results_layout.addLayout(results_header)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        results_layout.addWidget(self.progress_bar)
        
        # Results tabs for different output formats
        self.results_tabs = QTabWidget()
        
        # Human-readable tab
        self.human_tab = QTextEdit()
        self.human_tab.setReadOnly(True)
        self.human_tab.setPlaceholderText("Human-readable results will appear here...")
        self.human_tab.setFont(QFont("Consolas", 10))
        self.results_tabs.addTab(self.human_tab, "👤 Human Summary")
        
        # AI-optimized tab
        self.ai_tab = QTextEdit()
        self.ai_tab.setReadOnly(True)
        self.ai_tab.setPlaceholderText("AI-optimized JSON will appear here...")
        self.ai_tab.setFont(QFont("Consolas", 9))
        self.results_tabs.addTab(self.ai_tab, "🤖 AI JSON")
        
        # Raw analysis tab
        self.raw_tab = QTextEdit()
        self.raw_tab.setReadOnly(True)
        self.raw_tab.setPlaceholderText("Raw analysis results will appear here...")
        self.raw_tab.setFont(QFont("Consolas", 10))
        self.results_tabs.addTab(self.raw_tab, "🔍 Raw Analysis")
        
        results_layout.addWidget(self.results_tabs)
        
        # Status area
        status_layout = QHBoxLayout()
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #7F8C8D; font-style: italic;")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # Analysis info
        self.analysis_info = QLabel("")
        self.analysis_info.setStyleSheet("color: #7F8C8D; font-size: 10px;")
        status_layout.addWidget(self.analysis_info)
        
        results_layout.addLayout(status_layout)
        
        splitter.addWidget(results_widget)

    # NEW METHODS FOR FILE LOADING
    def get_project_directory(self):
        """Get the project root directory where IntelliCrawler is located"""
        import os
        # Get the directory where this file is located
        current_file = os.path.abspath(__file__)
        # Go up from supercrawler/ui/analysis_tab.py to the project root
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
        return project_root
    
    def get_exports_directories(self):
        """Get all export directories where saved crawls might be located"""
        import os
        project_root = self.get_project_directory()
        
        export_dirs = {
            "JSON Crawl Data": os.path.join(project_root, "exports", "json"),
            "Comprehensive Documentation": os.path.join(project_root, "exports", "documentation"),
            "Individual Markdown Pages": os.path.join(project_root, "exports", "markdown")
        }
        
        # Filter to only existing directories
        existing_dirs = {}
        for name, path in export_dirs.items():
            if os.path.exists(path):
                existing_dirs[name] = path
                
        return existing_dirs
        
    def refresh_saved_crawls(self, checked=False):
        """Refresh the list of saved crawl files from exports directories"""
        try:
            # Clear current items (except the first placeholder)
            self.saved_crawls_combo.clear()
            self.saved_crawls_combo.addItem("📂 Select a saved crawl or export...")
            
            export_dirs = self.get_exports_directories()
            total_files = 0
            
            if not export_dirs:
                self.saved_crawls_combo.addItem("❌ No exports directory found")
                self.update_status("No exports directories found in project")
                return
            
            # Process each export directory
            for dir_name, dir_path in export_dirs.items():
                try:
                    if not os.path.exists(dir_path):
                        continue
                        
                    # Add section header
                    self.saved_crawls_combo.addItem(f"📁 {dir_name}", None)
                    
                    # Get appropriate file types for each directory
                    if "JSON" in dir_name:
                        file_extensions = ['.json']
                        file_icon = "📊"
                    elif "Documentation" in dir_name:
                        file_extensions = ['.md', '.txt']
                        file_icon = "📚"
                    elif "Markdown" in dir_name:
                        file_extensions = ['.md']
                        file_icon = "📝"
                    else:
                        file_extensions = ['.json', '.md', '.txt']
                        file_icon = "📄"
                    
                    # Get files with the right extensions
                    all_files = os.listdir(dir_path)
                    matching_files = [f for f in all_files if any(f.lower().endswith(ext) for ext in file_extensions)]
                    matching_files.sort(reverse=True)  # Most recent first
                    
                    files_added = 0
                    for file in matching_files:
                        file_path = os.path.join(dir_path, file)
                        try:
                            # Get file info
                            mtime = os.path.getmtime(file_path)
                            date_str = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M')
                            
                            size = os.path.getsize(file_path)
                            if size < 1024:
                                size_str = f"{size}B"
                            elif size < 1024*1024:
                                size_str = f"{size/1024:.1f}KB"
                            else:
                                size_str = f"{size/(1024*1024):.1f}MB"
                            
                            # Create a cleaner filename for display
                            display_file = file
                            if len(display_file) > 50:
                                display_file = display_file[:47] + "..."
                                
                            display_name = f"   {file_icon} {display_file}"
                            tooltip = f"File: {file}\nPath: {file_path}\nSize: {size_str}\nModified: {date_str}"
                            
                            self.saved_crawls_combo.addItem(display_name, file_path)
                            
                            # Set tooltip (if supported)
                            item_index = self.saved_crawls_combo.count() - 1
                            self.saved_crawls_combo.setItemData(item_index, tooltip, Qt.ToolTipRole)
                            
                            files_added += 1
                            total_files += 1
                            
                        except Exception:
                            # Fallback to just filename
                            self.saved_crawls_combo.addItem(f"   ⚠️ {file}", file_path)
                            files_added += 1
                            total_files += 1
                    
                    # Add count info if no files found
                    if files_added == 0:
                        self.saved_crawls_combo.addItem("   📭 No files found", None)
                        
                except Exception as e:
                    self.logger.warning(f"Error processing directory {dir_name}: {str(e)}")
                    self.saved_crawls_combo.addItem(f"   ❌ Error reading {dir_name}", None)
            
            # Add separator and browse option
            self.saved_crawls_combo.addItem("─" * 50, None)
            self.saved_crawls_combo.addItem("📁 Browse for other files...", "BROWSE")
            
            # Update status
            if total_files > 0:
                self.update_status(f"✅ Found {total_files} export files in {len(export_dirs)} directories")
            else:
                self.update_status("⚠️ No export files found - try running the Web Crawler first")
                
        except Exception as e:
            self.logger.error(f"Error refreshing saved crawls: {str(e)}")
            self.update_status(f"❌ Error loading exports: {str(e)}")
            self.saved_crawls_combo.addItem("❌ Error loading files", None)
            
    def on_saved_crawl_selected(self, text):
        """Handle saved crawl selection with enhanced dropdown support"""
        try:
            current_index = self.saved_crawls_combo.currentIndex()
            
            if current_index <= 0:  # First item is placeholder
                self.load_saved_btn.setEnabled(False)
                return
            
            # Get the data associated with this item
            file_path = self.saved_crawls_combo.currentData()
            
            # Handle special cases
            if file_path == "BROWSE":
                # User selected "Browse for other files..."
                self.browse_for_file()
                return
            elif file_path is None:
                # This is a section header or separator, not a file
                self.load_saved_btn.setEnabled(False)
                return
            elif not file_path or not os.path.exists(file_path):
                # Invalid or missing file
                self.load_saved_btn.setEnabled(False)
                self.update_status("⚠️ Selected file does not exist")
                return
            
            # Valid file selected
            self.load_saved_btn.setEnabled(True)
            
            # Show file info in status
            try:
                size = os.path.getsize(file_path)
                size_str = f"{size/1024:.1f}KB" if size < 1024*1024 else f"{size/(1024*1024):.1f}MB"
                file_name = os.path.basename(file_path)
                self.update_status(f"📄 Selected: {file_name} ({size_str})")
            except:
                self.update_status(f"📄 Selected: {os.path.basename(file_path)}")
                
        except Exception as e:
            self.logger.error(f"Error in file selection: {str(e)}")
            self.load_saved_btn.setEnabled(False)
            
    @try_except_with_dialog
    def load_selected_crawl(self, checked=False):
        """Load the selected saved crawl file"""
        current_index = self.saved_crawls_combo.currentIndex()
        if current_index <= 0:  # 0 is the placeholder
            return
            
        file_path = self.saved_crawls_combo.currentData()
        if file_path:
            self.load_crawl_file(file_path)
            
    @try_except_with_dialog
    def browse_for_file(self, checked=False):
        """Browse for a crawl file to load from project exports or anywhere else"""
        try:
            # Start browsing in the project exports folder
            project_root = self.get_project_directory()
            exports_folder = os.path.join(project_root, "exports")
            
            # Use exports folder if it exists, otherwise project root
            start_dir = exports_folder if os.path.exists(exports_folder) else project_root
            
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "📂 Load Analysis Data - IntelliCrawler Exports",
                start_dir,
                "IntelliCrawler Files (*.json *.md *.txt);;JSON Data Files (*.json);;Markdown Files (*.md);;Text Files (*.txt);;All Files (*)"
            )
            
            if file_path:
                self.load_crawl_file(file_path)
                
        except Exception as e:
            self.logger.error(f"Error in browse dialog: {str(e)}")
            self.update_status(f"❌ Browse error: {str(e)}")
            
    def load_crawl_file(self, file_path):
        """Load data from a crawl file (JSON or Markdown)"""
        try:
            file_name = os.path.basename(file_path)
            file_ext = os.path.splitext(file_path)[1].lower()
            
            self.update_status(f"📂 Loading file: {file_name}")
            
            if file_ext == '.json':
                # Load JSON crawl data
                with open(file_path, 'r', encoding='utf-8') as f:
                    raw_data = json.load(f)
                
                # Handle different JSON structures
                crawl_data = None
                
                if isinstance(raw_data, list):
                    # Direct list of pages
                    crawl_data = raw_data
                elif isinstance(raw_data, dict):
                    # IntelliCrawler export format with metadata
                    if 'crawled_data' in raw_data and isinstance(raw_data['crawled_data'], list):
                        crawl_data = raw_data['crawled_data']

                        # Show export info if available
                        if 'export_info' in raw_data:
                            export_info = raw_data['export_info']
                            total_pages = export_info.get('total_pages', len(crawl_data))
                            export_date = export_info.get('export_date', 'unknown')
                            # crawler_version = export_info.get('crawler_version', 'unknown')  # Not used

                            self.update_status(f"📂 IntelliCrawler export from {export_date[:10]} ({total_pages} pages)")
                    # Check for other possible structures
                    elif 'pages' in raw_data:
                        crawl_data = raw_data['pages']
                    elif 'results' in raw_data:
                        crawl_data = raw_data['results']
                    elif 'data' in raw_data:
                        crawl_data = raw_data['data']
                
                if not crawl_data or not isinstance(crawl_data, list) or len(crawl_data) == 0:
                    QMessageBox.warning(self, "Invalid JSON File", 
                                      "The selected JSON file does not contain valid crawl data.\n\n"
                                      "Expected structure: {'crawled_data': [list of pages]} or [list of pages]")
                    return
                    
                # Set the loaded data
                self.set_crawled_data(crawl_data)
                
                # Update status
                self.update_status(f"✅ Loaded JSON data: {len(crawl_data)} pages from {file_name}")
                
                # Update data status with file info
                self.data_status.setText(f"✅ Loaded JSON: {file_name} ({len(crawl_data)} pages)")
                self.data_status.setStyleSheet("""
                    QLabel {
                        background-color: #E8F5E8;
                        color: #2E7D32;
                        padding: 8px;
                        border-radius: 4px;
                        border-left: 4px solid #4CAF50;
                        font-weight: bold;
                    }
                """)
                
            elif file_ext in ['.md', '.txt']:
                # Load Markdown/text content as a single document
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if not content.strip():
                    QMessageBox.warning(self, "Empty File", "The selected file appears to be empty.")
                    return
                
                # Create a single-item data structure
                mock_data = [{
                    "title": file_name,
                    "url": f"file://{file_path}",
                    "content": content,
                    "depth": 0,
                    "html": "",
                    "file_type": "markdown" if file_ext == '.md' else "text"
                }]
                
                # Set the loaded data
                self.set_crawled_data(mock_data)
                
                # Update status
                char_count = len(content)
                size_str = f"{char_count} characters"
                self.update_status(f"✅ Loaded {file_ext.upper()} file: {file_name} ({size_str})")
                
                # Update data status with file info
                self.data_status.setText(f"✅ Loaded {file_ext.upper()}: {file_name} ({size_str})")
                self.data_status.setStyleSheet("""
                    QLabel {
                        background-color: #E3F2FD;
                        color: #1976D2;
                        padding: 8px;
                        border-radius: 4px;
                        border-left: 4px solid #2196F3;
                        font-weight: bold;
                    }
                """)
                
            else:
                QMessageBox.warning(self, "Unsupported File Type", 
                                  f"Unsupported file type: {file_ext}\n\nSupported types:\n• JSON (.json) - Raw crawl data\n• Markdown (.md) - Documentation\n• Text (.txt) - Text content")
                return
                
        except json.JSONDecodeError:
            QMessageBox.critical(self, "JSON Parse Error", 
                               f"The selected file is not a valid JSON file.\n\nFile: {file_name}\n\nPlease check the file format and try again.")
        except UnicodeDecodeError:
            QMessageBox.critical(self, "Encoding Error", 
                               f"Unable to read the file due to encoding issues.\n\nFile: {file_name}\n\nPlease ensure the file is UTF-8 encoded.")
        except FileNotFoundError:
            QMessageBox.critical(self, "File Not Found", 
                               f"The selected file could not be found.\n\nFile: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Load Error", 
                               f"Failed to load file: {file_name}\n\nError: {str(e)}")
            self.logger.error(f"Error loading crawl file {file_path}: {str(e)}")
        
    def update_temp_label(self, value):
        """Update temperature label when slider changes"""
        temperature = value / 10.0
        self.temp_value_label.setText(f"{temperature:.1f}")
        
    def set_crawled_data(self, data):
        """Set the crawled data for analysis with smart recommendations"""
        self.crawled_data = data
        count = len(data) if data else 0
        
        if count > 0:
            self.data_status.setText(f"✅ Data available: {count} pages crawled.")
            self.data_status.setStyleSheet("""
                QLabel {
                    background-color: #E8F5E8;
                    color: #2E7D32;
                    padding: 8px;
                    border-radius: 4px;
                    border-left: 4px solid #4CAF50;
                    font-weight: bold;
                }
            """)
            self.analyze_button.setEnabled(True)
            self.quick_summary_btn.setEnabled(True)
            self.auto_analyze_btn.setEnabled(True)
            
            # Update integration status
            self.integration_status.setText(f"✅ {count} pages loaded")
            self.integration_status.setStyleSheet("""
                QLabel {
                    background-color: #E8F5E8;
                    color: #2E7D32;
                    padding: 6px 12px;
                    border-radius: 4px;
                    border: 1px solid #4CAF50;
                    font-weight: bold;
                    font-size: 11px;
                }
            """)
            
            # Update workflow guide
            self.workflow_guide.setText("📋 Step 2: ✅ Data loaded → Choose analysis type → Run analysis → Export results")
            
            # Generate and show smart recommendations
            self.update_smart_recommendations(data)
            
            # Populate page list
            self.page_list.clear()
            for i, item in enumerate(data):
                title = item.get('title', f'Page {i+1}')
                url = item.get('url', 'No URL')
                display_text = f"{title}"
                if len(display_text) > 50:
                    display_text = display_text[:47] + "..."
                    
                list_item = QListWidgetItem(display_text)
                list_item.setToolTip(f"URL: {url}\nTitle: {title}")
                list_item.setData(Qt.UserRole, item)
                self.page_list.addItem(list_item)
                
            # Update workflow step
            self.workflow_step = 2
            
        else:
            self.data_status.setText("📊 Ready for analysis - Load data using the options below")
            self.data_status.setStyleSheet("""
                QLabel {
                    background-color: #FFF3E0;
                    color: #F57C00;
                    padding: 8px;
                    border-radius: 4px;
                    border-left: 4px solid #FF9800;
                    font-weight: bold;
                }
            """)
            self.analyze_button.setEnabled(False)
            self.quick_summary_btn.setEnabled(False)
            self.auto_analyze_btn.setEnabled(False)
            self.page_list.clear()
            self.smart_recommendations.setVisible(False)
            self.workflow_step = 0
            
    @try_except_with_dialog
    def quick_summary(self, checked=False):
        """Generate quick summary using enhanced summary generation"""
        if not self.crawled_data:
            return
            
        # Set to enhanced summary mode
        for i, button in enumerate(self.analysis_types.buttons()):
            if i == 0:  # Enhanced summary is first option
                button.setChecked(True)
                break
                
        # Run analysis
        self.analyze_content()
        
    @try_except_with_dialog
    def analyze_content(self, checked=False):
        """Analyze the crawled content"""
        if not self.crawled_data:
            self.update_status("No data to analyze")
            return
            
        # Prepare content
        content = self._prepare_content_for_analysis()
        if not content:
            self.update_status("No content selected for analysis")
            return
            
        # Get analysis parameters
        analysis_type = self._get_selected_analysis_type()
        model = "deepseek-reasoner" if "reasoner" in self.model_selector.currentText() else "deepseek-chat"
        temperature = self.temperature_slider.value() / 10.0
        
        # Get AI instance with fallback options
        if hasattr(self.parent(), 'get_ai_instance'):
            self.ai = self.parent().get_ai_instance()

        # If no AI instance available, create one as fallback
        if not self.ai:
            from intellicrawler.ai_integration import DeepSeekAI
            self.ai = DeepSeekAI()
            self.update_status("Created new AI instance for analysis")

        # Validate AI instance and API key
        if not self.ai.is_api_key_set():
            self.update_status("❌ DeepSeek API key not configured")
            QMessageBox.warning(self, "API Key Required",
                              "DeepSeek API key not configured. Please set it in Settings.")
            return

        # Start analysis in background thread
        self.analysis_worker = AnalysisWorker(
            self.ai, content, analysis_type, model, temperature
        )
        self.analysis_worker.progress_updated.connect(self.on_progress_updated)
        self.analysis_worker.analysis_completed.connect(self.on_analysis_completed)
        self.analysis_worker.error_occurred.connect(self.on_analysis_error)
        
        # Update UI for analysis
        self.analyze_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # Start analysis
        self.analysis_worker.start()
        
    def _prepare_content_for_analysis(self):
        """Prepare content based on selected scope"""
        if self.scope_all.isChecked():
            # Use all crawled data
            return self.crawled_data
        else:
            # Use selected pages only
            selected_items = self.page_list.selectedItems()
            if not selected_items:
                return None
                
            return [item.data(Qt.UserRole) for item in selected_items]
            
    def _get_selected_analysis_type(self):
        """Get the selected analysis type"""
        analysis_types = [
            "document_enhancement", "summary_generation", "summary", "key_points", 
            "sentiment", "entities", "json", "detailed_report"
        ]
        
        checked_button = self.analysis_types.checkedButton()
        if checked_button:
            button_id = self.analysis_types.id(checked_button)
            return analysis_types[button_id]
        return "document_enhancement"  # Default to document enhancement
        
    def on_progress_updated(self, value, message):
        """Handle progress updates"""
        self.progress_bar.setValue(value)
        self.update_status(message)
        
    def on_analysis_completed(self, result):
        """Handle completed analysis with workflow updates"""
        self.last_analysis_result = result
        self._display_results(result)
        
        # Update UI
        self.progress_bar.setVisible(False)
        self.analyze_button.setEnabled(True)
        self.export_txt_button.setEnabled(True)
        self.export_json_button.setEnabled(True)
        self.clear_button.setEnabled(True)
        
        # Update analysis info
        timestamp = result.get('timestamp', 'Unknown')
        analysis_type = result.get('type', 'Unknown')
        
        # Enhanced analysis info with workflow guidance
        type_names = {
            "document_enhancement": "Document Enhancement",
            "summary_generation": "Enhanced Summary",
            "summary": "AI Summary", 
            "key_points": "Key Points",
            "sentiment": "Sentiment Analysis",
            "entities": "Entity Extraction",
            "json": "Structured Data",
            "detailed_report": "Detailed Report"
        }
        
        friendly_name = type_names.get(analysis_type, analysis_type)
        self.analysis_info.setText(f"✅ {friendly_name} completed | {timestamp[:19]} | Ready for export")
        
        # Update workflow guide
        self.workflow_guide.setText("📋 Step 4: ✅ Analysis complete → Export results (TXT/JSON) or run another analysis")
        self.workflow_guide.setStyleSheet("""
            QLabel {
                background-color: #E8F5E8;
                color: #2E7D32;
                padding: 8px;
                border-radius: 4px;
                border-left: 4px solid #4CAF50;
                font-weight: bold;
                margin: 4px 0px;
            }
        """)
        
        # Update workflow step
        self.workflow_step = 4
        
        self.update_status("✅ Analysis completed successfully - Ready for export!")
        
    def on_analysis_error(self, error_message):
        """Handle analysis errors"""
        self.progress_bar.setVisible(False)
        self.analyze_button.setEnabled(True)
        self.update_status(f"❌ Analysis failed: {error_message}")
        
        # Show error in results
        self.raw_tab.setText(f"Error occurred during analysis:\n\n{error_message}")
        
    def _display_results(self, result):
        """Display analysis results in appropriate tabs"""
        result_type = result.get('type', '')
        
        if result_type == "enhanced_summary":
            # Enhanced summary has both human and AI formats
            human_summary = result.get('human_summary', 'No human summary available')
            ai_json = result.get('ai_optimized', {})
            
            self.human_tab.setText(human_summary)
            self.ai_tab.setText(json.dumps(ai_json, indent=2, ensure_ascii=False))
            self.raw_tab.setText(f"Enhanced Summary Analysis\n{'='*50}\n\nType: {result_type}\nStatus: {result.get('status', 'Unknown')}\nTimestamp: {result.get('timestamp', 'Unknown')}\n\nHuman Summary Generated: {len(human_summary)} characters\nAI JSON Generated: {len(json.dumps(ai_json))} characters")
            
            # Set focus to human tab for enhanced summaries
            self.results_tabs.setCurrentIndex(0)
        elif result_type == "document_enhancement":
            # Document enhancement has detailed enhanced content
            enhanced_content = result.get('result', 'No enhanced content available')
            enhancement_details = result.get('enhancement_details', {})
            
            # Display enhanced content in human tab
            self.human_tab.setText(enhanced_content)
            
            # Display detailed info in AI tab
            details_text = f"""Document Enhancement Analysis
{'='*50}

Enhancement Status: {result.get('enhancement_status', 'Unknown')}
Timestamp: {result.get('timestamp', 'Unknown')}

Enhancement Details:
- URLs Processed: {enhancement_details.get('urls_processed', 0)}
- Pages Scraped: {enhancement_details.get('pages_scraped', 0)}
- Topics Researched: {enhancement_details.get('topics_researched', 0)}
- Search Queries: {enhancement_details.get('search_queries_made', 0)}

Source URLs:
{chr(10).join('- ' + url for url in result.get('source_urls', []))}

Topics Researched:
{chr(10).join('- ' + topic for topic in result.get('topics_researched', []))}

Enhanced Content Length: {len(enhanced_content)} characters
"""
            self.ai_tab.setText(details_text)
            
            # Display raw JSON in raw tab
            self.raw_tab.setText(json.dumps(result, indent=2, ensure_ascii=False))
            
            # Set focus to human tab for enhanced content
            self.results_tabs.setCurrentIndex(0)
        else:
            # Traditional AI analysis
            analysis_result = result.get('result', 'No result available')
            
            self.raw_tab.setText(analysis_result)
            self.human_tab.setText(f"Traditional AI Analysis Result:\n\n{analysis_result}")
            self.ai_tab.setText(json.dumps(result, indent=2, ensure_ascii=False))
            
            # Set focus to raw tab for traditional analysis
            self.results_tabs.setCurrentIndex(2)
            
    def update_status(self, message):
        """Update status label"""
        if hasattr(self, 'status_label') and self.status_label:
            self.status_label.setText(message)
        
    @try_except_with_dialog
    def export_as_txt(self, checked=False):
        """Export results as text file"""
        if not self.last_analysis_result:
            QMessageBox.warning(self, "Export Error", "No analysis results to export")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Analysis as TXT", 
            f"analysis_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt);;All Files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    if self.last_analysis_result.get('type') == 'enhanced_summary':
                        f.write(self.last_analysis_result.get('human_summary', ''))
                    else:
                        f.write(self.last_analysis_result.get('result', ''))
                        
                QMessageBox.information(self, "Export Successful", f"Results exported to:\n{file_path}")
                self.update_status("✅ Results exported as TXT")
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export file:\n{str(e)}")
                
    @try_except_with_dialog
    def export_as_json(self, checked=False):
        """Export results as JSON file"""
        if not self.last_analysis_result:
            QMessageBox.warning(self, "Export Error", "No analysis results to export")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Analysis as JSON", 
            f"analysis_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json);;All Files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    if self.last_analysis_result.get('type') == 'enhanced_summary':
                        # Export the AI-optimized JSON
                        json.dump(self.last_analysis_result.get('ai_optimized', {}), f, indent=2, ensure_ascii=False)
                    else:
                        # Export the full result
                        json.dump(self.last_analysis_result, f, indent=2, ensure_ascii=False)
                        
                QMessageBox.information(self, "Export Successful", f"Results exported to:\n{file_path}")
                self.update_status("✅ Results exported as JSON")
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export file:\n{str(e)}")
                
    def clear_results(self, checked=False):
        """Clear all results"""
        self.human_tab.clear()
        self.ai_tab.clear()
        self.raw_tab.clear()
        self.last_analysis_result = None
        self.export_txt_button.setEnabled(False)
        self.export_json_button.setEnabled(False)
        self.clear_button.setEnabled(False)
        self.analysis_info.setText("")
        self.update_status("Results cleared")
        
    def refresh_settings(self):
        """Refresh settings after configuration changes"""
        try:
            from intellicrawler.utils.config import load_config
            config = load_config()
            
            if hasattr(self, 'ai') and self.ai:
                api_key = config.get("deepseek_api_key", "")
                if api_key and hasattr(self.ai, 'set_api_key'):
                    self.ai.set_api_key(api_key)
                    
            self.logger.info("Analysis tab settings refreshed")
        except Exception as e:
            self.logger.error(f"Error refreshing analysis tab settings: {str(e)}")
    
    # NEW SMART INTEGRATION METHODS
    
    def check_for_new_crawl_data(self):
        """Check for new crawl data from the crawler tab"""
        try:
            if hasattr(self.parent, 'crawler_tab'):
                crawler_tab = self.parent.crawler_tab
                
                # Check if crawler has new data
                if hasattr(crawler_tab, 'scraped_data'):
                    current_count = len(crawler_tab.scraped_data) if crawler_tab.scraped_data else 0
                    
                    # Check if crawler is currently running
                    is_running = getattr(crawler_tab, 'is_running', False)
                    
                    if is_running and current_count > self.last_crawl_count:
                        # New data detected during crawling
                        self.integration_status.setText(f"🔄 Live: {current_count} pages crawled")
                        self.integration_status.setStyleSheet("""
                            QLabel {
                                background-color: #E1F5FE;
                                color: #0277BD;
                                padding: 6px 12px;
                                border-radius: 4px;
                                border: 1px solid #03A9F4;
                                font-weight: bold;
                                font-size: 11px;
                            }
                        """)
                        self.last_crawl_count = current_count
                        
                    elif not is_running and current_count > 0 and current_count != len(self.crawled_data):
                        # Crawler finished, new data available
                        self.integration_status.setText(f"🔔 New data available: {current_count} pages")
                        self.integration_status.setStyleSheet("""
                            QLabel {
                                background-color: #FFF3E0;
                                color: #F57C00;
                                padding: 6px 12px;
                                border-radius: 4px;
                                border: 1px solid #FF9800;
                                font-weight: bold;
                                font-size: 11px;
                                animation: blink 1s infinite;
                            }
                        """)
                        
                        # Auto-load the new data
                        if current_count > len(self.crawled_data):
                            self.set_crawled_data(crawler_tab.scraped_data)
                            self.update_status(f"✅ Auto-loaded {current_count} pages from crawler")
                    
        except Exception as e:
            self.logger.debug(f"Error checking for new crawl data: {str(e)}")
    
    def go_to_crawler_tab(self):
        """Navigate to the crawler tab"""
        if hasattr(self.parent, 'tabs'):
            self.parent.tabs.setCurrentIndex(0)  # Assuming crawler is first tab
    
    @try_except_with_dialog
    def auto_analyze(self):
        """Automatically choose and run the best analysis type based on content"""
        if not self.crawled_data:
            self.update_status("No data available for auto-analysis")
            return
        
        # Analyze content characteristics to choose best analysis type
        analysis_type = self.determine_best_analysis_type(self.crawled_data)
        
        # Set the recommended analysis type
        analysis_types = [
            "document_enhancement", "summary_generation", "summary", "key_points", 
            "sentiment", "entities", "json", "detailed_report"
        ]
        
        if analysis_type in analysis_types:
            type_index = analysis_types.index(analysis_type)
            
            # Set the radio button
            for i, button in enumerate(self.analysis_types.buttons()):
                if i == type_index:
                    button.setChecked(True)
                    break
        
        # Show what we're doing
        type_names = {
            "document_enhancement": "Document Enhancement",
            "summary_generation": "Enhanced Summary",
            "summary": "AI Summary", 
            "key_points": "Key Points",
            "sentiment": "Sentiment Analysis",
            "entities": "Entity Extraction",
            "json": "Structured Data",
            "detailed_report": "Detailed Report"
        }
        
        chosen_name = type_names.get(analysis_type, analysis_type)
        self.update_status(f"🤖 Auto-Analysis: Using {chosen_name} based on content analysis")
        
        # Run the analysis
        self.analyze_content()
    
    def determine_best_analysis_type(self, data):
        """Analyze content to determine the best analysis type"""
        if not data:
            return "summary_generation"
        
        # Analyze content characteristics
        total_pages = len(data)
        has_documentation = False
        has_tutorials = False
        has_code = False
        has_structured_data = False
        avg_content_length = 0
        
        # Sample first few pages for analysis
        sample_size = min(5, len(data))
        for item in data[:sample_size]:
            content = item.get('content', '').lower()
            title = item.get('title', '').lower()
            
            # Check for documentation patterns
            if any(word in content + title for word in ['api', 'reference', 'documentation', 'docs', 'manual']):
                has_documentation = True
            
            # Check for tutorial patterns
            if any(word in content + title for word in ['tutorial', 'how to', 'guide', 'step', 'example']):
                has_tutorials = True
            
            # Check for code patterns
            if any(word in content for word in ['function', 'class', 'import', 'def ', 'var ', '{', '}', 'public ']):
                has_code = True
            
            # Check for structured data patterns
            if any(word in content for word in ['json', 'xml', 'yaml', 'table', 'list:', 'properties']):
                has_structured_data = True
            
            avg_content_length += len(content)
        
        avg_content_length = avg_content_length / sample_size if sample_size > 0 else 0
        
        # Decision logic
        if has_documentation and total_pages > 5:
            return "summary_generation"  # Best for comprehensive documentation
        elif has_tutorials and has_code:
            return "key_points"  # Extract important steps and code snippets
        elif has_structured_data:
            return "json"  # Extract structured information
        elif total_pages > 10:
            return "summary_generation"  # Handle large datasets well
        elif avg_content_length > 3000:
            return "detailed_report"  # Rich content deserves detailed analysis
        else:
            return "summary_generation"  # Safe default choice
    
    def update_smart_recommendations(self, data):
        """Generate and display smart recommendations based on content"""
        if not data:
            self.smart_recommendations.setVisible(False)
            return
        
        recommendations = []
        total_pages = len(data)
        
        # Analyze content patterns
        has_documentation = False
        has_tutorials = False
        has_code = False
        avg_length = sum(len(item.get('content', '')) for item in data[:5]) / min(5, len(data))
        
        for item in data[:3]:  # Quick analysis of first 3 pages
            content = item.get('content', '').lower()
            title = item.get('title', '').lower()
            
            if any(word in content + title for word in ['api', 'documentation', 'reference']):
                has_documentation = True
            if any(word in content + title for word in ['tutorial', 'how to', 'guide']):
                has_tutorials = True
            if any(word in content for word in ['function', 'class', 'code', 'example']):
                has_code = True
        
        # Generate specific recommendations
        if has_documentation:
            recommendations.append("📚 Documentation detected - 'Enhanced Summary' recommended for comprehensive analysis")
        
        if has_tutorials:
            recommendations.append("🎯 Tutorial content found - 'Key Points' will extract important steps")
        
        if has_code:
            recommendations.append("💻 Code samples detected - 'Structured Data (JSON)' will organize technical content")
        
        if total_pages > 15:
            recommendations.append(f"📊 Large dataset ({total_pages} pages) - 'Enhanced Summary' handles big data well")
        elif total_pages < 3:
            recommendations.append("⚡ Small dataset - Perfect for 'Detailed Report' analysis")
        
        if avg_length > 4000:
            recommendations.append("📝 Rich content detected - All analysis types will work excellently")
        elif avg_length < 800:
            recommendations.append("⚠️ Brief content - Consider crawling more pages for better results")
        
        # Display recommendations
        if recommendations:
            rec_text = "🤖 Smart Recommendations:\n" + "\n".join([f"• {rec}" for rec in recommendations])
            self.smart_recommendations.setText(rec_text)
            self.smart_recommendations.setVisible(True)
        else:
            self.smart_recommendations.setText("🤖 Smart Recommendations: Your content is ready for any analysis type!")
            self.smart_recommendations.setVisible(True)