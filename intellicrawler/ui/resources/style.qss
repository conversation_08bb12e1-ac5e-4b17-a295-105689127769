/* Enhanced Style Sheet for IntelliCrawler - Standardized UI Controls */

/* === ROOT WIDGET STYLES === */
QWidget {
    background-color: #FFFFFF;
    color: #2C3E50;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 14px;
}

QMainWindow {
    background-color: #F8F9FA;
}

/* === STANDARDIZED BUTTON STYLES === */
QPushButton {
    background-color: #3498DB;
    color: #FFFFFF;
    border: 1px solid #2980B9;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 14px;
    min-height: 40px;
    max-height: 40px;
    min-width: 100px;
    text-align: center;
    icon-size: 16px 16px;
}

QPushButton:hover {
    background-color: #2980B9;
    border-color: #21618C;
    transform: translateY(-1px);
}

QPushButton:pressed {
    background-color: #21618C;
    border-color: #1B4F72;
    transform: translateY(0px);
}

QPushButton:disabled {
    background-color: #BDC3C7;
    color: #7F8C8D;
    border-color: #95A5A6;
}

/* Large buttons for primary actions */
QPushButton.primary {
    background-color: #27AE60;
    border-color: #229954;
    min-height: 48px;
    max-height: 48px;
    font-size: 16px;
    font-weight: bold;
    icon-size: 24px 24px;
}

QPushButton.primary:hover {
    background-color: #229954;
    border-color: #1E8449;
}

QPushButton.primary:pressed {
    background-color: #1E8449;
    border-color: #196F3D;
}

/* Secondary buttons */
QPushButton.secondary {
    background-color: #95A5A6;
    border-color: #7F8C8D;
}

QPushButton.secondary:hover {
    background-color: #7F8C8D;
    border-color: #6C7B7D;
}

/* Danger buttons */
QPushButton.danger {
    background-color: #E74C3C;
    border-color: #C0392B;
}

QPushButton.danger:hover {
    background-color: #C0392B;
    border-color: #A93226;
}

/* Small buttons for compact areas */
QPushButton.small {
    min-height: 32px;
    max-height: 32px;
    padding: 6px 12px;
    font-size: 12px;
    min-width: 80px;
    icon-size: 16px 16px;
}

/* === ENHANCED TAB WIDGET STYLES === */
QTabWidget::pane {
    border: 2px solid #BDC3C7;
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 4px;
    margin-top: 2px;
}

QTabWidget::tab-bar {
    left: 8px;
    alignment: left;
}

QTabBar {
    qproperty-drawBase: 0;
}

QTabBar::tab {
    background-color: #ECF0F1;
    color: #2C3E50;
    padding: 12px 20px;
    margin-right: 4px;
    margin-bottom: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    border: 2px solid #BDC3C7;
    border-bottom: none;
    font-weight: bold;
    font-size: 14px;
    min-width: 120px;
    min-height: 24px;
    text-align: center;
}

QTabBar::tab:selected {
    background-color: #3498DB;
    color: #FFFFFF;
    border-color: #2980B9;
    border-bottom: 2px solid #3498DB;
    margin-bottom: 0px;
}

QTabBar::tab:hover:!selected {
    background-color: #D5DBDB;
    color: #2C3E50;
    border-color: #A6ACAF;
}

QTabBar::tab:!selected {
    margin-top: 2px;
}

/* Dark theme compatibility for tabs */
[theme="dark"] QTabWidget::pane {
    border-color: #5D6D7E;
    background-color: #2C3E50;
}

[theme="dark"] QTabBar::tab {
    background-color: #34495E;
    color: #ECF0F1;
    border-color: #5D6D7E;
}

[theme="dark"] QTabBar::tab:selected {
    background-color: #3498DB;
    color: #FFFFFF;
    border-color: #2980B9;
}

[theme="dark"] QTabBar::tab:hover:!selected {
    background-color: #5D6D7E;
    color: #ECF0F1;
    border-color: #85929E;
}

QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #FFFFFF;
    color: #2C3E50;
    border: 1px solid #BDC3C7;
    border-radius: 4px;
    padding: 6px;
    selection-background-color: #3498DB;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border: 2px solid #3498DB;
}

QLabel {
    color: #2C3E50;
    font-weight: normal;
}

QProgressBar {
    border: 1px solid #BDC3C7;
    border-radius: 4px;
    background-color: #ECF0F1;
    color: #2C3E50;
    text-align: center;
    font-weight: bold;
}

QProgressBar::chunk {
    background-color: #27AE60;
    border-radius: 3px;
}

QStatusBar {
    background-color: #ECF0F1;
    color: #2C3E50;
    border-top: 1px solid #BDC3C7;
}

QMenuBar {
    background-color: #F8F9FA;
    color: #2C3E50;
    border-bottom: 1px solid #BDC3C7;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 12px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #3498DB;
    color: #FFFFFF;
}

QMenu {
    background-color: #FFFFFF;
    color: #2C3E50;
    border: 1px solid #BDC3C7;
    border-radius: 4px;
}

QMenu::item {
    padding: 8px 20px;
    border-radius: 4px;
    margin: 2px;
}

QMenu::item:selected {
    background-color: #3498DB;
    color: #FFFFFF;
}

QComboBox {
    background-color: #FFFFFF;
    color: #2C3E50;
    border: 1px solid #BDC3C7;
    border-radius: 4px;
    padding: 6px;
    min-width: 100px;
}

QComboBox:hover {
    border: 2px solid #3498DB;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #2C3E50;
    margin-right: 5px;
}

QCheckBox {
    color: #2C3E50;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #BDC3C7;
    border-radius: 3px;
    background-color: #FFFFFF;
}

QCheckBox::indicator:checked {
    background-color: #3498DB;
    border: 1px solid #2980B9;
}

QSpinBox {
    background-color: #FFFFFF;
    color: #2C3E50;
    border: 1px solid #BDC3C7;
    border-radius: 4px;
    padding: 4px;
}

QSpinBox:focus {
    border: 2px solid #3498DB;
}

/* === STANDARDIZED ICON SIZES === */
/* Small icons (16x16) for compact buttons, checkboxes, etc. */
.icon-small {
    qproperty-iconSize: 16px 16px;
}

/* Medium icons (24x24) for main buttons and actions */
.icon-medium {
    qproperty-iconSize: 24px 24px;
}

/* Large icons (32x32) for primary actions */
.icon-large {
    qproperty-iconSize: 32px 32px;
}

/* === TOOLTIP STYLES === */
QToolTip {
    background-color: #2C3E50;
    color: #FFFFFF;
    border: 1px solid #34495E;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: normal;
    max-width: 300px;
}

/* === ENHANCED FORM CONTROLS === */
QGroupBox {
    color: #2C3E50;
    border: 2px solid #BDC3C7;
    border-radius: 8px;
    margin-top: 12px;
    padding-top: 8px;
    font-weight: bold;
    font-size: 14px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 4px 8px;
    background-color: #FFFFFF;
    border-radius: 4px;
    color: #2C3E50;
}

QRadioButton {
    color: #2C3E50;
    spacing: 8px;
    font-size: 14px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #BDC3C7;
    border-radius: 8px;
    background-color: #FFFFFF;
}

QRadioButton::indicator:checked {
    background-color: #3498DB;
    border: 2px solid #2980B9;
}

QRadioButton::indicator:hover {
    border-color: #3498DB;
}

QSlider::groove:horizontal {
    border: 1px solid #BDC3C7;
    height: 8px;
    background-color: #ECF0F1;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background-color: #3498DB;
    border: 2px solid #2980B9;
    width: 20px;
    margin: -8px 0;
    border-radius: 10px;
}

QSlider::handle:horizontal:hover {
    background-color: #2980B9;
    border-color: #21618C;
}

/* === LIST WIDGETS === */
QListWidget {
    background-color: #FFFFFF;
    border: 1px solid #BDC3C7;
    border-radius: 4px;
    padding: 4px;
    selection-background-color: #3498DB;
    selection-color: #FFFFFF;
}

QListWidget::item {
    padding: 6px 8px;
    border-radius: 4px;
    margin: 1px;
}

QListWidget::item:selected {
    background-color: #3498DB;
    color: #FFFFFF;
}

QListWidget::item:hover:!selected {
    background-color: #ECF0F1;
}

/* === TREE WIDGETS === */
QTreeWidget {
    background-color: #FFFFFF;
    border: 1px solid #BDC3C7;
    border-radius: 4px;
    selection-background-color: #3498DB;
    selection-color: #FFFFFF;
}

QTreeWidget::item {
    padding: 4px;
    border-radius: 2px;
}

QTreeWidget::item:selected {
    background-color: #3498DB;
    color: #FFFFFF;
}

QTreeWidget::item:hover:!selected {
    background-color: #ECF0F1;
}

/* === SCROLL BARS === */
QScrollBar:vertical {
    background-color: #ECF0F1;
    width: 12px;
    border-radius: 6px;
    border: none;
}

QScrollBar::handle:vertical {
    background-color: #BDC3C7;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95A5A6;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #ECF0F1;
    height: 12px;
    border-radius: 6px;
    border: none;
}

QScrollBar::handle:horizontal {
    background-color: #BDC3C7;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #95A5A6;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* === DARK THEME COMPATIBILITY === */
[theme="dark"] QWidget {
    background-color: #2C3E50;
    color: #ECF0F1;
}

[theme="dark"] QMainWindow {
    background-color: #34495E;
}

[theme="dark"] QPushButton {
    background-color: #3498DB;
    color: #FFFFFF;
    border-color: #2980B9;
}

[theme="dark"] QPushButton:hover {
    background-color: #2980B9;
    border-color: #21618C;
}

[theme="dark"] QLineEdit,
[theme="dark"] QTextEdit,
[theme="dark"] QPlainTextEdit {
    background-color: #34495E;
    color: #ECF0F1;
    border-color: #5D6D7E;
}

[theme="dark"] QGroupBox {
    color: #ECF0F1;
    border-color: #5D6D7E;
}

[theme="dark"] QGroupBox::title {
    background-color: #34495E;
    color: #ECF0F1;
}

[theme="dark"] QListWidget,
[theme="dark"] QTreeWidget {
    background-color: #34495E;
    color: #ECF0F1;
    border-color: #5D6D7E;
}

[theme="dark"] QComboBox {
    background-color: #34495E;
    color: #ECF0F1;
    border-color: #5D6D7E;
}

[theme="dark"] QSpinBox {
    background-color: #34495E;
    color: #ECF0F1;
    border-color: #5D6D7E;
}

[theme="dark"] QCheckBox,
[theme="dark"] QRadioButton {
    color: #ECF0F1;
}

[theme="dark"] QCheckBox::indicator,
[theme="dark"] QRadioButton::indicator {
    background-color: #34495E;
    border-color: #5D6D7E;
}

[theme="dark"] QStatusBar {
    background-color: #34495E;
    color: #ECF0F1;
    border-color: #5D6D7E;
}

[theme="dark"] QMenuBar {
    background-color: #34495E;
    color: #ECF0F1;
    border-color: #5D6D7E;
}

[theme="dark"] QMenu {
    background-color: #2C3E50;
    color: #ECF0F1;
    border-color: #5D6D7E;
}

[theme="dark"] QToolTip {
    background-color: #34495E;
    color: #ECF0F1;
    border-color: #5D6D7E;
}

/* === ACCESSIBILITY ENHANCEMENTS === */
*:focus {
    outline: 2px solid #3498DB;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    QPushButton {
        border-width: 2px;
    }
    
    QTabBar::tab {
        border-width: 2px;
    }
    
    QLineEdit, QTextEdit, QPlainTextEdit {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    QPushButton:hover {
        transform: none;
    }
    
    QPushButton:pressed {
        transform: none;
    }
}
