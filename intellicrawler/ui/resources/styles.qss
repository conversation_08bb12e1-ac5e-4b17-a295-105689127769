
            /* Light theme stylesheet for IntelliCrawler */
            QWidget {
                background-color: #FFFFFF;
                color: #2C3E50;
                font-family: 'Segoe UI', Arial, sans-serif;
            }

            QMainWindow {
                background-color: #F8F9FA;
            }

            QTabWidget::pane {
                border: 1px solid #BDC3C7;
                background-color: #FFFFFF;
                border-radius: 4px;
            }

            QTabWidget::tab-bar {
                left: 5px;
            }

            QTabBar::tab {
                background-color: #ECF0F1;
                color: #2C3E50;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                border: 1px solid #BDC3C7;
                font-weight: bold;
            }

            QTabBar::tab:selected {
                background-color: #3498DB;
                color: #FFFFFF;
                border-bottom-color: #3498DB;
            }

            QTabBar::tab:hover:!selected {
                background-color: #D5DBDB;
            }

            QPushButton {
                background-color: #3498DB;
                color: #FFFFFF;
                border: 1px solid #2980B9;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }

            QPushButton:hover {
                background-color: #2980B9;
            }

            QPushButton:pressed {
                background-color: #21618C;
            }
            
            QLineEdit, QTextEdit, QPlainTextEdit {
                background-color: #FFFFFF;
                color: #2C3E50;
                border: 1px solid #BDC3C7;
                border-radius: 4px;
                padding: 6px;
                selection-background-color: #3498DB;
            }

            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                border: 2px solid #3498DB;
            }

            QLabel {
                color: #2C3E50;
                font-weight: normal;
            }

            QProgressBar {
                border: 1px solid #BDC3C7;
                border-radius: 4px;
                background-color: #ECF0F1;
                color: #2C3E50;
                text-align: center;
                font-weight: bold;
            }

            QProgressBar::chunk {
                background-color: #27AE60;
                border-radius: 3px;
            }

            QStatusBar {
                background-color: #ECF0F1;
                color: #2C3E50;
                border-top: 1px solid #BDC3C7;
            }

            QMenuBar {
                background-color: #F8F9FA;
                color: #2C3E50;
                border-bottom: 1px solid #BDC3C7;
            }

            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 4px;
            }

            QMenuBar::item:selected {
                background-color: #3498DB;
                color: #FFFFFF;
            }

            QMenu {
                background-color: #FFFFFF;
                color: #2C3E50;
                border: 1px solid #BDC3C7;
                border-radius: 4px;
            }
            
            QMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
                margin: 2px;
            }

            QMenu::item:selected {
                background-color: #3498DB;
                color: #FFFFFF;
            }

            /* Additional styling for better appearance */
            QComboBox {
                background-color: #FFFFFF;
                color: #2C3E50;
                border: 1px solid #BDC3C7;
                border-radius: 4px;
                padding: 6px;
                min-width: 100px;
            }

            QComboBox:hover {
                border: 2px solid #3498DB;
            }

            QComboBox::drop-down {
                border: none;
                width: 20px;
            }

            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #2C3E50;
                margin-right: 5px;
            }

            QCheckBox {
                color: #2C3E50;
                spacing: 8px;
            }

            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid #BDC3C7;
                border-radius: 3px;
                background-color: #FFFFFF;
            }

            QCheckBox::indicator:checked {
                background-color: #3498DB;
                border: 1px solid #2980B9;
            }

            QSpinBox {
                background-color: #FFFFFF;
                color: #2C3E50;
                border: 1px solid #BDC3C7;
                border-radius: 4px;
                padding: 4px;
            }

            QSpinBox:focus {
                border: 2px solid #3498DB;
            }
            