from PyQt5.QtWidgets import (Q<PERSON><PERSON>t, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QTextEdit,
                            QLineEdit, QCheckBox, QSpinBox, QComboBox, QProgressBar,
                            QGroupBox, QFormLayout, QRadioButton, QButtonGroup,
                            QListWidget, QSplitter, QTabWidget, QFrame, QScrollArea,
                            QTextBrowser, QTreeWidget, QTreeWidgetItem, QSlider)
from PyQt5.QtCore import Qt, pyqtSlot, QTimer, pyqtSignal
from PyQt5.QtGui import QIcon, QFont, QPixmap, QPainter, QColor

from intellicrawler.utils.logger import get_logger
from intellicrawler.utils.error_handler import try_except_with_dialog
from intellicrawler.utils.data_persistence import DataPersistenceManager

import os
import sys
from datetime import datetime
import json

class IntelligentCrawlerTab(QWidget):
    """Enhanced AI-powered crawler tab for intelligent web scraping"""
    
    # Signals for real-time updates
    browser_activity = pyqtSignal(str, str)  # action, url
    intelligent_decision = pyqtSignal(str)  # AI decision message
    crawl_completed = pyqtSignal(list)  # crawled data
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self.scraped_data = []
        self.is_running = False
        self.using_ai_mode = False
        self.current_crawling_strategy = None
        self.browser_activity_log = []

        # Initialize data persistence manager
        self.persistence_manager = DataPersistenceManager()

        self.setup_ui()

        # Timer for real-time updates
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_real_time_display)
        
    def setup_ui(self):
        # Main splitter for layout
        main_splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Configuration and Control
        left_panel = self.create_control_panel()
        main_splitter.addWidget(left_panel)
        
        # Right panel - Real-time Activity and Results
        right_panel = self.create_activity_panel()
        main_splitter.addWidget(right_panel)
        
        # Set splitter proportions
        main_splitter.setStretchFactor(0, 1)  # Control panel
        main_splitter.setStretchFactor(1, 2)  # Activity panel (bigger)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.addWidget(main_splitter)
        self.setLayout(main_layout)
        
    def create_control_panel(self):
        """Create the left control panel"""
        control_widget = QWidget()
        layout = QVBoxLayout(control_widget)
        
        # URL and Target Configuration
        url_group = QGroupBox("🎯 Target Configuration")
        url_layout = QVBoxLayout()
        
        # URL input with validation
        url_input_layout = QHBoxLayout()
        url_label = QLabel("Website URL:")
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("https://learn.microsoft.com/en-us/windows/win32/termserv/...")
        self.url_input.textChanged.connect(self.analyze_url_context)
        self.url_input.textChanged.connect(self.detect_documentation_site)
        url_input_layout.addWidget(url_label)
        url_input_layout.addWidget(self.url_input)
        url_layout.addLayout(url_input_layout)
        
        # AI Context Detection (auto-populated)
        self.context_label = QLabel("🤖 AI Detected Context: Not analyzed yet")
        self.context_label.setWordWrap(True)
        self.context_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 8px; border-radius: 4px; }")
        url_layout.addWidget(self.context_label)
        
        url_group.setLayout(url_layout)
        layout.addWidget(url_group)
        
        # Intelligent Crawling Mode Selection
        mode_group = QGroupBox("🧠 Intelligent Crawling Mode")
        mode_layout = QVBoxLayout()
        
        self.mode_group = QButtonGroup()
        
        # Smart Documentation Mode
        self.doc_mode = QRadioButton("📚 Smart Documentation Crawler")
        self.doc_mode.setToolTip("Perfect for technical documentation like Microsoft Learn, developer guides, API docs")
        self.doc_mode.setChecked(True)
        self.mode_group.addButton(self.doc_mode, 0)
        mode_layout.addWidget(self.doc_mode)
        
        # Research Mode
        self.research_mode = QRadioButton("🔬 Research & Analysis Mode")
        self.research_mode.setToolTip("For comprehensive research on specific topics, collects related content")
        self.mode_group.addButton(self.research_mode, 1)
        mode_layout.addWidget(self.research_mode)
        
        # Targeted Content Mode
        self.content_mode = QRadioButton("🎯 Targeted Content Extraction")
        self.content_mode.setToolTip("Focus on specific content types (tutorials, guides, examples)")
        self.mode_group.addButton(self.content_mode, 2)
        mode_layout.addWidget(self.content_mode)
        
        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)
        
        # AI Agent Configuration
        ai_group = QGroupBox("⚙️ AI Agent Settings")
        ai_layout = QFormLayout()
        
        # Intelligence Level
        self.intelligence_slider = QSlider(Qt.Horizontal)
        self.intelligence_slider.setRange(1, 5)
        self.intelligence_slider.setValue(3)
        self.intelligence_slider.setTickPosition(QSlider.TicksBelow)
        self.intelligence_slider.setTickInterval(1)
        self.intelligence_slider.valueChanged.connect(self.update_intelligence_description)
        ai_layout.addRow("Intelligence Level:", self.intelligence_slider)
        
        self.intelligence_desc = QLabel("Balanced: Smart decisions with good speed")
        self.intelligence_desc.setWordWrap(True)
        ai_layout.addRow("", self.intelligence_desc)
        
        # Focus Topic (AI will auto-suggest)
        self.focus_input = QLineEdit()
        self.focus_input.setPlaceholderText("AI will auto-detect, or specify your focus...")
        ai_layout.addRow("Focus Topic:", self.focus_input)
        
        # AI Model Selection
        self.model_combo = QComboBox()
        self.model_combo.addItems(["deepseek-reasoner (R1) - Best for Analysis", "deepseek-chat (V3) - Faster"])
        ai_layout.addRow("AI Model:", self.model_combo)
        
        ai_group.setLayout(ai_layout)
        layout.addWidget(ai_group)
        
        # Crawling Constraints
        constraints_group = QGroupBox("🚧 Smart Constraints")
        constraints_layout = QFormLayout()
        
        # Max Pages with smart suggestion for documentation sites
        self.max_pages_spin = QSpinBox()
        self.max_pages_spin.setRange(5, 500)
        self.max_pages_spin.setValue(50)  # Increased from 25 to 50 for better documentation coverage
        self.max_pages_spin.setToolTip("Recommended: 50+ for documentation sites, 25 for general crawling")
        constraints_layout.addRow("Max Pages:", self.max_pages_spin)
        
        # Depth with AI recommendation
        self.depth_spin = QSpinBox()
        self.depth_spin.setRange(1, 8)
        self.depth_spin.setValue(4)  # Increased from 3 to 4 for better documentation coverage
        self.depth_spin.setToolTip("Recommended: 4+ for documentation sites, 3 for general crawling")
        constraints_layout.addRow("Crawl Depth:", self.depth_spin)
        
        # Stay in Domain (important for focused crawling)
        self.stay_domain_check = QCheckBox("Stay within same domain")
        self.stay_domain_check.setChecked(True)
        self.stay_domain_check.setToolTip("Prevents crawling to external sites")
        constraints_layout.addRow("", self.stay_domain_check)
        
        # Follow Navigation Patterns
        self.smart_nav_check = QCheckBox("Follow navigation patterns")
        self.smart_nav_check.setChecked(True)
        self.smart_nav_check.setToolTip("AI will identify and follow site navigation structure")
        constraints_layout.addRow("", self.smart_nav_check)
        
        constraints_group.setLayout(constraints_layout)
        layout.addWidget(constraints_group)
        
        # Control Buttons
        button_layout = QVBoxLayout()
        
        self.start_button = QPushButton("🚀 Start Intelligent Crawl")
        self.start_button.clicked.connect(self.start_intelligent_crawl)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        button_layout.addWidget(self.start_button)
        
        button_controls = QHBoxLayout()
        
        self.stop_button = QPushButton("⏹️ Stop")
        self.stop_button.clicked.connect(self.stop_crawling)
        self.stop_button.setEnabled(False)
        button_controls.addWidget(self.stop_button)
        
        self.reset_button = QPushButton("🔄 Reset")
        self.reset_button.clicked.connect(self.reset)
        button_controls.addWidget(self.reset_button)
        
        button_layout.addLayout(button_controls)
        layout.addLayout(button_layout)
        
        # Progress and Status
        progress_group = QGroupBox("📊 Progress")
        progress_layout = QVBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        progress_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("Ready for intelligent crawling")
        self.status_label.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(self.status_label)
        
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)
        
        # Add stretch to push everything to top
        layout.addStretch()
        
        return control_widget
    
    def create_activity_panel(self):
        """Create the right activity panel with real-time updates"""
        activity_widget = QWidget()
        layout = QVBoxLayout(activity_widget)
        
        # Title
        title = QLabel("🤖 AI Agent Browser - Real-time Activity")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Tabbed interface for different views
        self.activity_tabs = QTabWidget()
        
        # Tab 1: Live Browser Activity
        self.browser_tab = self.create_browser_activity_tab()
        self.activity_tabs.addTab(self.browser_tab, "🌐 Live Browser")
        
        # Tab 2: AI Decision Log
        self.decision_tab = self.create_decision_log_tab()
        self.activity_tabs.addTab(self.decision_tab, "🧠 AI Decisions")
        
        # Tab 3: Content Preview
        self.content_tab = self.create_content_preview_tab()
        self.activity_tabs.addTab(self.content_tab, "📄 Content Preview")
        
        # Tab 4: Crawl Results
        self.results_tab = self.create_results_tab()
        self.activity_tabs.addTab(self.results_tab, "📁 Results")
        
        layout.addWidget(self.activity_tabs)
        
        return activity_widget
    
    def create_browser_activity_tab(self):
        """Create the live browser activity tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Current action display
        self.current_action_label = QLabel("🔍 Waiting to start...")
        self.current_action_label.setStyleSheet("""
            QLabel {
                background-color: #e3f2fd;
                padding: 10px;
                border-radius: 6px;
                font-size: 13px;
                border-left: 4px solid #2196f3;
            }
        """)
        layout.addWidget(self.current_action_label)
        
        # Browser activity log
        self.browser_log = QTextBrowser()
        self.browser_log.setPlaceholderText("AI browser activity will appear here in real-time...")
        layout.addWidget(self.browser_log)
        
        # Current page info
        page_info_group = QGroupBox("📄 Current Page Analysis")
        page_info_layout = QVBoxLayout()
        
        self.page_title_label = QLabel("Title: Not started")
        self.page_url_label = QLabel("URL: Not started")
        self.page_relevance_label = QLabel("Relevance Score: Not analyzed")
        
        page_info_layout.addWidget(self.page_title_label)
        page_info_layout.addWidget(self.page_url_label)
        page_info_layout.addWidget(self.page_relevance_label)
        
        page_info_group.setLayout(page_info_layout)
        layout.addWidget(page_info_group)
        
        return tab
    
    def create_decision_log_tab(self):
        """Create the AI decision log tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        self.decision_log = QTextBrowser()
        self.decision_log.setPlaceholderText("AI decision-making process will be logged here...")
        layout.addWidget(self.decision_log)
        
        return tab
    
    def create_content_preview_tab(self):
        """Create the content preview tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Content stats
        stats_layout = QHBoxLayout()
        self.pages_found_label = QLabel("Pages Found: 0")
        self.content_quality_label = QLabel("Avg Quality: N/A")
        self.duplicate_filter_label = QLabel("Duplicates Filtered: 0")
        
        stats_layout.addWidget(self.pages_found_label)
        stats_layout.addWidget(self.content_quality_label)
        stats_layout.addWidget(self.duplicate_filter_label)
        layout.addLayout(stats_layout)
        
        # Content tree view
        self.content_tree = QTreeWidget()
        self.content_tree.setHeaderLabels(["Title", "URL", "Quality Score", "Content Type"])
        layout.addWidget(self.content_tree)
        
        return tab
    
    def create_results_tab(self):
        """Create the results tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Results summary
        summary_group = QGroupBox("📊 Crawl Summary")
        summary_layout = QFormLayout()
        
        self.total_pages_label = QLabel("0")
        self.avg_quality_label = QLabel("N/A")
        self.total_content_label = QLabel("0 KB")
        self.crawl_time_label = QLabel("Not started")
        
        summary_layout.addRow("Total Pages:", self.total_pages_label)
        summary_layout.addRow("Average Quality:", self.avg_quality_label)
        summary_layout.addRow("Total Content:", self.total_content_label)
        summary_layout.addRow("Crawl Time:", self.crawl_time_label)
        
        summary_group.setLayout(summary_layout)
        layout.addWidget(summary_group)
        
        # Export options
        export_group = QGroupBox("💾 Export Options")
        export_layout = QHBoxLayout()
        
        self.export_markdown_btn = QPushButton("📝 Export as Markdown")
        self.export_markdown_btn.clicked.connect(self.export_as_markdown)
        self.export_markdown_btn.setEnabled(False)
        
        self.export_json_btn = QPushButton("📋 Export as JSON")
        self.export_json_btn.clicked.connect(self.export_as_json)
        self.export_json_btn.setEnabled(False)
        
        self.export_docs_btn = QPushButton("📚 Create Documentation")
        self.export_docs_btn.clicked.connect(self.create_documentation)
        self.export_docs_btn.setEnabled(False)
        
        export_layout.addWidget(self.export_markdown_btn)
        export_layout.addWidget(self.export_json_btn)
        export_layout.addWidget(self.export_docs_btn)
        
        export_group.setLayout(export_layout)
        layout.addWidget(export_group)
        
        # Detailed results list
        self.results_list = QListWidget()
        layout.addWidget(self.results_list)
        
        return tab
    
    def analyze_url_context(self):
        """Analyze the URL to detect context and suggest optimal settings"""
        url = self.url_input.text().strip()
        if not url:
            self.context_label.setText("🤖 AI Detected Context: Enter a URL to analyze")
            return
            
        # Quick context analysis based on URL patterns
        context_hints = []
        suggested_mode = None
        
        # Documentation patterns
        if any(pattern in url.lower() for pattern in ['docs', 'documentation', 'learn', 'guide', 'api', 'reference']):
            context_hints.append("Documentation Site")
            suggested_mode = 0  # Doc mode
            
        # Microsoft Learn specific
        if 'learn.microsoft.com' in url.lower():
            context_hints.append("Microsoft Learn")
            self.focus_input.setPlaceholderText("e.g., 'Remote Desktop ActiveX Control documentation'")
            
        # Developer resources
        if any(pattern in url.lower() for pattern in ['developer', 'dev', 'sdk', 'api']):
            context_hints.append("Developer Resources")
            
        # Technical content
        if any(pattern in url.lower() for pattern in ['tutorial', 'how-to', 'example', 'sample']):
            context_hints.append("Technical Content")
            suggested_mode = 2  # Content mode
            
        if context_hints:
            context_text = f"🤖 AI Detected Context: {', '.join(context_hints)}"
            self.context_label.setText(context_text)
            
            # Auto-suggest optimal mode
            if suggested_mode is not None:
                self.mode_group.button(suggested_mode).setChecked(True)
        else:
            self.context_label.setText("🤖 AI Detected Context: General website - will analyze on start")

    def detect_documentation_site(self):
        """Detect documentation sites and auto-adjust crawl parameters"""
        url = self.url_input.text().strip().lower()
        if not url:
            return

        # Documentation site patterns
        doc_patterns = [
            'docs.', 'documentation', 'api-docs', 'learn.', 'developer.',
            'guide', 'manual', 'reference', 'tutorial', 'help.'
        ]

        # Check if this looks like a documentation site
        is_doc_site = any(pattern in url for pattern in doc_patterns)

        if is_doc_site:
            # Auto-adjust parameters for better documentation coverage
            current_max_pages = self.max_pages_spin.value()
            current_depth = self.depth_spin.value()

            # Only increase if current values are low
            if current_max_pages < 50:
                self.max_pages_spin.setValue(50)
                self.add_browser_log("📚 Documentation site detected - Max pages increased to 50")

            if current_depth < 4:
                self.depth_spin.setValue(4)
                self.add_browser_log("📚 Documentation site detected - Crawl depth increased to 4")

    def update_intelligence_description(self, value):
        """Update the intelligence level description"""
        descriptions = {
            1: "Basic: Fast crawling with simple decisions",
            2: "Smart: Good balance of speed and intelligence", 
            3: "Balanced: Smart decisions with good speed",
            4: "Advanced: Deep analysis and strategic crawling",
            5: "Maximum: Comprehensive analysis (slower but thorough)"
        }
        self.intelligence_desc.setText(descriptions.get(value, "Unknown level"))
    
    @try_except_with_dialog
    def start_intelligent_crawl(self, checked=False):
        """Start the intelligent AI-powered crawl"""
        if self.is_running:
            self.add_browser_log("⚠️ Crawl already in progress")
            return
            
        # Validate inputs
        url = self.url_input.text().strip()
        if not url:
            self.add_browser_log("❌ ERROR: URL is required")
            return
            
        # Normalize URL
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            self.url_input.setText(url)
            
        # Get configuration
        crawl_mode = self.mode_group.checkedId()
        intelligence_level = self.intelligence_slider.value()
        focus_topic = self.focus_input.text().strip()
        max_pages = self.max_pages_spin.value()
        depth = self.depth_spin.value()
        stay_domain = self.stay_domain_check.isChecked()
        smart_nav = self.smart_nav_check.isChecked()
        model = "deepseek-reasoner" if "reasoner" in self.model_combo.currentText() else "deepseek-chat"
        
        # Update UI
        self.is_running = True
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("🚀 Initializing intelligent crawler...")
        
        # Clear previous results
        self.browser_log.clear()
        self.decision_log.clear()
        self.content_tree.clear()
        self.results_list.clear()
        
        # Start the AI agent with enhanced parameters
        self.add_browser_log(f"🎯 Starting intelligent crawl of: {url}")
        self.add_browser_log(f"🧠 Mode: {['Documentation', 'Research', 'Targeted Content'][crawl_mode]}")
        self.add_browser_log(f"⚡ Intelligence Level: {intelligence_level}/5")
        
        try:
            self._start_enhanced_ai_agent(
                url=url,
                crawl_mode=crawl_mode,
                intelligence_level=intelligence_level,
                focus_topic=focus_topic,
                max_pages=max_pages,
                depth=depth,
                stay_domain=stay_domain,
                smart_nav=smart_nav,
                model=model
            )
            
            # Start real-time updates
            self.update_timer.start(1000)  # Update every second
            
        except Exception as e:
            self.logger.error(f"Error starting intelligent crawl: {str(e)}")
            self.add_browser_log(f"❌ ERROR: {str(e)}")
            self.on_crawl_error(str(e))
    
    def _start_enhanced_ai_agent(self, **kwargs):
        """Start the enhanced AI agent with all parameters"""
        try:
            # Import AI integration
            from intellicrawler.ai_integration import DeepSeekAI
            
            # Get or create AI instance
            ai = None
            try:
                if hasattr(self.parent(), 'ai_integration'):
                    ai = self.parent().ai_integration
                    self.add_decision_log("✅ Using application's AI integration")
                else:
                    ai = DeepSeekAI()
                    self.add_decision_log("✅ Created new AI integration instance")
            except Exception as e:
                error_msg = f"Failed to initialize AI integration: {str(e)}"
                self.logger.error(error_msg, exc_info=True)
                raise RuntimeError(error_msg)
            
            # Check API key
            if not ai.is_api_key_set():
                error_msg = "DeepSeek API key not configured. Please set it in Settings."
                self.add_decision_log(f"❌ {error_msg}")
                raise ValueError(error_msg)
            
            # Connect signals for real-time updates
            ai.agent_progress.connect(self.on_agent_progress)
            ai.agent_completed.connect(self.on_agent_completed)
            ai.error_occurred.connect(self.on_crawl_error)
            
            # Store AI instance
            self.ai_agent = ai
            
            # Start enhanced crawling with parameters
            self.add_decision_log("🚀 Starting enhanced AI agent...")
            ai.start_enhanced_ai_agent(**kwargs)
            
        except Exception as e:
            self.logger.error(f"Error in _start_enhanced_ai_agent: {str(e)}")
            raise
    
    def stop_crawling(self):
        """Stop the intelligent crawling"""
        self.add_browser_log("⏹️ Stopping intelligent crawl...")
        
        try:
            if hasattr(self, 'ai_agent') and self.ai_agent:
                if hasattr(self.ai_agent, 'stop_ai_agent'):
                    self.ai_agent.stop_ai_agent()
                    
            self.update_timer.stop()
            self.is_running = False
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText("⏹️ Crawl stopped")
            
            self.add_browser_log("✅ Crawl stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping crawl: {str(e)}")
            self.add_browser_log(f"⚠️ Error stopping crawl: {str(e)}")
    
    def reset(self):
        """Reset the crawler tab"""
        # Stop any running crawl
        if self.is_running:
            self.stop_crawling()
            
        # Clear inputs
        self.url_input.clear()
        self.focus_input.clear()
        
        # Reset controls
        self.max_pages_spin.setValue(25)
        self.depth_spin.setValue(3)
        self.intelligence_slider.setValue(3)
        self.doc_mode.setChecked(True)
        self.stay_domain_check.setChecked(True)
        self.smart_nav_check.setChecked(True)
        
        # Clear displays
        self.browser_log.clear()
        self.decision_log.clear()
        self.content_tree.clear()
        self.results_list.clear()
        
        # Reset labels
        self.context_label.setText("🤖 AI Detected Context: Enter a URL to analyze")
        self.current_action_label.setText("🔍 Waiting to start...")
        self.status_label.setText("Ready for intelligent crawling")
        self.progress_bar.setValue(0)
        
        # Reset export buttons
        self.export_markdown_btn.setEnabled(False)
        self.export_json_btn.setEnabled(False)
        self.export_docs_btn.setEnabled(False)
        
        self.add_browser_log("🔄 Crawler reset and ready")
    
    def add_browser_log(self, message):
        """Add message to browser activity log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        self.browser_log.append(formatted_message)
        self.browser_activity_log.append(formatted_message)
        
        # Auto-scroll to bottom
        scrollbar = self.browser_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def add_decision_log(self, message):
        """Add message to AI decision log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        self.decision_log.append(formatted_message)
        
        # Auto-scroll to bottom
        scrollbar = self.decision_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def update_real_time_display(self):
        """Update real-time display elements"""
        if not self.is_running:
            return
            
        # This will be called every second during crawling
        # Update current action, progress, etc.
        pass
    
    @pyqtSlot(str, int)
    def on_agent_progress(self, message, progress):
        """Handle AI agent progress updates"""
        self.add_browser_log(f"🤖 {message}")
        self.progress_bar.setValue(progress)
        self.status_label.setText(f"Progress: {progress}% - {message}")
    
    @pyqtSlot(list)
    def on_agent_completed(self, data):
        """Handle AI agent completion"""
        self.update_timer.stop()
        self.is_running = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        if data:
            self.scraped_data = data
            self.add_browser_log(f"✅ Crawl completed! Found {len(data)} relevant pages")
            self.status_label.setText(f"✅ Completed: {len(data)} pages found")
            self.progress_bar.setValue(100)

            # Update results
            self.update_results_display(data)

            # Enable export buttons
            self.export_markdown_btn.setEnabled(True)
            self.export_json_btn.setEnabled(True)
            self.export_docs_btn.setEnabled(True)

            # Automatically save session data
            try:
                session_id = self.persistence_manager.save_session_data(data)
                self.add_browser_log(f"💾 Data automatically saved (Session: {session_id})")
            except Exception as e:
                self.add_browser_log(f"⚠️ Auto-save failed: {str(e)}")
                self.logger.error(f"Auto-save failed: {str(e)}")

            # Emit signal to notify analysis tab
            self.crawl_completed.emit(data)

            # Switch to results tab
            self.activity_tabs.setCurrentIndex(3)
        else:
            self.add_browser_log("⚠️ Crawl completed but no data was found")
            self.status_label.setText("⚠️ Completed: No relevant content found")

            # Still emit signal with empty data
            self.crawl_completed.emit([])
    
    @pyqtSlot(str)
    def on_crawl_error(self, error_message):
        """Handle crawl errors"""
        self.update_timer.stop()
        self.is_running = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        self.add_browser_log(f"❌ ERROR: {error_message}")
        self.status_label.setText(f"❌ Error: {error_message}")
    
    def update_results_display(self, data):
        """Update the results display with crawled data"""
        # Update summary
        self.total_pages_label.setText(str(len(data)))
        
        # Calculate average quality if available
        quality_scores = [item.get('quality_score', 0) for item in data if 'quality_score' in item]
        if quality_scores:
            avg_quality = sum(quality_scores) / len(quality_scores)
            self.avg_quality_label.setText(f"{avg_quality:.1f}/10")
        
        # Calculate total content size
        total_size = sum(len(item.get('content', '')) for item in data)
        self.total_content_label.setText(f"{total_size / 1024:.1f} KB")
        
        # Update content tree
        self.content_tree.clear()
        for item in data:
            tree_item = QTreeWidgetItem([
                item.get('title', 'Untitled')[:50],
                item.get('url', '')[:60],
                str(item.get('quality_score', 'N/A')),
                item.get('content_type', 'Unknown')
            ])
            self.content_tree.addTopLevelItem(tree_item)
        
        # Update results list
        self.results_list.clear()
        for i, item in enumerate(data):
            list_item = f"{i+1}. {item.get('title', 'Untitled')} ({item.get('url', 'No URL')})"
            self.results_list.addItem(list_item)
    
    def export_as_markdown(self):
        """Export crawled data as markdown files"""
        if not self.scraped_data:
            return
            
        try:
            # Create export directory
            export_dir = os.path.join(os.path.expanduser("~"), "IntelliCrawler_Exports", "markdown")
            os.makedirs(export_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            for i, item in enumerate(self.scraped_data):
                filename = f"page_{i+1}_{timestamp}.md"
                filepath = os.path.join(export_dir, filename)
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(f"# {item.get('title', 'Untitled')}\n\n")
                    f.write(f"**URL:** {item.get('url', 'Unknown')}\n\n")
                    f.write(f"**Scraped:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write("---\n\n")
                    f.write(item.get('content', 'No content available'))
            
            self.add_browser_log(f"✅ Exported {len(self.scraped_data)} pages to: {export_dir}")
            
        except Exception as e:
            self.add_browser_log(f"❌ Export error: {str(e)}")
    
    def export_as_json(self):
        """Export crawled data as JSON"""
        if not self.scraped_data:
            return
            
        try:
            export_dir = os.path.join(os.path.expanduser("~"), "IntelliCrawler_Exports", "json")
            os.makedirs(export_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"crawl_data_{timestamp}.json"
            filepath = os.path.join(export_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.scraped_data, f, indent=2, ensure_ascii=False)
            
            self.add_browser_log(f"✅ Exported data to: {filepath}")
            
        except Exception as e:
            self.add_browser_log(f"❌ Export error: {str(e)}")
    
    def create_documentation(self):
        """Create structured documentation from crawled data"""
        if not self.scraped_data:
            return
            
        try:
            export_dir = os.path.join(os.path.expanduser("~"), "IntelliCrawler_Exports", "documentation")
            os.makedirs(export_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"documentation_{timestamp}.md"
            filepath = os.path.join(export_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("# Crawled Documentation\n\n")
                f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write("## Table of Contents\n\n")
                
                # Table of contents
                for i, item in enumerate(self.scraped_data):
                    title = item.get('title', f'Page {i+1}')
                    f.write(f"{i+1}. [{title}](#{title.lower().replace(' ', '-')})\n")
                
                f.write("\n---\n\n")
                
                # Content sections
                for i, item in enumerate(self.scraped_data):
                    title = item.get('title', f'Page {i+1}')
                    f.write(f"## {title}\n\n")
                    f.write(f"**Source:** [{item.get('url', 'Unknown')}]({item.get('url', '#')})\n\n")
                    f.write(item.get('content', 'No content available'))
                    f.write("\n\n---\n\n")
            
            self.add_browser_log(f"✅ Created documentation: {filepath}")
            
        except Exception as e:
            self.add_browser_log(f"❌ Documentation creation error: {str(e)}")

# Maintain backward compatibility
CrawlerTab = IntelligentCrawlerTab 