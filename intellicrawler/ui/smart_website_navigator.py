#!/usr/bin/env python3
"""
IntelliCrawler Smart Website Navigator
Intelligent routing system that prioritizes Universal Website Handler
"""

import os
import json
import time
import random
import requests
from datetime import datetime
from intellicrawler.ui.universal_website_handler import UniversalWebsiteHandler

class SmartWebsiteNavigator:
    """
    Smart Website Navigator - Intelligent routing system
    Routes scraping requests to the most appropriate handler
    Prioritizes Universal Website Handler for maximum compatibility
    """
    
    def __init__(self, logger=None):
        self.logger = logger
        
        # Initialize Universal Website Handler (primary handler)
        self.universal_handler = UniversalWebsiteHandler(logger=logger)
        
        # Performance tracking
        self.success_statistics = {
            'universal_handler': {'successes': 0, 'attempts': 0},
            'fallback_methods': {'successes': 0, 'attempts': 0}
        }
        
        # Learned patterns for intelligent routing
        self.routing_intelligence = {
            'domain_preferences': {},
            'success_patterns': {},
            'failure_patterns': {}
        }
        
    def _log(self, message):
        """Safe logging with fallback"""
        if self.logger:
            self.logger.info(message)
        else:
            # Fallback to logger import if no logger provided
            try:
                from intellicrawler.utils.logger import get_logger
                fallback_logger = get_logger(__name__)
                fallback_logger.info(f"[SmartNavigator] {message}")
            except:
                print(f"[SmartNavigator] {message}")
    
    def discover_all_pages_intelligent(self, url, source_name, max_pages=25):
        """
        Intelligent page discovery using the best available method
        Always tries Universal Handler first as it's the most advanced
        """
        self._log(f"🧠 Smart Navigator: Starting intelligent page discovery for {source_name}")
        
        # Phase 0: Try Universal AI-Powered Handler First (PRIORITY)
        try:
            self._log(f"🚀 Trying Universal Website Handler for {source_name}")
            self.success_statistics['universal_handler']['attempts'] += 1
            
            universal_pages = self.universal_handler.discover_all_pages_universal(url, source_name, max_pages)
            
            if universal_pages and len(universal_pages) > 1:
                self.success_statistics['universal_handler']['successes'] += 1
                self._log(f"✅ Universal Handler SUCCESS: Discovered {len(universal_pages)} pages for {source_name}")
                
                # Learn from success
                self._learn_success_pattern(source_name, 'universal_handler', len(universal_pages))
                
                return universal_pages
            else:
                self._log(f"⚠️ Universal Handler returned limited results for {source_name}")
                
        except Exception as e:
            self._log(f"❌ Universal Handler failed for {source_name}: {str(e)}")
        
        # Phase 1: Fallback to basic pattern discovery
        try:
            self._log(f"🔄 Trying fallback pattern discovery for {source_name}")
            self.success_statistics['fallback_methods']['attempts'] += 1
            
            fallback_pages = self._fallback_page_discovery(url, source_name, max_pages)
            
            if fallback_pages:
                self.success_statistics['fallback_methods']['successes'] += 1
                self._log(f"✅ Fallback SUCCESS: Discovered {len(fallback_pages)} pages for {source_name}")
                
                # Learn from fallback success
                self._learn_success_pattern(source_name, 'fallback_methods', len(fallback_pages))
                
                return fallback_pages
                
        except Exception as e:
            self._log(f"❌ Fallback discovery failed for {source_name}: {str(e)}")
        
        # Phase 2: Return single page as absolute fallback
        self._log(f"⚠️ All discovery methods failed for {source_name}, returning single page")
        return [url]
    
    def extract_content_intelligent(self, content, source_name, page_url):
        """
        Intelligent content extraction using the best available method
        Always tries Universal Handler first as it's the most sophisticated
        """
        self._log(f"🎯 Smart Navigator: Starting intelligent content extraction for {source_name}")
        
        # Phase 0: Try Universal AI-Powered Handler First (PRIORITY)
        try:
            self._log(f"🚀 Trying Universal Website Handler extraction for {source_name}")
            
            universal_proxies = self.universal_handler.extract_content_universal(content, source_name, page_url)
            
            if universal_proxies:
                self._log(f"✅ Universal Handler SUCCESS: Extracted {len(universal_proxies)} proxies from {source_name}")
                
                # Learn from successful extraction
                self._learn_extraction_success(source_name, 'universal_handler', len(universal_proxies))
                
                return universal_proxies[:600]  # Limit to reasonable number
            else:
                self._log(f"⚠️ Universal Handler returned no proxies for {source_name}")
                
        except Exception as e:
            self._log(f"❌ Universal Handler extraction failed for {source_name}: {str(e)}")
        
        # Phase 1: Fallback to basic extraction methods
        try:
            self._log(f"🔄 Trying fallback extraction methods for {source_name}")
            
            fallback_proxies = self._fallback_content_extraction(content, source_name, page_url)
            
            if fallback_proxies:
                self._log(f"✅ Fallback SUCCESS: Extracted {len(fallback_proxies)} proxies from {source_name}")
                
                # Learn from fallback success
                self._learn_extraction_success(source_name, 'fallback_methods', len(fallback_proxies))
                
                return fallback_proxies[:600]
                
        except Exception as e:
            self._log(f"❌ Fallback extraction failed for {source_name}: {str(e)}")
        
        # Phase 2: Return empty list if all methods fail
        self._log(f"❌ All extraction methods failed for {source_name}")
        return []
    
    def _fallback_page_discovery(self, url, source_name, max_pages):
        """Basic fallback page discovery using common patterns"""
        pages = [url]  # Always include base URL
        
        # Common pagination patterns to try
        patterns = [
            lambda base_url, page: f"{base_url.rstrip('/')}/page/{page}/",
            lambda base_url, page: f"{base_url}?page={page}",
            lambda base_url, page: f"{base_url}{'&' if '?' in base_url else '?'}page={page}",
            lambda base_url, page: f"{base_url.rstrip('/')}/{page}/",
            lambda base_url, page: f"{base_url}{'&' if '?' in base_url else '?'}p={page}",
        ]
        
        # Generate pages using patterns
        max_attempts = min(max_pages, 10)  # Limit fallback attempts
        
        for page_num in range(2, max_attempts + 1):
            for pattern in patterns:
                try:
                    page_url = pattern(url, page_num)
                    pages.append(page_url)
                except:
                    continue
        
        return list(set(pages))  # Remove duplicates
    
    def _fallback_content_extraction(self, content, source_name, page_url):
        """Basic fallback content extraction using regex patterns"""
        import re
        
        proxies = []
        
        # Basic proxy patterns
        proxy_patterns = [
            r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):(?:6553[0-5]|655[0-2][0-9]|65[0-4][0-9]{2}|6[0-4][0-9]{3}|[1-5][0-9]{4}|[1-9][0-9]{1,3}|[0-9])\b',
            r'(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)[:\s,;|]+(?:6553[0-5]|655[0-2][0-9]|65[0-4][0-9]{2}|6[0-4][0-9]{3}|[1-5][0-9]{4}|[1-9][0-9]{1,3}|[0-9])'
        ]
        
        # Extract proxies using regex
        for pattern in proxy_patterns:
            matches = re.findall(pattern, content)
            
            for match in matches:
                # Clean and validate
                if ':' in match:
                    parts = match.split(':')
                    if len(parts) == 2:
                        ip, port = parts
                        if self._is_valid_ip(ip) and self._is_valid_port(port):
                            proxy = {
                                'ip': ip,
                                'port': int(port),
                                'protocol': 'http',
                                'source': source_name
                            }
                            proxies.append(proxy)
        
        # Remove duplicates
        seen = set()
        unique_proxies = []
        for proxy in proxies:
            key = f"{proxy['ip']}:{proxy['port']}"
            if key not in seen:
                seen.add(key)
                unique_proxies.append(proxy)
        
        return unique_proxies
    
    def _is_valid_ip(self, ip):
        """Validate IP address"""
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not 0 <= int(part) <= 255:
                    return False
            return True
        except:
            return False
    
    def _is_valid_port(self, port):
        """Validate port number"""
        try:
            port_num = int(port)
            return 1 <= port_num <= 65535
        except:
            return False
    
    def _learn_success_pattern(self, source_name, method, pages_found):
        """Learn from successful page discovery"""
        if source_name not in self.routing_intelligence['success_patterns']:
            self.routing_intelligence['success_patterns'][source_name] = {}
        
        self.routing_intelligence['success_patterns'][source_name][method] = {
            'pages_found': pages_found,
            'timestamp': datetime.now().isoformat(),
            'success_count': self.routing_intelligence['success_patterns'][source_name].get(method, {}).get('success_count', 0) + 1
        }
    
    def _learn_extraction_success(self, source_name, method, proxies_found):
        """Learn from successful content extraction"""
        if source_name not in self.routing_intelligence['success_patterns']:
            self.routing_intelligence['success_patterns'][source_name] = {}
        
        if 'extraction' not in self.routing_intelligence['success_patterns'][source_name]:
            self.routing_intelligence['success_patterns'][source_name]['extraction'] = {}
        
        self.routing_intelligence['success_patterns'][source_name]['extraction'][method] = {
            'proxies_found': proxies_found,
            'timestamp': datetime.now().isoformat(),
            'success_count': self.routing_intelligence['success_patterns'][source_name]['extraction'].get(method, {}).get('success_count', 0) + 1
        }
    
    def get_performance_statistics(self):
        """Get performance statistics for all handlers"""
        stats = {}
        
        for handler, data in self.success_statistics.items():
            if data['attempts'] > 0:
                success_rate = (data['successes'] / data['attempts']) * 100
                stats[handler] = {
                    'attempts': data['attempts'],
                    'successes': data['successes'],
                    'success_rate': f"{success_rate:.1f}%"
                }
            else:
                stats[handler] = {
                    'attempts': 0,
                    'successes': 0,
                    'success_rate': "N/A"
                }
        
        return stats
    
    def get_intelligence_summary(self):
        """Get summary of learned intelligence"""
        summary = {
            'total_sources_learned': len(self.routing_intelligence['success_patterns']),
            'universal_handler_success_rate': 0,
            'best_performing_sources': [],
            'learning_patterns': len(self.routing_intelligence['success_patterns'])
        }
        
        # Calculate Universal Handler success rate
        if self.success_statistics['universal_handler']['attempts'] > 0:
            summary['universal_handler_success_rate'] = (
                self.success_statistics['universal_handler']['successes'] / 
                self.success_statistics['universal_handler']['attempts']
            ) * 100
        
        # Find best performing sources
        for source, patterns in self.routing_intelligence['success_patterns'].items():
            if 'universal_handler' in patterns:
                summary['best_performing_sources'].append({
                    'source': source,
                    'pages_found': patterns['universal_handler'].get('pages_found', 0),
                    'success_count': patterns['universal_handler'].get('success_count', 0)
                })
        
        # Sort by success count
        summary['best_performing_sources'] = sorted(
            summary['best_performing_sources'], 
            key=lambda x: x['success_count'], 
            reverse=True
        )[:5]  # Top 5
        
        return summary
    
    def save_intelligence(self, filepath):
        """Save learned intelligence to file"""
        try:
            intelligence_data = {
                'routing_intelligence': self.routing_intelligence,
                'success_statistics': self.success_statistics,
                'timestamp': datetime.now().isoformat()
            }
            
            with open(filepath, 'w') as f:
                json.dump(intelligence_data, f, indent=2)
            
            self._log(f"✅ Intelligence saved to {filepath}")
            
        except Exception as e:
            self._log(f"❌ Failed to save intelligence: {str(e)}")
    
    def load_intelligence(self, filepath):
        """Load learned intelligence from file"""
        try:
            if os.path.exists(filepath):
                with open(filepath, 'r') as f:
                    intelligence_data = json.load(f)
                
                self.routing_intelligence = intelligence_data.get('routing_intelligence', {})
                self.success_statistics = intelligence_data.get('success_statistics', self.success_statistics)
                
                self._log(f"✅ Intelligence loaded from {filepath}")
            else:
                self._log(f"⚠️ Intelligence file not found: {filepath}")
                
        except Exception as e:
            self._log(f"❌ Failed to load intelligence: {str(e)}")
    
    def analyze_domain_compatibility(self, url):
        """Analyze domain compatibility with different handlers"""
        from urllib.parse import urlparse
        
        domain = urlparse(url).netloc.lower()
        
        # Check if we have learned patterns for this domain
        if domain in self.routing_intelligence['success_patterns']:
            patterns = self.routing_intelligence['success_patterns'][domain]
            
            compatibility = {
                'domain': domain,
                'universal_handler_success': 'universal_handler' in patterns,
                'recommended_handler': 'universal_handler',  # Always recommend universal first
                'confidence': 0.8 if 'universal_handler' in patterns else 0.5,
                'previous_successes': patterns.get('universal_handler', {}).get('success_count', 0)
            }
        else:
            # No previous data, default recommendation
            compatibility = {
                'domain': domain,
                'universal_handler_success': None,
                'recommended_handler': 'universal_handler',  # Always try universal first
                'confidence': 0.7,  # Default confidence
                'previous_successes': 0
            }
        
        return compatibility
    
    def get_recommended_strategy(self, url, source_name):
        """Get recommended scraping strategy based on learned intelligence"""
        compatibility = self.analyze_domain_compatibility(url)
        
        strategy = {
            'primary_handler': 'universal_handler',
            'fallback_handlers': ['fallback_methods'],
            'confidence': compatibility['confidence'],
            'reasoning': f"Universal Handler recommended for {source_name}",
            'estimated_success_rate': compatibility['confidence'] * 100
        }
        
        # Adjust based on previous success
        if compatibility['previous_successes'] > 0:
            strategy['confidence'] = min(0.95, strategy['confidence'] + 0.1)
            strategy['reasoning'] += f" (Previous successes: {compatibility['previous_successes']})"
        
        return strategy