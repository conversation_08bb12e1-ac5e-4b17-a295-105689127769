"""
This module provides a simple text-based logo for SuperCrawler
as a placeholder until a proper graphical icon is created.
"""

def print_logo():
    """Print the ASCII art logo to the console"""
    logo = """
  ____ _   _ ____  _____ ____   ____ ____      _    __        ___     _____ ____  
 / ___| | | |  _ \| ____|  _ \ / ___|  _ \    / \   \ \      / / |   | ____|  _ \ 
 \___ \ | | | |_) |  _| | |_) | |   | |_) |  / _ \   \ \ /\ / /| |   |  _| | |_) |
  ___) | |_| |  __/| |___|  _ <| |___|  _ <  / ___ \   \ V  V / | |___| |___|  _ < 
 |____/ \___/|_|   |_____|_| \_\\\\____|_| \_\/_/   \_\   \_/\_/  |_____|_____|_| \_\\

 A web scraper with AI power - DeepSeek R1 & V2.5 integrated
    """
    print(logo)

if __name__ == "__main__":
    print_logo() 