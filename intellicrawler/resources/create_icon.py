#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Create SuperCrawler icon from embedded base64 data.
This script generates the application icon file from embedded data.
"""

import os
import base64
import io
from PIL import Image

# Base64 encoded PNG icon data (32x32 pixel spider icon with AI elements)
ICON_DATA = """
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
"""

def main():
    """Create icon files from base64 data"""
    # Decode base64 string
    icon_data = base64.b64decode(ICON_DATA)
    
    # Open as PIL Image
    img = Image.open(io.BytesIO(icon_data))
    
    # Create resources directory if it doesn't exist
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Save as ICO for Windows
    ico_path = os.path.join(script_dir, 'icon.ico')
    img.save(ico_path)
    print(f"Created icon at: {ico_path}")
    
    # Save as PNG as well
    png_path = os.path.join(script_dir, 'icon.png')
    img.save(png_path)
    print(f"Created PNG at: {png_path}")

if __name__ == "__main__":
    main() 