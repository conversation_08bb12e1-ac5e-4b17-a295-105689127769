#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication, QAction
from PyQt5.QtCore import QCoreApplication, Qt
from intellicrawler.ui.main_window import MainWindow
from intellicrawler.utils.logger import setup_logger, get_logger, log_error
from intellicrawler.utils.config import load_config, create_default_config
from intellicrawler.utils.error_handler import Error<PERSON><PERSON><PERSON>

def create_shortcut():
    """Create desktop shortcut for Windows 10"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "IntelliCrawler.lnk")
        target = sys.executable
        wDir = os.path.dirname(sys.executable)

        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = wDir
        shortcut.Arguments = "-m intellicrawler.main"
        shortcut.IconLocation = os.path.join(os.path.dirname(__file__), "resources", "icon.ico")
        shortcut.save()
        return True
    except Exception as e:
        log_error(e, "Failed to create desktop shortcut")
        return False

def ensure_resource_dirs():
    """Ensure resource directories exist"""
    # Create UI resources directory
    ui_resources_dir = os.path.join(os.path.dirname(__file__), "ui", "resources")
    os.makedirs(ui_resources_dir, exist_ok=True)
    
    # Create main resources directory
    resources_dir = os.path.join(os.path.dirname(__file__), "resources")
    os.makedirs(resources_dir, exist_ok=True)
    
    # Create default QSS file if it doesn't exist
    qss_file = os.path.join(ui_resources_dir, "styles.qss")
    if not os.path.exists(qss_file):
        try:
            with open(qss_file, 'w', encoding='utf-8') as f:
                f.write("""
            /* Dark theme stylesheet for SuperCrawler */
            QWidget {
                background-color: #2D2D2D;
                color: #EEEEEE;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QMainWindow {
                background-color: #2D2D2D;
            }
            
            QTabWidget::pane {
                border: 1px solid #444444;
                background-color: #2D2D2D;
            }
            
            QTabWidget::tab-bar {
                left: 5px;
            }
            
            QTabBar::tab {
                background-color: #3D3D3D;
                color: #CCCCCC;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            
            QTabBar::tab:selected {
                background-color: #4D4D4D;
                color: #FFFFFF;
            }
            
            QPushButton {
                background-color: #444444;
                color: #EEEEEE;
                border: 1px solid #555555;
                padding: 6px 12px;
                border-radius: 4px;
            }
            
            QPushButton:hover {
                background-color: #555555;
            }
            
            QPushButton:pressed {
                background-color: #505050;
            }
            
            QLineEdit, QTextEdit, QPlainTextEdit {
                background-color: #3A3A3A;
                color: #EEEEEE;
                border: 1px solid #555555;
                border-radius: 3px;
                padding: 3px;
            }
            
            QLabel {
                color: #EEEEEE;
            }
            
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 3px;
                background-color: #3A3A3A;
                color: #EEEEEE;
                text-align: center;
            }
            
            QProgressBar::chunk {
                background-color: #3A7734;
            }
            
            QStatusBar {
                background-color: #363636;
                color: #DDDDDD;
            }
            
            QMenuBar {
                background-color: #363636;
                color: #EEEEEE;
            }
            
            QMenuBar::item {
                background-color: transparent;
                padding: 5px 10px;
            }
            
            QMenuBar::item:selected {
                background-color: #444444;
            }
            
            QMenu {
                background-color: #363636;
                color: #EEEEEE;
                border: 1px solid #555555;
            }
            
            QMenu::item {
                padding: 5px 20px;
            }
            
            QMenu::item:selected {
                background-color: #444444;
            }
            """)
        except Exception as e:
            logger = get_logger()
            logger.error(f"Failed to create styles.qss file: {str(e)}")

def main():
    """Main application entry point"""
    # Set Application Name and Version
    app_name = "IntelliCrawler"
    app_version = "2.0.0"
    
    # Create application
    QCoreApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
    app = QApplication(sys.argv)
    app.setApplicationName(app_name)
    app.setApplicationVersion(app_version)
    
    # Set up error handler
    error_handler = ErrorHandler()
    error_handler.setup_exception_handling()
    
    # Set up logging
    setup_logger()
    logger = get_logger()
    logger.info(f"Starting {app_name} v{app_version}")
    
    # Create config if it doesn't exist
    config = load_config()
    if not config:
        logger.info("No config file found. Creating default config.")
        create_default_config()
    
    # Ensure resource directories exist
    ensure_resource_dirs()

    # Load stylesheet if available
    try:
        ui_resources_dir = os.path.join(os.path.dirname(__file__), "ui", "resources")
        qss_file = os.path.join(ui_resources_dir, "styles.qss")
        if os.path.exists(qss_file):
            with open(qss_file, 'r', encoding='utf-8') as f:
                app.setStyleSheet(f.read())
            logger.info("Loaded application stylesheet")
    except Exception as e:
        logger.warning(f"Could not load stylesheet: {str(e)}")

    # Create main window
    window = MainWindow(config)
    
    # Add diagnostic tool integration
    add_diagnostic_tool(window)
    
    # Show the window
    window.show()
    
    # Run the application loop
    return app.exec_()

def add_diagnostic_tool(window):
    """Add diagnostic tool integration to the main window"""
    logger = get_logger()
    
    # Check if diagnostic module is available
    try:
        from intellicrawler.diagnostic import run_diagnostic_gui
        
        # Create diagnostic action
        diagnostic_action = QAction("Run Diagnostic Tests", window)
        diagnostic_action.setStatusTip("Run diagnostic tests to check system configuration and application components")
        diagnostic_action.triggered.connect(run_diagnostic_gui)
        
        # Add to tools menu if it exists
        if hasattr(window, 'tools_menu'):
            window.tools_menu.addAction(diagnostic_action)
        # Otherwise, try to find or create a tools menu
        else:
            # Find the menubar
            menubar = window.menuBar()
            
            # Check if a tools menu already exists
            tools_menu = None
            for action in menubar.actions():
                if action.text() == "&Tools":
                    tools_menu = action.menu()
                    break
            
            # Create the tools menu if it doesn't exist
            if tools_menu is None:
                tools_menu = menubar.addMenu("&Tools")
            
            # Add the diagnostic action to the tools menu
            tools_menu.addAction(diagnostic_action)
            
        logger.info("Diagnostic tool integration added to main window")
    except ImportError:
        logger.warning("Could not import diagnostic module. Diagnostic tool integration not available.")
    except Exception as e:
        logger.error(f"Error adding diagnostic tool integration: {str(e)}")

if __name__ == "__main__":
    sys.exit(main()) 