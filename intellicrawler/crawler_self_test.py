#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IntelliCrawler Crawler Self-Test

This module provides specific tests for the crawler component,
helping to diagnose issues with the web crawling functionality.

Usage:
    python -m intellicrawler.crawler_self_test
"""

import os
import sys
import json
import time
import logging
import traceback
import platform
from typing import Dict, List, Any, Tuple, Optional

from intellicrawler.utils.logger import setup_logger, get_logger
from intellicrawler.utils.config import load_config
from intellicrawler.crawler import Crawler

# Set up logger
logger = get_logger()

# Constants
TEST_URL = "https://docs.firecrawl.dev/introduction"
TEST_OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "test_output")

# Define platform-compatible symbols
if platform.system() == "Windows":
    PASS_SYMBOL = "[PASS]"
    FAIL_SYMBOL = "[FAIL]"
else:
    PASS_SYMBOL = "✓"
    FAIL_SYMBOL = "✗"

class CrawlerSelfTest:
    """Self-testing for crawler component"""
    
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.logger = logger
        self.crawler = None
        self.results = {}
        self.test_count = 0
        self.passed_count = 0
        
        # Ensure test output directory exists
        os.makedirs(TEST_OUTPUT_DIR, exist_ok=True)
    
    def log(self, message: str) -> None:
        """Log a message"""
        if self.verbose:
            self.logger.info(message)
            print(message)
    
    def run_all_tests(self) -> Dict[str, Dict[str, Any]]:
        """Run all crawler tests"""
        self.log("Starting crawler self-test...")
        self.results = {}
        self.test_count = 0
        self.passed_count = 0
        
        try:
            # Initialize the crawler
            self.crawler = Crawler()
            
            # Run tests
            self.run_test("Crawler Initialization", self._test_crawler_init)
            self.run_test("User Agent Configuration", self._test_user_agent)
            self.run_test("URL Validation", self._test_url_validation)
            self.run_test("Single Page Crawl (Static)", self._test_static_crawl)
            self.run_test("WebDriver Availability", self._test_webdriver_availability)
            
            # Only run dynamic test if WebDriver is available
            if self.results.get("WebDriver Availability", {}).get("success", False):
                self.run_test("Single Page Crawl (Dynamic)", self._test_dynamic_crawl)
                self.run_test("WebDriver Cleanup", self._test_webdriver_cleanup)
            
            self.log(f"\nTest Summary: {self.passed_count}/{self.test_count} tests passed")
            return self.results
            
        except Exception as e:
            error_msg = f"Error during crawler self-test: {str(e)}"
            self.logger.error(error_msg)
            traceback.print_exc()
            return {"error": {"success": False, "message": error_msg}}
    
    def run_test(self, test_name: str, test_func, *args, **kwargs) -> Tuple[bool, str]:
        """Run a single test and record results"""
        self.test_count += 1
        
        self.log(f"\nRunning test: {test_name}")
        start_time = time.time()
        
        try:
            result, message = test_func(*args, **kwargs)
            elapsed = time.time() - start_time
            
            if result:
                self.passed_count += 1
                self.log(f"{PASS_SYMBOL} PASS: {test_name} ({elapsed:.2f}s)")
                self.log(f"  {message}")
            else:
                self.log(f"{FAIL_SYMBOL} FAIL: {test_name} ({elapsed:.2f}s)")
                self.log(f"  {message}")
            
            self.results[test_name] = {
                "success": result,
                "message": message,
                "elapsed": elapsed
            }
            
            return result, message
            
        except Exception as e:
            elapsed = time.time() - start_time
            error_msg = f"Test failed with exception: {str(e)}"
            self.logger.error(error_msg)
            traceback.print_exc()
            
            self.results[test_name] = {
                "success": False,
                "message": error_msg,
                "elapsed": elapsed,
                "exception": str(e)
            }
            
            return False, error_msg
    
    def _test_crawler_init(self) -> Tuple[bool, str]:
        """Test if the crawler instance can be created"""
        try:
            if self.crawler is not None:
                # Check critical attributes
                attributes = [
                    'visited_urls', 'scraped_data', 'session',
                    'progress_updated', 'status_updated', 'crawl_completed'
                ]
                
                missing = []
                for attr in attributes:
                    if not hasattr(self.crawler, attr):
                        missing.append(attr)
                
                if not missing:
                    return True, "Crawler instance created successfully with all required attributes"
                else:
                    return False, f"Crawler instance missing required attributes: {', '.join(missing)}"
            else:
                return False, "Crawler instance is None"
        except Exception as e:
            return False, f"Error creating crawler instance: {str(e)}"
    
    def _test_user_agent(self) -> Tuple[bool, str]:
        """Test if User-Agent is properly configured"""
        try:
            if self.crawler is None:
                return False, "Crawler instance is not initialized"
            
            # Check if session has User-Agent header
            user_agent = self.crawler.session.headers.get('User-Agent')
            
            if user_agent and len(user_agent) > 0:
                return True, f"User-Agent is configured: {user_agent}"
            else:
                return False, "User-Agent is not configured"
        except Exception as e:
            return False, f"Error checking User-Agent: {str(e)}"
    
    def _test_url_validation(self) -> Tuple[bool, str]:
        """Test URL validation logic"""
        try:
            # Set of valid and invalid test URLs
            valid_urls = [
                "https://www.example.com",
                "http://example.com/page.html",
                "https://example.com/path/to/resource?param=value",
                TEST_URL
            ]
            
            invalid_urls = [
                "",  # Empty
                "example.com",  # Missing scheme
                "file:///C:/path/to/file.html",  # File URL
                "javascript:alert('test')",  # JavaScript URL
                "https://"  # Incomplete URL
            ]
            
            valid_fails = []
            invalid_passes = []
            
            # Check if valid URLs pass validation
            for url in valid_urls:
                try:
                    import urllib.parse
                    parsed = urllib.parse.urlparse(url)
                    if not parsed.scheme or not parsed.netloc:
                        valid_fails.append(url)
                except Exception:
                    valid_fails.append(url)
            
            # Check if invalid URLs fail validation
            for url in invalid_urls:
                try:
                    import urllib.parse
                    parsed = urllib.parse.urlparse(url)
                    if parsed.scheme and parsed.netloc:
                        # This should be invalid but passed
                        invalid_passes.append(url)
                except Exception:
                    # Exception is expected for invalid URLs
                    pass
            
            if not valid_fails and not invalid_passes:
                return True, "URL validation logic is working correctly"
            else:
                issues = []
                if valid_fails:
                    issues.append(f"Valid URLs that failed validation: {', '.join(valid_fails)}")
                if invalid_passes:
                    issues.append(f"Invalid URLs that passed validation: {', '.join(invalid_passes)}")
                return False, f"URL validation issues found: {'; '.join(issues)}"
        except Exception as e:
            return False, f"Error testing URL validation: {str(e)}"
    
    def _test_static_crawl(self) -> Tuple[bool, str]:
        """Test static crawling of a single page"""
        try:
            if self.crawler is None:
                return False, "Crawler instance is not initialized"
            
            # Reset crawler state
            self.crawler.visited_urls = set()
            self.crawler.scraped_data = []
            
            # Variables to store results from callback
            crawl_results = None
            crawl_error = None
            
            # Define callbacks
            def on_complete(results):
                nonlocal crawl_results
                crawl_results = results
            
            def on_error(error_msg):
                nonlocal crawl_error
                crawl_error = error_msg
            
            # Connect signals
            self.crawler.crawl_completed.connect(on_complete)
            self.crawler.error_occurred.connect(on_error)
            
            # Perform the crawl
            self.crawler.crawl(
                start_url=TEST_URL,
                max_pages=1,  # Just one page
                depth=0,      # No link following
                dynamic=False,
                delay=1.0
            )
            
            # Wait for results with timeout
            timeout = 30  # 30 seconds max
            start_time = time.time()
            
            while crawl_results is None and crawl_error is None and (time.time() - start_time) < timeout:
                time.sleep(0.5)
            
            # Disconnect signals
            self.crawler.crawl_completed.disconnect(on_complete)
            self.crawler.error_occurred.disconnect(on_error)
            
            if crawl_error:
                return False, f"Static crawl failed with error: {crawl_error}"
            elif crawl_results is None:
                return False, f"Static crawl timed out after {timeout} seconds"
            elif not crawl_results:
                return False, "Static crawl returned empty results"
            else:
                # Save results to file
                result_file = os.path.join(TEST_OUTPUT_DIR, "static_crawl_test.json")
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(crawl_results, f, indent=2)
                
                # Check if key data was extracted
                if isinstance(crawl_results, list) and len(crawl_results) > 0:
                    first_page = crawl_results[0]
                    has_title = bool(first_page.get("title"))
                    has_content = bool(first_page.get("content"))
                    has_url = bool(first_page.get("url"))
                    
                    if has_title and has_content and has_url:
                        return True, f"Static crawl successful, extracted page with title, content, and URL"
                    else:
                        missing = []
                        if not has_title: missing.append("title")
                        if not has_content: missing.append("content")
                        if not has_url: missing.append("url")
                        return False, f"Static crawl completed but missing data: {', '.join(missing)}"
                else:
                    return False, "Static crawl results have invalid format"
        except Exception as e:
            return False, f"Error during static crawl test: {str(e)}"
    
    def _test_webdriver_availability(self) -> Tuple[bool, str]:
        """Test if WebDriver is available for dynamic crawling"""
        try:
            if self.crawler is None:
                return False, "Crawler instance is not initialized"
            
            # Initialize Selenium
            self.crawler.initialize_selenium()
            
            if self.crawler.driver is not None:
                # Get browser information
                capabilities = self.crawler.driver.capabilities
                browser_name = capabilities.get('browserName', 'unknown')
                browser_version = capabilities.get('browserVersion', 'unknown')
                
                return True, f"WebDriver initialized successfully: {browser_name} {browser_version}"
            else:
                return False, "WebDriver initialization failed"
        except Exception as e:
            return False, f"Error initializing WebDriver: {str(e)}"
    
    def _test_dynamic_crawl(self) -> Tuple[bool, str]:
        """Test dynamic crawling of a single page"""
        try:
            if self.crawler is None:
                return False, "Crawler instance is not initialized"
            
            # Reset crawler state
            self.crawler.visited_urls = set()
            self.crawler.scraped_data = []
            
            # Variables to store results from callback
            crawl_results = None
            crawl_error = None
            
            # Define callbacks
            def on_complete(results):
                nonlocal crawl_results
                crawl_results = results
            
            def on_error(error_msg):
                nonlocal crawl_error
                crawl_error = error_msg
            
            # Connect signals
            self.crawler.crawl_completed.connect(on_complete)
            self.crawler.error_occurred.connect(on_error)
            
            # Perform the crawl
            self.crawler.crawl(
                start_url=TEST_URL,
                max_pages=1,  # Just one page
                depth=0,      # No link following
                dynamic=True, # Use Selenium
                delay=1.0
            )
            
            # Wait for results with timeout
            timeout = 60  # 60 seconds max for dynamic mode
            start_time = time.time()
            
            while crawl_results is None and crawl_error is None and (time.time() - start_time) < timeout:
                time.sleep(0.5)
            
            # Disconnect signals
            self.crawler.crawl_completed.disconnect(on_complete)
            self.crawler.error_occurred.disconnect(on_error)
            
            if crawl_error:
                return False, f"Dynamic crawl failed with error: {crawl_error}"
            elif crawl_results is None:
                return False, f"Dynamic crawl timed out after {timeout} seconds"
            elif not crawl_results:
                return False, "Dynamic crawl returned empty results"
            else:
                # Save results to file
                result_file = os.path.join(TEST_OUTPUT_DIR, "dynamic_crawl_test.json")
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(crawl_results, f, indent=2)
                
                # Check if key data was extracted
                if isinstance(crawl_results, list) and len(crawl_results) > 0:
                    first_page = crawl_results[0]
                    has_title = bool(first_page.get("title"))
                    has_content = bool(first_page.get("content"))
                    has_url = bool(first_page.get("url"))
                    
                    if has_title and has_content and has_url:
                        return True, f"Dynamic crawl successful, extracted page with title, content, and URL"
                    else:
                        missing = []
                        if not has_title: missing.append("title")
                        if not has_content: missing.append("content")
                        if not has_url: missing.append("url")
                        return False, f"Dynamic crawl completed but missing data: {', '.join(missing)}"
                else:
                    return False, "Dynamic crawl results have invalid format"
        except Exception as e:
            return False, f"Error during dynamic crawl test: {str(e)}"
    
    def _test_webdriver_cleanup(self) -> Tuple[bool, str]:
        """Test WebDriver cleanup"""
        try:
            if self.crawler is None:
                return False, "Crawler instance is not initialized"
            
            # Call stop() to cleanup
            self.crawler.stop()
            
            # Check if driver is None after cleanup
            if self.crawler.driver is None:
                return True, "WebDriver successfully cleaned up"
            else:
                return False, "WebDriver not properly cleaned up"
        except Exception as e:
            return False, f"Error testing WebDriver cleanup: {str(e)}"
    
    def format_results(self) -> str:
        """Format test results as readable output"""
        if not self.results:
            return "No test results available."
        
        output = []
        output.append("=" * 60)
        output.append("SUPERCRAWLER CRAWLER SELF-TEST RESULTS")
        output.append("=" * 60)
        output.append("")
        
        for test_name, result in self.results.items():
            status = f"{PASS_SYMBOL} PASS" if result["success"] else f"{FAIL_SYMBOL} FAIL"
            elapsed = result.get("elapsed", 0)
            message = result.get("message", "No message")
            
            output.append(f"{status}: {test_name} ({elapsed:.2f}s)")
            output.append(f"  {message}")
            output.append("")
        
        output.append("-" * 60)
        output.append(f"SUMMARY: {self.passed_count}/{self.test_count} tests passed")
        
        # Add recommendations
        output.append("\nRECOMMENDATIONS:")
        
        if not self.results.get("WebDriver Availability", {}).get("success", False):
            output.append("- Install Chrome or Edge browser for dynamic crawling")
            output.append("- Check system PATH and WebDriver configuration")
        
        if not self.results.get("Single Page Crawl (Static)", {}).get("success", False):
            output.append("- Check internet connectivity")
            output.append("- Verify that the test URL is accessible")
            output.append("- Check if your IP is being rate-limited or blocked")
        
        if (self.results.get("WebDriver Availability", {}).get("success", True) and 
            not self.results.get("Single Page Crawl (Dynamic)", {}).get("success", False)):
            output.append("- Check browser compatibility with Selenium")
            output.append("- Try updating WebDriver and browser versions")
            output.append("- Check if JavaScript is enabled in the browser")
        
        output.append("")
        output.append("=" * 60)
        
        return "\n".join(output)


def run_crawler_self_test():
    """Run the crawler self-test"""
    print("SuperCrawler Crawler Self-Test")
    print("==============================")
    
    # Set up logger if not already set up
    setup_logger()
    
    # Create and run the self-test
    tester = CrawlerSelfTest(verbose=True)
    tester.run_all_tests()
    
    # Print formatted results
    print("\n" + tester.format_results())


if __name__ == "__main__":
    run_crawler_self_test() 