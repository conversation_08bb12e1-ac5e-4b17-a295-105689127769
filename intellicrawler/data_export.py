import json
import pandas as pd
import xml.dom.minidom as md
from PyQt5.QtCore import QObject, pyqtSignal
from intellicrawler.utils.logger import get_logger

class DataExporter(QObject):
    """Handles exporting crawled data in various formats"""
    
    # Signals
    export_completed = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger()
    
    def export_json(self, data, filepath):
        """Export data as JSON"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Data exported as JSON to {filepath}")
            self.export_completed.emit(f"Data successfully exported to {filepath}")
            return True
        except Exception as e:
            error_msg = f"Failed to export JSON: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def export_csv(self, data, filepath):
        """Export data as CSV"""
        try:
            # Prepare data for CSV format
            csv_data = []
            
            for item in data:
                # Extract basic fields for CSV
                row = {
                    'url': item.get('url', ''),
                    'title': item.get('title', ''),
                    'timestamp': item.get('timestamp', ''),
                    'depth': item.get('depth', 0),
                    # Truncate content for CSV
                    'content': item.get('content', '')[:1000].replace('\n', ' ').replace('\r', '')
                }
                csv_data.append(row)
            
            # Write to CSV
            if csv_data:
                df = pd.DataFrame(csv_data)
                df.to_csv(filepath, index=False, encoding='utf-8')
                
                self.logger.info(f"Data exported as CSV to {filepath}")
                self.export_completed.emit(f"Data successfully exported to {filepath}")
                return True
            else:
                self.error_occurred.emit("No data to export")
                return False
                
        except Exception as e:
            error_msg = f"Failed to export CSV: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def export_excel(self, data, filepath):
        """Export data as Excel spreadsheet"""
        try:
            # Prepare data for Excel format
            excel_data = []
            
            for item in data:
                # Extract fields for Excel
                row = {
                    'URL': item.get('url', ''),
                    'Title': item.get('title', ''),
                    'Timestamp': item.get('timestamp', ''),
                    'Depth': item.get('depth', 0),
                    'Content Preview': item.get('content', '')[:500].replace('\n', ' ')
                }
                excel_data.append(row)
            
            # Write to Excel
            if excel_data:
                df = pd.DataFrame(excel_data)
                df.to_excel(filepath, index=False, engine='openpyxl')
                
                self.logger.info(f"Data exported as Excel to {filepath}")
                self.export_completed.emit(f"Data successfully exported to {filepath}")
                return True
            else:
                self.error_occurred.emit("No data to export")
                return False
                
        except Exception as e:
            error_msg = f"Failed to export Excel file: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def export_xml(self, data, filepath):
        """Export data as XML"""
        try:
            # Create XML document
            doc = md.getDOMImplementation().createDocument(None, "SuperCrawlerData", None)
            root = doc.documentElement
            
            # Add each page as a node
            for item in data:
                page_elem = doc.createElement("Page")
                
                # Add elements for each field
                for field in ['url', 'title', 'timestamp', 'depth']:
                    if field in item:
                        elem = doc.createElement(field)
                        text = doc.createTextNode(str(item[field]))
                        elem.appendChild(text)
                        page_elem.appendChild(elem)
                
                # Add content with CDATA
                if 'content' in item:
                    content_elem = doc.createElement("content")
                    cdata = doc.createCDATASection(item['content'][:2000])  # Limit content size
                    content_elem.appendChild(cdata)
                    page_elem.appendChild(content_elem)
                
                root.appendChild(page_elem)
            
            # Write to file with pretty formatting
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(doc.toprettyxml(indent="  "))
            
            self.logger.info(f"Data exported as XML to {filepath}")
            self.export_completed.emit(f"Data successfully exported to {filepath}")
            return True
                
        except Exception as e:
            error_msg = f"Failed to export XML: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def export_text(self, data, filepath):
        """Export data as plain text"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                for i, item in enumerate(data):
                    f.write(f"--- Page {i+1} ---\n")
                    f.write(f"URL: {item.get('url', '')}\n")
                    f.write(f"Title: {item.get('title', '')}\n")
                    f.write(f"Timestamp: {item.get('timestamp', '')}\n")
                    f.write(f"Depth: {item.get('depth', 0)}\n")
                    f.write("\nContent:\n")
                    f.write(item.get('content', '')[:2000])  # Limit content size
                    f.write("\n\n" + "="*50 + "\n\n")
            
            self.logger.info(f"Data exported as text to {filepath}")
            self.export_completed.emit(f"Data successfully exported to {filepath}")
            return True
                
        except Exception as e:
            error_msg = f"Failed to export text: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def export_report(self, report_data, filepath, format='pdf'):
        """Export analysis report in various formats"""
        try:
            if format == 'pdf':
                # Implementation for PDF export would go here
                # Requires additional library like reportlab
                pass
            elif format == 'docx':
                # Implementation for DOCX export would go here
                # Requires additional library like python-docx
                pass
            elif format == 'md':
                # Export as Markdown
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(f"# {report_data.get('title', 'SuperCrawler Report')}\n\n")
                    f.write(f"*Generated on: {report_data.get('generated_at', '')}*\n\n")
                    f.write(f"Report type: {report_data.get('report_type', 'standard')}\n")
                    f.write(f"Analyzed pages: {report_data.get('page_count', 0)}\n\n")
                    f.write("## Sources\n\n")
                    for url in report_data.get('sources', []):
                        f.write(f"- {url}\n")
                    f.write("\n## Content\n\n")
                    f.write(report_data.get('content', ''))
                
                self.logger.info(f"Report exported as Markdown to {filepath}")
                self.export_completed.emit(f"Report successfully exported to {filepath}")
                return True
            else:
                # Default to JSON for unknown formats
                return self.export_json(report_data, filepath)
                
        except Exception as e:
            error_msg = f"Failed to export report: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False 