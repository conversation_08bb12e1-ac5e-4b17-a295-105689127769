#!/usr/bin/env python3
"""
Minimal test for IntelliCrawler environment configuration
Tests the .env file and validation system directly
"""

import os
import tempfile
from pathlib import Path

# Test our .env file directly
def test_env_file_format():
    """Test that our .env file has the correct format"""
    print("=" * 60)
    print("📄 TESTING .ENV FILE FORMAT")
    print("=" * 60)
    
    env_file_path = Path(__file__).parent / ".env"
    
    if not env_file_path.exists():
        print("❌ .env file not found!")
        return False
    
    print(f"✅ .env file exists: {env_file_path}")
    
    try:
        with open(env_file_path, 'r') as f:
            content = f.read()
        
        print(f"📄 File size: {len(content)} characters")
        
        # Check for required sections
        required_sections = [
            "REQUIRED CONFIGURATION",
            "DEEPSEEK_API_KEY=",
            "BROWSER CONFIGURATION", 
            "CHROME_PATH=",
            "CRAWLING BEHAVIOR SETTINGS",
            "USER_AGENT=",
            "MAX_PAGES_DEFAULT=",
            "CRAWL_DEPTH_DEFAULT=",
            "DELAY_DEFAULT="
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in content:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ Missing sections: {missing_sections}")
            return False
        else:
            print("✅ All required sections present")
        
        # Count lines and variables
        lines = content.split('\n')
        variable_lines = [line for line in lines if '=' in line and not line.strip().startswith('#')]
        
        print(f"📊 Total lines: {len(lines)}")
        print(f"📊 Variable definitions: {len(variable_lines)}")
        
        # Show some variables
        print("🔍 Sample variables found:")
        for line in variable_lines[:8]:  # Show first 8 variables
            if '=' in line:
                var_name = line.split('=')[0].strip()
                print(f"  ✅ {var_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def test_env_template_format():
    """Test that our .env.template file has the correct format"""
    print("\n" + "=" * 60)
    print("📋 TESTING .ENV TEMPLATE FORMAT")
    print("=" * 60)
    
    template_file_path = Path(__file__).parent / ".env.template"
    
    if not template_file_path.exists():
        print("⚠️  .env.template file not found - this is optional")
        return True
    
    print(f"✅ .env.template file exists: {template_file_path}")
    
    try:
        with open(template_file_path, 'r') as f:
            content = f.read()
        
        print(f"📄 Template size: {len(content)} characters")
        
        # Check that template has placeholder values
        if "your_deepseek_api_key_here" in content:
            print("✅ Template has API key placeholder")
        else:
            print("❌ Template missing API key placeholder")
        
        # Count variables in template
        lines = content.split('\n')
        variable_lines = [line for line in lines if '=' in line and not line.strip().startswith('#')]
        print(f"📊 Template variables: {len(variable_lines)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading .env.template file: {e}")
        return False

def test_environment_variable_types():
    """Test parsing different environment variable types"""
    print("\n" + "=" * 60)
    print("🧪 TESTING ENVIRONMENT VARIABLE PARSING")
    print("=" * 60)
    
    # Test different variable types
    test_cases = [
        # (env_var, expected_type, expected_value, description)
        ("DEEPSEEK_API_KEY", str, "sk-12345test", "API key string"),
        ("MAX_PAGES_DEFAULT", int, 100, "Integer value"),
        ("CRAWL_DEPTH_DEFAULT", int, 3, "Integer value"),
        ("DELAY_DEFAULT", float, 1.5, "Float value"),
        ("RESPECT_ROBOTS", bool, True, "Boolean true"),
        ("ENABLE_JAVASCRIPT", bool, False, "Boolean false"),
        ("DEBUG_MODE", bool, True, "Boolean true")
    ]
    
    original_env = dict(os.environ)
    success_count = 0
    
    try:
        # Clear environment first
        for var_name, _, _, _ in test_cases:
            os.environ.pop(var_name, None)
        
        for var_name, expected_type, test_value, description in test_cases:
            print(f"\n🧪 Testing {var_name} ({description}):")
            
            # Set the environment variable as string (as they always are in env)
            if expected_type == bool:
                os.environ[var_name] = "true" if test_value else "false"
            else:
                os.environ[var_name] = str(test_value)
            
            print(f"  📝 Set {var_name}={os.environ[var_name]}")
            
            # Test parsing
            if expected_type == str:
                parsed_value = os.getenv(var_name, "")
                success = parsed_value == str(test_value)
            elif expected_type == int:
                try:
                    parsed_value = int(os.getenv(var_name, "0"))
                    success = parsed_value == test_value
                except ValueError:
                    success = False
            elif expected_type == float:
                try:
                    parsed_value = float(os.getenv(var_name, "0.0"))
                    success = parsed_value == test_value
                except ValueError:
                    success = False
            elif expected_type == bool:
                parsed_value = os.getenv(var_name, "false").lower() == "true"
                success = parsed_value == test_value
            else:
                success = False
            
            if success:
                print(f"  ✅ Parsed correctly: {parsed_value} ({type(parsed_value).__name__})")
                success_count += 1
            else:
                print(f"  ❌ Parse failed: {parsed_value} (expected {test_value})")
        
        print(f"\n📊 Parsing tests: {success_count}/{len(test_cases)} passed")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ Environment variable parsing test failed: {e}")
        return False
        
    finally:
        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)

def test_config_completeness():
    """Test that configuration covers all needed aspects"""
    print("\n" + "=" * 60)
    print("🔍 TESTING CONFIGURATION COMPLETENESS")
    print("=" * 60)
    
    # Check if .env file covers all the aspects we need
    env_file_path = Path(__file__).parent / ".env"
    
    if not env_file_path.exists():
        print("❌ .env file not found for completeness check")
        return False
    
    try:
        with open(env_file_path, 'r') as f:
            content = f.read()
        
        # Categories of configuration we should have
        expected_categories = {
            "API Configuration": ["DEEPSEEK_API_KEY"],
            "Browser Settings": ["CHROME_PATH", "ENABLE_JAVASCRIPT"],
            "Crawling Behavior": ["MAX_PAGES_DEFAULT", "CRAWL_DEPTH_DEFAULT", "DELAY_DEFAULT", "USER_AGENT"],
            "Advanced Options": ["RESPECT_ROBOTS", "REQUEST_TIMEOUT", "MAX_RETRIES"],
            "Debug Options": ["DEBUG_MODE", "VERBOSE_LOGGING"]
        }
        
        missing_vars = []
        found_vars = []
        
        for category, vars_list in expected_categories.items():
            print(f"\n📂 Checking {category}:")
            for var in vars_list:
                if f"{var}=" in content:
                    print(f"  ✅ {var}")
                    found_vars.append(var)
                else:
                    print(f"  ❌ {var} (missing)")
                    missing_vars.append(var)
        
        print(f"\n📊 Configuration coverage:")
        print(f"  ✅ Found: {len(found_vars)} variables")
        print(f"  ❌ Missing: {len(missing_vars)} variables")
        
        if missing_vars:
            print(f"  Missing variables: {missing_vars}")
        
        # Check for documentation/comments
        comment_lines = [line for line in content.split('\n') if line.strip().startswith('#')]
        print(f"  📝 Documentation lines: {len(comment_lines)}")
        
        if len(comment_lines) < 10:
            print("  ⚠️  Could use more documentation")
        else:
            print("  ✅ Well documented")
        
        return len(missing_vars) == 0
        
    except Exception as e:
        print(f"❌ Configuration completeness test failed: {e}")
        return False

def test_graceful_degradation():
    """Test that configuration handles missing variables gracefully"""
    print("\n" + "=" * 60)
    print("🛡️  TESTING GRACEFUL DEGRADATION")
    print("=" * 60)
    
    # Test what happens when environment variables are missing
    original_env = dict(os.environ)
    
    try:
        # Clear critical environment variables
        critical_vars = ['DEEPSEEK_API_KEY', 'MAX_PAGES_DEFAULT', 'CRAWL_DEPTH_DEFAULT']
        
        for var in critical_vars:
            os.environ.pop(var, None)
        
        print("🧪 Testing with missing environment variables...")
        
        # Test basic environment access
        api_key = os.getenv("DEEPSEEK_API_KEY", "")
        max_pages = int(os.getenv("MAX_PAGES_DEFAULT", "100"))  # Default fallback
        crawl_depth = int(os.getenv("CRAWL_DEPTH_DEFAULT", "3"))  # Default fallback
        
        print(f"  🔑 API key: {'(empty)' if not api_key else '(present)'}")
        print(f"  📊 Max pages default: {max_pages}")
        print(f"  📊 Crawl depth default: {crawl_depth}")
        
        # Test that we can handle missing API key gracefully
        if not api_key:
            print("  ✅ API key missing handled gracefully (empty string)")
        
        # Test that numeric defaults work
        if max_pages == 100 and crawl_depth == 3:
            print("  ✅ Numeric defaults applied correctly")
        else:
            print("  ❌ Numeric defaults not working properly")
        
        print("✅ Graceful degradation test passed")
        return True
        
    except Exception as e:
        print(f"❌ Graceful degradation test failed: {e}")
        return False
        
    finally:
        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)

def main():
    """Run all configuration tests"""
    print("🚀 INTELLICRAWLER ENVIRONMENT CONFIGURATION TESTS")
    print("=" * 80)
    
    tests = [
        ("Environment File Format", test_env_file_format),
        ("Template File Format", test_env_template_format),
        ("Environment Variable Types", test_environment_variable_types),
        ("Configuration Completeness", test_config_completeness),
        ("Graceful Degradation", test_graceful_degradation)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running: {test_name}")
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: FAILED with exception: {str(e)}")
    
    print("\n" + "=" * 80)
    print(f"📊 TEST RESULTS")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success rate: {(passed / (passed + failed) * 100):.1f}%")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Environment configuration is properly set up.")
        print("\n📋 CONFIGURATION SUMMARY:")
        print("✅ .env file format is correct")
        print("✅ All required environment variables are defined")
        print("✅ Variable types are properly handled")
        print("✅ Configuration is complete and well-documented")
        print("✅ System handles missing variables gracefully")
        print("\n💡 The configuration system is ready for application startup testing!")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())