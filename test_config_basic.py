#!/usr/bin/env python3
"""
Basic test script for IntelliCrawler configuration system
Tests core configuration functionality without AI dependencies
"""

import os
import sys
import tempfile
import json
from pathlib import Path

# Add the IntelliCrawler module to the path
sys.path.insert(0, str(Path(__file__).parent))

def test_environment_variable_validation():
    """Test environment variable validation with various scenarios"""
    print("=" * 60)
    print("🧪 TESTING ENVIRONMENT VARIABLE VALIDATION")
    print("=" * 60)
    
    # Test scenarios with different environment variable values
    test_scenarios = [
        {
            'name': 'Valid configuration',
            'env_vars': {
                'DEEPSEEK_API_KEY': 'sk-1234567890abcdef1234567890abcdef',
                'MAX_PAGES_DEFAULT': '100',
                'CRAWL_DEPTH_DEFAULT': '3',
                'DELAY_DEFAULT': '1.0',
                'RESPECT_ROBOTS': 'true',
                'ENABLE_JAVASCRIPT': 'true',
            },
            'expected_valid': True
        },
        {
            'name': 'Placeholder API key (warning expected)',
            'env_vars': {
                'DEEPSEEK_API_KEY': 'your_deepseek_api_key_here',
                'MAX_PAGES_DEFAULT': '50',
            },
            'expected_valid': True  # Should be valid but with warnings
        },
        {
            'name': 'Invalid numeric values',
            'env_vars': {
                'DEEPSEEK_API_KEY': 'sk-validkey123456789',
                'MAX_PAGES_DEFAULT': 'not_a_number',
                'CRAWL_DEPTH_DEFAULT': '-1',
                'DELAY_DEFAULT': 'invalid'
            },
            'expected_valid': False
        },
        {
            'name': 'Out of range values (warnings expected)',
            'env_vars': {
                'DEEPSEEK_API_KEY': 'sk-validkey123456789',
                'MAX_PAGES_DEFAULT': '5000',  # Too high
                'CRAWL_DEPTH_DEFAULT': '20',  # Too high
                'DELAY_DEFAULT': '0.01'      # Too low
            },
            'expected_valid': True  # Valid but with warnings
        }
    ]
    
    original_env = dict(os.environ)
    
    try:
        from intellicrawler.utils.config import validate_environment_variables
        
        for scenario in test_scenarios:
            print(f"\n🧪 Testing scenario: {scenario['name']}")
            print("-" * 40)
            
            # Clear relevant environment variables
            env_keys_to_clear = ['DEEPSEEK_API_KEY', 'MAX_PAGES_DEFAULT', 'CRAWL_DEPTH_DEFAULT', 'DELAY_DEFAULT', 'CHROME_PATH']
            for key in env_keys_to_clear:
                os.environ.pop(key, None)
            
            # Set test environment variables
            for key, value in scenario['env_vars'].items():
                os.environ[key] = value
                print(f"  📝 Set {key}={value}")
            
            # Run validation
            validation = validate_environment_variables()
            
            print(f"  📊 Result: {'✅ VALID' if validation['valid'] else '❌ INVALID'}")
            print(f"  ❌ Errors: {len(validation['errors'])}")
            print(f"  ⚠️  Warnings: {len(validation['warnings'])}")
            print(f"  ℹ️  Info: {len(validation['info'])}")
            
            # Print details
            for error in validation['errors']:
                print(f"    ❌ {error['variable']}: {error['message']}")
            for warning in validation['warnings']:
                print(f"    ⚠️  {warning['variable']}: {warning['message']}")
                
        return True
        
    except Exception as e:
        print(f"❌ Environment variable validation test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)

def test_env_loading():
    """Test loading environment variables"""
    print("\n" + "=" * 60)
    print("🔧 TESTING ENVIRONMENT VARIABLE LOADING")
    print("=" * 60)
    
    original_env = dict(os.environ)
    
    try:
        # Set some test environment variables
        test_env_vars = {
            'DEEPSEEK_API_KEY': 'sk-test123456789abcdef',
            'MAX_PAGES_DEFAULT': '150',
            'CRAWL_DEPTH_DEFAULT': '5',
            'DELAY_DEFAULT': '2.0',
            'RESPECT_ROBOTS': 'false',
            'ENABLE_JAVASCRIPT': 'true',
            'DEBUG_MODE': 'true'
        }
        
        for key, value in test_env_vars.items():
            os.environ[key] = value
        
        from intellicrawler.utils.config import load_env_variables
        
        env_vars = load_env_variables()
        
        print(f"✅ Environment variables loaded")
        print(f"📋 Loaded keys: {list(env_vars.keys())}")
        
        # Verify some key values
        expected_checks = [
            ('deepseek_api_key', 'sk-test123456789abcdef'),
            ('max_pages_default', 150),
            ('crawl_depth_default', 5),
            ('delay_default', 2.0),
            ('respect_robots', False),
            ('enable_javascript', True),
            ('debug_mode', True)
        ]
        
        for key, expected_value in expected_checks:
            actual_value = env_vars.get(key)
            if actual_value == expected_value:
                print(f"  ✅ {key}: {actual_value} (correct)")
            else:
                print(f"  ❌ {key}: {actual_value} (expected {expected_value})")
        
        return True
        
    except Exception as e:
        print(f"❌ Environment loading test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)

def test_config_without_ai():
    """Test basic configuration loading without AI components"""
    print("\n" + "=" * 60)
    print("📁 TESTING BASIC CONFIG LOADING")
    print("=" * 60)
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # Mock the config directory
            import intellicrawler.utils.config as config_module
            original_config_dir = config_module.CONFIG_DIR
            original_config_file = config_module.CONFIG_FILE
            original_env_file = config_module.ENV_FILE
            
            config_module.CONFIG_DIR = temp_dir
            config_module.CONFIG_FILE = os.path.join(temp_dir, "config.json")
            config_module.ENV_FILE = os.path.join(temp_dir, ".env")
            
            from intellicrawler.utils.config import create_default_config, save_config, load_config
            
            print("🏗️  Testing default config creation...")
            default_config = create_default_config()
            print(f"✅ Default config created with {len(default_config)} keys")
            print(f"🔍 Sample keys: {list(default_config.keys())[:5]}...")
            
            print("🧪 Testing config modification and saving...")
            test_config = default_config.copy()
            test_config['test_key'] = 'test_value'
            test_config['deepseek_api_key'] = 'test_api_key_12345'
            
            save_result = save_config(test_config)
            print(f"💾 Save result: {'✅ Success' if save_result else '❌ Failed'}")
            
            # Verify file was created
            config_file_exists = os.path.exists(config_module.CONFIG_FILE)
            print(f"📁 Config file exists: {'✅ Yes' if config_file_exists else '❌ No'}")
            
            if config_file_exists:
                with open(config_module.CONFIG_FILE, 'r') as f:
                    saved_data = json.load(f)
                print(f"💾 Saved data has {len(saved_data)} keys")
                print(f"🔍 Test key in saved data: {'✅ Yes' if 'test_key' in saved_data else '❌ No'}")
            
            print("🔄 Testing config loading...")
            loaded_config = load_config()
            print(f"📥 Loaded config with {len(loaded_config)} keys")
            print(f"🔍 Test key preserved: {'✅ Yes' if loaded_config.get('test_key') == 'test_value' else '❌ No'}")
            
            return True
            
        except Exception as e:
            print(f"❌ Config operations test failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
        finally:
            # Restore original paths
            config_module.CONFIG_DIR = original_config_dir
            config_module.CONFIG_FILE = original_config_file
            config_module.ENV_FILE = original_env_file

def test_dotenv_file():
    """Test .env file loading"""
    print("\n" + "=" * 60)
    print("📄 TESTING .ENV FILE LOADING")
    print("=" * 60)
    
    # Create temporary .env file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
        f.write("""# Test .env file
DEEPSEEK_API_KEY=sk-testenv123456789
MAX_PAGES_DEFAULT=75
CRAWL_DEPTH_DEFAULT=2
DELAY_DEFAULT=1.5
RESPECT_ROBOTS=true
DEBUG_MODE=false
""")
        temp_env_file = f.name
    
    try:
        # Mock the ROOT_ENV_FILE to point to our test file
        import intellicrawler.utils.config as config_module
        original_root_env = config_module.ROOT_ENV_FILE
        config_module.ROOT_ENV_FILE = temp_env_file
        
        from intellicrawler.utils.config import load_env_variables
        
        env_vars = load_env_variables()
        
        print(f"✅ .env file loaded")
        print(f"📋 Loaded environment variables:")
        
        expected_values = {
            'deepseek_api_key': 'sk-testenv123456789',
            'max_pages_default': 75,
            'crawl_depth_default': 2,
            'delay_default': 1.5,
            'respect_robots': True,
            'debug_mode': False
        }
        
        for key, expected in expected_values.items():
            actual = env_vars.get(key)
            if actual == expected:
                print(f"  ✅ {key}: {actual}")
            else:
                print(f"  ❌ {key}: {actual} (expected {expected})")
        
        return True
        
    except Exception as e:
        print(f"❌ .env file test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        try:
            os.unlink(temp_env_file)
            config_module.ROOT_ENV_FILE = original_root_env
        except:
            pass

def main():
    """Run all configuration tests"""
    print("🚀 INTELLICRAWLER CONFIGURATION SYSTEM TESTS (BASIC)")
    print("=" * 80)
    
    tests = [
        ("Environment Variable Validation", test_environment_variable_validation),
        ("Environment Variable Loading", test_env_loading),
        ("Basic Config Operations", test_config_without_ai),
        (".env File Loading", test_dotenv_file)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running: {test_name}")
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: FAILED with exception: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 80)
    print(f"📊 TEST RESULTS")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success rate: {(passed / (passed + failed) * 100):.1f}%")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Configuration system is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())