# Setup script for IntelliCrawler
from setuptools import setup, find_packages
import os

# Read the README file for long description
def read_readme():
    try:
        with open("README.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return "IntelliCrawler - Advanced AI-Powered Web Crawling Platform"

# Read requirements from requirements.txt
def read_requirements():
    try:
        with open("requirements.txt", "r", encoding="utf-8") as f:
            return [line.strip() for line in f 
                   if line.strip() and not line.startswith("#") 
                   and "extra ==" not in line]
    except FileNotFoundError:
        return [
            "requests>=2.31.0",
            "beautifulsoup4>=4.12.0",
            "openai>=1.0.0",
            "PyQt5>=5.15.0",
            "pandas>=2.0.0",
            "lxml>=4.9.0",
            "selenium>=4.15.0",
            "webdriver-manager>=4.0.0",
            "nltk>=3.8.0",
            "pygments>=2.15.0",
            "tqdm>=4.65.0",
            "python-dotenv>=1.0.0",
            "pygame>=2.5.0",
        ]

setup(
    name="IntelliCrawler",
    version="2.0.0",
    author="IntelliCrawler Team",
    description="Advanced AI-Powered Web Crawling and Analysis Platform",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/intellicrawler",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: System Administrators", 
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Internet :: WWW/HTTP :: Indexing/Search",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.9",
    install_requires=read_requirements(),
    extras_require={
        "audio": [
            "librosa>=0.10.0",
        ],
        "dev": [
            "pytest>=7.4.0",
            "pytest-cov>=4.1.0", 
            "pytest-qt>=4.2.0",
            "ruff>=0.1.0",
            "mypy>=1.5.0",
            "vulture>=2.9.0",
            "bandit>=1.7.0",
            "safety>=2.3.0",
        ],
        "windows": [
            "pywin32>=306",
            "winshell>=0.6",
        ],
        "all": [
            "librosa>=0.10.0",
            "pytest>=7.4.0",
            "pytest-cov>=4.1.0",
            "pytest-qt>=4.2.0",
            "ruff>=0.1.0",
            "mypy>=1.5.0",
            "vulture>=2.9.0",
            "bandit>=1.7.0",
            "safety>=2.3.0",
            "pywin32>=306; sys_platform=='win32'",
            "winshell>=0.6; sys_platform=='win32'",
        ],
    },
    entry_points={
        "console_scripts": [
            "intellicrawler=intellicrawler.main:main",
            "intellicrawler-launcher=intellicrawler_launcher:main",
        ],
    },
    include_package_data=True,
    package_data={
        "intellicrawler": [
            "ui/resources/*.qss",
            "resources/*.py",
            "*.json",
        ],
    },
    keywords="web-scraping, ai, crawling, automation, data-extraction, selenium, beautifulsoup",
    project_urls={
        "Documentation": "https://github.com/yourusername/intellicrawler/wiki",
        "Bug Reports": "https://github.com/yourusername/intellicrawler/issues",
        "Source": "https://github.com/yourusername/intellicrawler",
        "Changelog": "https://github.com/yourusername/intellicrawler/blob/main/docs/CHANGELOG.md",
    },
) 