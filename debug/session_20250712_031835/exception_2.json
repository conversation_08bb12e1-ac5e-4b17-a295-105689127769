{"exception_type": "ImportError", "exception_message": "cannot import name 'ProxyScraper' from 'intellicrawler.proxy_scraper_simple' (C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\proxy_scraper_simple.py)", "context": "Crawler functionality test", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\debug_launcher.py\", line 329, in test_crawler_functionality\n    from intellicrawler.crawler import Crawler\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\crawler.py\", line 20, in <module>\n    from intellicrawler.proxy_scraper_simple import ProxyScraper  # Add import\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nImportError: cannot import name 'ProxyScraper' from 'intellicrawler.proxy_scraper_simple' (C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\proxy_scraper_simple.py)\n", "timestamp": "2025-07-12T03:18:38.935044"}