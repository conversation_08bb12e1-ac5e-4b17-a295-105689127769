{"debug_session": "20250712_031835", "total_issues": 2, "total_exceptions": 2, "total_screenshots": 6, "system_info": {"platform": "Windows-10-10.0.19045-SP0", "python_version": "3.11.8", "architecture": ["64bit", "WindowsPE"], "processor": "AMD64 Family 23 Model 8 Stepping 2, AuthenticAMD", "memory": {"total": 34281730048, "available": 19450462208, "percent": 43.3}, "disk": {"total": 999507046400, "free": 188545642496, "percent": 81.1}, "timestamp": "2025-07-12T03:18:35.385905"}, "issues": ["Failed to import Crawler", "Failed to test crawler functionality"], "exception_traces": [{"exception_type": "ImportError", "exception_message": "cannot import name 'ProxyScraper' from 'intellicrawler.proxy_scraper_simple' (C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\proxy_scraper_simple.py)", "context": "Crawler import", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\debug_launcher.py\", line 262, in test_intellicrawler_imports\n    from intellicrawler.crawler import Crawler\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\crawler.py\", line 20, in <module>\n    from intellicrawler.proxy_scraper_simple import ProxyScraper  # Add import\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nImportError: cannot import name 'ProxyScraper' from 'intellicrawler.proxy_scraper_simple' (C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\proxy_scraper_simple.py)\n", "timestamp": "2025-07-12T03:18:38.926043"}, {"exception_type": "ImportError", "exception_message": "cannot import name 'ProxyScraper' from 'intellicrawler.proxy_scraper_simple' (C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\proxy_scraper_simple.py)", "context": "Crawler functionality test", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\debug_launcher.py\", line 329, in test_crawler_functionality\n    from intellicrawler.crawler import Crawler\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\crawler.py\", line 20, in <module>\n    from intellicrawler.proxy_scraper_simple import ProxyScraper  # Add import\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nImportError: cannot import name 'ProxyScraper' from 'intellicrawler.proxy_scraper_simple' (C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\proxy_scraper_simple.py)\n", "timestamp": "2025-07-12T03:18:38.935044"}], "screenshots": ["debug\\session_20250712_031835\\screenshot_0_before_show.png", "debug\\session_20250712_031835\\screenshot_1_after_show.png", "debug\\session_20250712_031835\\screenshot_2_runtime_check.png", "debug\\session_20250712_031835\\screenshot_3_runtime_check.png", "debug\\session_20250712_031835\\screenshot_4_runtime_check.png", "debug\\session_20250712_031835\\screenshot_5_runtime_check.png"], "performance_data": [{"timestamp": "2025-07-12T03:18:45.543073", "cpu_percent": 3.9, "memory_percent": 43.8, "disk_io": {"read_count": 469556, "write_count": 437455, "read_bytes": 18407920640, "write_bytes": 11936645120, "read_time": 135, "write_time": 43}, "network_io": {"bytes_sent": 703116125, "bytes_recv": 3763795654, "packets_sent": 695437, "packets_recv": 2951573, "errin": 0, "errout": 0, "dropin": 0, "dropout": 0}}, {"timestamp": "2025-07-12T03:18:47.542221", "cpu_percent": 10.4, "memory_percent": 43.7, "disk_io": {"read_count": 469559, "write_count": 437554, "read_bytes": 18408018944, "write_bytes": 11938803712, "read_time": 135, "write_time": 43}, "network_io": {"bytes_sent": 703117647, "bytes_recv": 3763799487, "packets_sent": 695452, "packets_recv": 2951594, "errin": 0, "errout": 0, "dropin": 0, "dropout": 0}}, {"timestamp": "2025-07-12T03:18:49.555082", "cpu_percent": 19.6, "memory_percent": 43.9, "disk_io": {"read_count": 469560, "write_count": 437631, "read_bytes": 18408051712, "write_bytes": 11940114432, "read_time": 135, "write_time": 43}, "network_io": {"bytes_sent": 703127269, "bytes_recv": 3763809850, "packets_sent": 695477, "packets_recv": 2951629, "errin": 0, "errout": 0, "dropin": 0, "dropout": 0}}, {"timestamp": "2025-07-12T03:18:51.549415", "cpu_percent": 4.9, "memory_percent": 43.8, "disk_io": {"read_count": 469562, "write_count": 437701, "read_bytes": 18408117248, "write_bytes": 11942694912, "read_time": 135, "write_time": 43}, "network_io": {"bytes_sent": 703131574, "bytes_recv": 3763811551, "packets_sent": 695488, "packets_recv": 2951644, "errin": 0, "errout": 0, "dropin": 0, "dropout": 0}}], "log_files": []}