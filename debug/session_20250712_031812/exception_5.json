{"exception_type": "ModuleNotFoundError", "exception_message": "No module named 'pygame'", "context": "GUI launch", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\debug_launcher.py\", line 275, in test_gui_launch\n    from intellicrawler.ui.main_window import MainWindow\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\ui\\main_window.py\", line 11, in <module>\n    from intellicrawler.ui.music_player_tab import MusicPlayerTab\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\ui\\music_player_tab.py\", line 2, in <module>\n    import pygame\nModuleNotFoundError: No module named 'pygame'\n", "timestamp": "2025-07-12T03:18:15.428471"}