{"debug_session": "20250712_031812", "total_issues": 5, "total_exceptions": 5, "total_screenshots": 1, "system_info": {"platform": "Windows-10-10.0.19045-SP0", "python_version": "3.11.8", "architecture": ["64bit", "WindowsPE"], "processor": "AMD64 Family 23 Model 8 Stepping 2, AuthenticAMD", "memory": {"total": 34281730048, "available": 19461738496, "percent": 43.2}, "disk": {"total": 999507046400, "free": 188577107968, "percent": 81.1}, "timestamp": "2025-07-12T03:18:13.006287"}, "issues": ["Failed to import intellicrawler.main", "Failed to import MainWindow", "Failed to import Crawler", "Failed to test crawler functionality", "Failed to launch GUI"], "exception_traces": [{"exception_type": "ModuleNotFoundError", "exception_message": "No module named 'pygame'", "context": "intellicrawler.main import", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\debug_launcher.py\", line 248, in test_intellicrawler_imports\n    from intellicrawler.main import main\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\main.py\", line 9, in <module>\n    from intellicrawler.ui.main_window import MainWindow\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\ui\\main_window.py\", line 11, in <module>\n    from intellicrawler.ui.music_player_tab import MusicPlayerTab\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\ui\\music_player_tab.py\", line 2, in <module>\n    import pygame\nModuleNotFoundError: No module named 'pygame'\n", "timestamp": "2025-07-12T03:18:15.224471"}, {"exception_type": "ModuleNotFoundError", "exception_message": "No module named 'pygame'", "context": "MainWindow import", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\debug_launcher.py\", line 255, in test_intellicrawler_imports\n    from intellicrawler.ui.main_window import MainWindow\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\ui\\main_window.py\", line 11, in <module>\n    from intellicrawler.ui.music_player_tab import MusicPlayerTab\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\ui\\music_player_tab.py\", line 2, in <module>\n    import pygame\nModuleNotFoundError: No module named 'pygame'\n", "timestamp": "2025-07-12T03:18:15.230470"}, {"exception_type": "ModuleNotFoundError", "exception_message": "No module named 'j<PERSON><PERSON>'", "context": "Crawler import", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\debug_launcher.py\", line 262, in test_intellicrawler_imports\n    from intellicrawler.crawler import Crawler\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\crawler.py\", line 18, in <module>\n    import jsonlines  # Add at top if not present\n    ^^^^^^^^^^^^^^^^\nModuleNotFoundError: No module named 'jsonlines'\n", "timestamp": "2025-07-12T03:18:15.413468"}, {"exception_type": "ModuleNotFoundError", "exception_message": "No module named 'j<PERSON><PERSON>'", "context": "Crawler functionality test", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\debug_launcher.py\", line 329, in test_crawler_functionality\n    from intellicrawler.crawler import Crawler\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\crawler.py\", line 18, in <module>\n    import jsonlines  # Add at top if not present\n    ^^^^^^^^^^^^^^^^\nModuleNotFoundError: No module named 'jsonlines'\n", "timestamp": "2025-07-12T03:18:15.422475"}, {"exception_type": "ModuleNotFoundError", "exception_message": "No module named 'pygame'", "context": "GUI launch", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\debug_launcher.py\", line 275, in test_gui_launch\n    from intellicrawler.ui.main_window import MainWindow\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\ui\\main_window.py\", line 11, in <module>\n    from intellicrawler.ui.music_player_tab import MusicPlayerTab\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\ui\\music_player_tab.py\", line 2, in <module>\n    import pygame\nModuleNotFoundError: No module named 'pygame'\n", "timestamp": "2025-07-12T03:18:15.428471"}], "screenshots": ["debug\\session_20250712_031812\\screenshot_0_gui_launch_error.png"], "performance_data": [], "log_files": []}