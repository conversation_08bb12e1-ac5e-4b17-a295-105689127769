{"exception_type": "ModuleNotFoundError", "exception_message": "No module named 'j<PERSON><PERSON>'", "context": "Crawler import", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\debug_launcher.py\", line 262, in test_intellicrawler_imports\n    from intellicrawler.crawler import Crawler\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\crawler.py\", line 18, in <module>\n    import jsonlines  # Add at top if not present\n    ^^^^^^^^^^^^^^^^\nModuleNotFoundError: No module named 'jsonlines'\n", "timestamp": "2025-07-12T03:18:15.413468"}