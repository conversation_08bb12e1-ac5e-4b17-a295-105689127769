{"exception_type": "ModuleNotFoundError", "exception_message": "No module named 'pygame'", "context": "intellicrawler.main import", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\debug_launcher.py\", line 248, in test_intellicrawler_imports\n    from intellicrawler.main import main\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\main.py\", line 9, in <module>\n    from intellicrawler.ui.main_window import MainWindow\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\ui\\main_window.py\", line 11, in <module>\n    from intellicrawler.ui.music_player_tab import MusicPlayerTab\n  File \"C:\\Users\\<USER>\\Desktop\\IntelliCrawler\\intellicrawler\\ui\\music_player_tab.py\", line 2, in <module>\n    import pygame\nModuleNotFoundError: No module named 'pygame'\n", "timestamp": "2025-07-12T03:18:15.224471"}