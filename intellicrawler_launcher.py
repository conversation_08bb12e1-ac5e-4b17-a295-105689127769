#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IntelliCrawler Launcher

This script launches the IntelliCrawler application and creates a desktop shortcut.
Run this script to start using IntelliCrawler.
"""

import os
import sys
import subprocess
import platform
import traceback
import time
import logging
import argparse
import sysconfig
from pathlib import Path

# Parse command line arguments
def parse_arguments():
    """Parse command line arguments for the launcher"""
    parser = argparse.ArgumentParser(description="IntelliCrawler Application Launcher")
 
    parser.add_argument("--url", 
                        help="Starting URL for crawling")
    
    parser.add_argument("--depth", type=int, default=2,
                        help="Crawl depth (default: 2)")
    
    parser.add_argument("--max-pages", type=int, default=50,
                        help="Maximum pages to crawl (default: 50)")
    
    parser.add_argument("--no-dynamic", action="store_true",
                        help="Disable dynamic crawling (JavaScript rendering)")
    
    parser.add_argument("--chrome-path", 
                        help="Path to Chrome browser executable")
    
    parser.add_argument("--delay", type=float, default=1.0,
                        help="Delay between requests in seconds (default: 1.0)")
    
    parser.add_argument("--verbose", action="store_true",
                        help="Enable verbose logging output")
    
    return parser.parse_args()

# Setup basic logging (will be reconfigured later based on --verbose flag)
log_dir = os.path.expanduser("~/.intellicrawler/logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"launcher_{int(time.time())}.log")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def create_windows_shortcut():
    """Create a desktop shortcut for Windows"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        # Get the path to this launcher script
        launcher_path = os.path.abspath(__file__)
        
        # Create shortcut on desktop
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "IntelliCrawler.lnk")
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{launcher_path}"'
        shortcut.WorkingDirectory = os.path.dirname(launcher_path)
        shortcut.IconLocation = sys.executable
        shortcut.Description = "IntelliCrawler - AI-Powered Web Scraping Tool"
        shortcut.save()
        
        logger.info(f"Desktop shortcut created: {shortcut_path}")
        return True
        
    except ImportError:
        logger.warning("Windows shell modules not available. Install pywin32 and winshell for shortcut creation.")
        return False
    except Exception as e:
        logger.error(f"Error creating Windows shortcut: {str(e)}")
        return False

def create_linux_shortcut():
    """Create a desktop launcher for Linux"""
    try:
        # Get the path to this launcher script
        launcher_path = os.path.abspath(__file__)
        
        # Create .desktop file content
        desktop_file_content = f"""[Desktop Entry]
Type=Application
Name=IntelliCrawler
Comment=AI-Powered Web Scraping & Analysis Tool
Exec={sys.executable} "{launcher_path}"
Terminal=false
Categories=Utility;Network;
"""
        
        # Try to find an icon file
        icon_paths = [
            os.path.join(os.path.dirname(launcher_path), "intellicrawler", "resources", "icon.png"),
            os.path.join(os.path.dirname(launcher_path), "icon.png")
        ]
        
        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                desktop_file_content += f"Icon={icon_path}\n"
                break
        
        # Write the desktop file
        home = os.path.expanduser("~")
        desktop_file_path = os.path.join(home, ".local", "share", "applications", "intellicrawler.desktop")
        desktop_link_path = os.path.join(home, "Desktop", "IntelliCrawler.desktop")
        
        # Make sure directory exists
        os.makedirs(os.path.dirname(desktop_file_path), exist_ok=True)
        
        # Create application entry
        with open(desktop_file_path, 'w') as f:
            f.write(desktop_file_content)
        
        # Make it executable
        os.chmod(desktop_file_path, 0o755)
        
        # Add to desktop if desktop directory exists
        desktop_dir = os.path.join(home, "Desktop")
        if os.path.isdir(desktop_dir):
            # Create a link to the .desktop file on the desktop
            if not os.path.exists(desktop_link_path):
                os.symlink(desktop_file_path, desktop_link_path)
        
        logger.info(f"Desktop launcher created: {desktop_file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error creating Linux shortcut: {str(e)}")
        return False

def create_macos_shortcut():
    """Create a desktop shortcut for macOS"""
    try:
        # Get the path to this launcher script
        launcher_path = os.path.abspath(__file__)
        
        # Create an AppleScript to launch the application
        applescript_content = f'''
tell application "Terminal"
    do script "cd '{os.path.dirname(launcher_path)}' && python3 '{launcher_path}'"
end tell
'''
        
        # Save the AppleScript
        home = os.path.expanduser("~")
        script_path = os.path.join(home, "Desktop", "IntelliCrawler.scpt")
        
        with open(script_path, 'w') as f:
            f.write(applescript_content)
        
        # Compile the AppleScript
        subprocess.run(['osacompile', '-o', script_path.replace('.scpt', '.app'), script_path], 
                      check=True)
        
        # Remove the source script
        os.remove(script_path)
        
        logger.info(f"macOS application created: {script_path.replace('.scpt', '.app')}")
        return True
        
    except Exception as e:
        logger.error(f"Error creating macOS shortcut: {str(e)}")
        return False

def create_desktop_shortcut():
    """Create a desktop shortcut based on the current platform"""
    logger.info("Creating desktop shortcut...")
    
    system = platform.system()
    if system == "Windows":
        return create_windows_shortcut()
    elif system == "Linux":
        return create_linux_shortcut()
    elif system == "Darwin":  # macOS
        return create_macos_shortcut()
    else:
        logger.warning(f"Unsupported platform for shortcut creation: {system}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        logger.error(f"Python 3.9+ required. Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro} ✓")
    return True


def is_externally_managed():
    """
    Check if the Python environment is externally managed.
    According to PEP 668, this is determined by the presence of an
    EXTERNALLY-MANAGED file in the standard library directory.
    """
    try:
        # Get the standard library path
        stdlib_path = sysconfig.get_path("stdlib")
        externally_managed_file = os.path.join(stdlib_path, "EXTERNALLY-MANAGED")
        
        # Check if the EXTERNALLY-MANAGED file exists
        return os.path.exists(externally_managed_file)
    except Exception as e:
        logger.debug(f"Error checking for externally managed environment: {str(e)}")
        return False

def install_dependencies():
    """Make sure all required dependencies are installed"""
    try:
        required_packages = [
            "PyQt5",
            "requests",
            "beautifulsoup4",
            "openai",
            "pandas",
            "lxml",
            "selenium",
            "webdriver-manager",
            "nltk",
            "python-dotenv"
        ]
        
        # Windows-specific packages
        if platform.system() == "Windows":
            required_packages.extend(["pywin32", "winshell"])
        
        # Check if we're in an externally managed environment
        externally_managed = is_externally_managed()
        
        if externally_managed:
            logger.info("Detected externally managed Python environment")
            logger.info("Will use --break-system-packages flag for pip installations")
        
        # Install packages that aren't already installed
        for package in required_packages:
            try:
                __import__(package.lower().replace("-", "_").split("[")[0])
                logger.info(f"Package {package} is already installed")
            except ImportError:
                logger.info(f"Installing package: {package}")
                # Build pip command with --break-system-packages if needed
                pip_cmd = [sys.executable, "-m", "pip", "install"]
                if externally_managed:
                    pip_cmd.append("--break-system-packages")
                pip_cmd.append(package)
                
                try:
                    subprocess.check_call(pip_cmd)
                except subprocess.CalledProcessError as e:
                    if externally_managed:
                        logger.error(f"Failed to install {package} even with --break-system-packages")
                        logger.error("You may need to:")
                        logger.error("1. Use a virtual environment: python -m venv venv && source venv/bin/activate")
                        logger.error("2. Use your system's package manager (e.g., apt, yum, brew)")
                        logger.error("3. Install packages manually using your OS package manager")
                    raise e
        
        return True
    except Exception as e:
        logger.error(f"Error installing dependencies: {str(e)}")
        return False
def check_environment():
    """Check if the environment is properly set up"""
    logger.info("Checking environment...")
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check if we can import basic modules
    try:
        import requests
        import bs4
        logger.info("Basic modules available ✓")
    except ImportError as e:
        logger.error(f"Missing required module: {str(e)}")
        return False
    
    return True

def install_package():
    """Install IntelliCrawler package in development mode"""
    try:
        # Get the directory of this script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Check if setup.py exists
        setup_py = os.path.join(script_dir, "setup.py")
        if not os.path.exists(setup_py):
            logger.error(f"setup.py not found at {setup_py}")
            return False
        
        # Check if we're in an externally managed environment
        externally_managed = is_externally_managed()
        
        # Install in development mode
        logger.info("Installing IntelliCrawler package...")
        # Build pip command with --break-system-packages if needed
        pip_cmd = [sys.executable, "-m", "pip", "install", "-e"]
        if externally_managed:
            pip_cmd.append("--break-system-packages")
        pip_cmd.append(script_dir)
        
        subprocess.check_call(pip_cmd)
        
        return True
    except Exception as e:
        logger.error(f"Error installing package: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def launch_application(args):
    """Launch the IntelliCrawler application"""
    try:
        logger.info("Launching IntelliCrawler application...")

        # Try to import the main module
        try:
            # First try direct import
            from intellicrawler.main import main
        except ImportError:
            # If that fails, try installing the package first
            logger.info("Package not found, attempting to install...")
            if not install_package():
                logger.error("Failed to install package")
                return False

        # Now we can import and run
        from intellicrawler.main import main

        # Store command line arguments for the application to use
        if args.url:
            os.environ["INTELLICRAWLER_URL"] = args.url
        if args.chrome_path:
            os.environ["INTELLICRAWLER_CHROME_PATH"] = args.chrome_path
        os.environ["INTELLICRAWLER_DEPTH"] = str(args.depth)
        os.environ["INTELLICRAWLER_MAX_PAGES"] = str(args.max_pages)
        os.environ["INTELLICRAWLER_DYNAMIC"] = str(not args.no_dynamic)
        os.environ["INTELLICRAWLER_DELAY"] = str(args.delay)
        os.environ["INTELLICRAWLER_VERBOSE"] = str(args.verbose)

        # Run the application
        main()
        return True

    except Exception as e:
        logger.error(f"Error launching application: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def main():
    """Main launcher function"""
    print("=" * 60)
    print("🚀 IntelliCrawler Launcher")
    print("   AI-Powered Web Scraping & Analysis Tool")
    print("=" * 60)

    # Parse command line arguments
    args = parse_arguments()

    # Configure verbose logging if requested
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Verbose logging enabled")

    # Check environment
    if not check_environment():
        logger.error("Environment check failed")
        return 1

    # Install dependencies if needed
    if not install_dependencies():
        logger.error("Failed to install dependencies")
        return 1

    # Create desktop shortcut
    create_desktop_shortcut()

    # Launch the application
    if not launch_application(args):
        logger.error("Failed to launch IntelliCrawler")
        print("Error: Failed to launch IntelliCrawler.")
        print(f"Check the launcher log for details: {log_file}")
        return 1

    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except Exception as e:
        logger.error(f"Unhandled exception in launcher: {str(e)}")
        logger.error(traceback.format_exc())

        print("\nError: An unexpected error occurred while launching IntelliCrawler.")
        print(f"Please check the launcher log for details: {log_file}\n")
        print(f"Error: {str(e)}")

        # Keep console window open on Windows
        if platform.system() == "Windows":
            input("\nPress Enter to exit...")

        sys.exit(1)
