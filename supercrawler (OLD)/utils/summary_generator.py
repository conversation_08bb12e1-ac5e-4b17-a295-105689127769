#!/usr/bin/env python3
"""
SuperCrawler Summary Generator Utility
Enhanced summary generation with multiple output formats
"""

import json
import re
import os
from datetime import datetime
from collections import Counter
from typing import Dict, List, Any
from urllib.parse import urlparse

class CrawlerSummaryGenerator:
    """Generate enhanced summaries from crawler output"""
    
    def __init__(self, input_file: str = None, data: List[Dict] = None):
        """Initialize with either file path or data directly"""
        self.input_file = input_file
        self.data = data or []
        if input_file:
            self.base_filename = os.path.splitext(input_file)[0]
        else:
            self.base_filename = "crawler_output"
        
    def load_data(self):
        """Load JSON data from input file"""
        if not self.input_file:
            return
            
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ Loaded {len(self.data)} items from {self.input_file}")
        except Exception as e:
            print(f"❌ Error loading data: {str(e)}")
            raise
            
    def set_data(self, data: List[Dict]):
        """Set data directly (for in-memory processing)"""
        self.data = data
        
    def extract_keywords(self, text: str, top_n: int = 20) -> List[str]:
        """Extract top keywords from text"""
        # Simple keyword extraction
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        # Filter out common words
        stop_words = {
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 
            'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way',
            'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'this', 'that', 'with',
            'have', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time', 'very', 'when',
            'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them',
            'well', 'were', 'will', 'would', 'your', 'about', 'after', 'before', 'other', 'right', 'where'
        }
        filtered_words = [word for word in words if word not in stop_words and len(word) > 3]
        
        # Count and return top keywords
        word_counts = Counter(filtered_words)
        return [word for word, count in word_counts.most_common(top_n)]
        
    def extract_sections(self, content: str) -> List[str]:
        """Extract section headers from content"""
        # Look for markdown headers and other section indicators
        sections = []
        patterns = [
            r'^#+\s+(.+)$',  # Markdown headers
            r'^([A-Z][A-Za-z\s]{2,}):$',  # Title case headers with colon
            r'^([A-Z\s]{3,})$',  # ALL CAPS headers
        ]
        
        for line in content.split('\n'):
            line = line.strip()
            for pattern in patterns:
                match = re.match(pattern, line)
                if match:
                    sections.append(match.group(1).strip())
                    break
                    
        return list(set(sections))[:15]  # Limit to 15 unique sections
        
    def analyze_domains(self, data: List[Dict]) -> Dict[str, int]:
        """Analyze domains in the crawled data"""
        domains = []
        for item in data:
            url = item.get('url', '')
            if url:
                try:
                    domain = urlparse(url).netloc
                    if domain:
                        domains.append(domain)
                except:
                    pass
        return dict(Counter(domains).most_common(10))
        
    def generate_human_summary(self) -> str:
        """Generate human-readable summary"""
        if not self.data:
            return "No data available for summary generation."
            
        # Basic statistics
        total_pages = len(self.data)
        total_content_length = sum(len(item.get('content', '')) for item in self.data)
        
        # Extract all content for analysis
        all_content = ' '.join(item.get('content', '') for item in self.data)
        
        # Get page titles
        titles = [item.get('title', 'Untitled') for item in self.data if item.get('title')]
        
        # Extract keywords and sections
        keywords = self.extract_keywords(all_content)
        
        # Analyze domains
        domains = self.analyze_domains(self.data)
        
        # Get depth distribution
        depths = [item.get('depth', 0) for item in self.data]
        depth_distribution = dict(Counter(depths))
        
        # Generate summary
        summary = f"""SUPERCRAWLER ANALYSIS SUMMARY
{'=' * 50}

OVERVIEW
--------
📊 Total Pages Crawled: {total_pages}
📝 Total Content Length: {total_content_length:,} characters
📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

PAGE TITLES
-----------
"""
        
        # Add first 10 titles
        for i, title in enumerate(titles[:10], 1):
            summary += f"{i:2d}. {title}\n"
        
        if len(titles) > 10:
            summary += f"    ... and {len(titles) - 10} more pages\n"
            
        summary += """
CONTENT ANALYSIS
----------------
🔑 Top Keywords:
"""
        
        # Add keywords in columns
        for i in range(0, min(len(keywords), 20), 4):
            keyword_group = keywords[i:i+4]
            summary += f"   {' | '.join(f'{kw:15s}' for kw in keyword_group)}\n"
            
        # Add domains
        if domains:
            summary += """
📍 Domains Crawled:
"""
            for domain, count in domains.items():
                summary += f"   {domain:30s} ({count} pages)\n"
                
        # Add depth distribution
        summary += """
📊 Crawl Depth Distribution:
"""
        for depth in sorted(depth_distribution.keys()):
            count = depth_distribution[depth]
            bar = '█' * min(count, 50)
            summary += f"   Depth {depth}: {count:3d} pages {bar}\n"
            
        # Add content sample
        if all_content:
            sample_content = all_content[:500].replace('\n', ' ').strip()
            summary += f"""
📄 CONTENT PREVIEW
------------------
{sample_content}...

TECHNICAL DETAILS
-----------------
🔧 Processing completed successfully
📁 Input source: {self.input_file or 'Direct data input'}
⚡ Analysis method: Enhanced summary generation
🎯 Output format: Human-readable text summary

---
Generated by SuperCrawler Enhanced Summary Generator
"""
        
        return summary
        
    def generate_ai_optimized_json(self) -> Dict[str, Any]:
        """Generate AI-optimized JSON format"""
        if not self.data:
            return {"error": "No data available"}
            
        # Prepare structured data for AI consumption
        ai_data = {
            "metadata": {
                "total_pages": len(self.data),
                "generated_at": datetime.now().isoformat(),
                "processing_method": "enhanced_summary_generation",
                "version": "2.0"
            },
            "summary_statistics": {
                "total_content_length": sum(len(item.get('content', '')) for item in self.data),
                "average_content_length": sum(len(item.get('content', '')) for item in self.data) // len(self.data) if self.data else 0,
                "pages_with_titles": len([item for item in self.data if item.get('title')]),
                "depth_distribution": dict(Counter(item.get('depth', 0) for item in self.data))
            },
            "content_analysis": {
                "top_keywords": self.extract_keywords(' '.join(item.get('content', '') for item in self.data)),
                "domains": self.analyze_domains(self.data),
                "page_titles": [item.get('title', 'Untitled') for item in self.data[:20]]  # Limit to first 20
            },
            "pages": []
        }
        
        # Add structured page data
        for i, item in enumerate(self.data):
            page_data = {
                "index": i,
                "title": item.get('title', 'Untitled'),
                "url": item.get('url', ''),
                "depth": item.get('depth', 0),
                "content_length": len(item.get('content', '')),
                "content_preview": item.get('content', '')[:200] + "..." if len(item.get('content', '')) > 200 else item.get('content', ''),
                "full_content": item.get('content', ''),  # Full content for AI processing
                "sections": self.extract_sections(item.get('content', '')),
                "word_count": len(item.get('content', '').split()) if item.get('content') else 0
            }
            ai_data["pages"].append(page_data)
            
        # Add analysis insights
        all_content = ' '.join(item.get('content', '') for item in self.data)
        ai_data["insights"] = {
            "content_themes": self.extract_keywords(all_content, 10),
            "main_topics": self.extract_sections(all_content),
            "content_density": "high" if len(all_content) > 50000 else "medium" if len(all_content) > 10000 else "low",
            "crawl_scope": "broad" if len(set(item.get('depth', 0) for item in self.data)) > 2 else "focused"
        }
        
        return ai_data
        
    def save_human_summary(self, output_path: str = None) -> str:
        """Save human-readable summary to file"""
        if not output_path:
            output_path = f"{self.base_filename}_summary.txt"
            
        summary = self.generate_human_summary()
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(summary)
            print(f"✅ Human summary saved to: {output_path}")
            return output_path
        except Exception as e:
            print(f"❌ Error saving human summary: {str(e)}")
            raise
            
    def save_ai_optimized_json(self, output_path: str = None) -> str:
        """Save AI-optimized JSON to file"""
        if not output_path:
            output_path = f"{self.base_filename}_ai_optimized.json"
            
        ai_data = self.generate_ai_optimized_json()
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(ai_data, f, indent=2, ensure_ascii=False)
            print(f"✅ AI-optimized JSON saved to: {output_path}")
            return output_path
        except Exception as e:
            print(f"❌ Error saving AI-optimized JSON: {str(e)}")
            raise
            
    def generate_both_formats(self, output_dir: str = None) -> Dict[str, str]:
        """Generate both human and AI formats and return file paths"""
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            human_path = os.path.join(output_dir, f"{os.path.basename(self.base_filename)}_summary.txt")
            ai_path = os.path.join(output_dir, f"{os.path.basename(self.base_filename)}_ai_optimized.json")
        else:
            human_path = None
            ai_path = None
            
        human_file = self.save_human_summary(human_path)
        ai_file = self.save_ai_optimized_json(ai_path)
        
        return {
            "human_summary": human_file,
            "ai_optimized": ai_file
        }

def process_crawler_data(data: List[Dict], output_dir: str = None) -> Dict[str, Any]:
    """Convenience function to process crawler data directly"""
    generator = CrawlerSummaryGenerator(data=data)
    
    # Generate summaries
    human_summary = generator.generate_human_summary()
    ai_optimized = generator.generate_ai_optimized_json()
    
    result = {
        "human_summary": human_summary,
        "ai_optimized": ai_optimized,
        "generator": generator  # Return generator for additional operations
    }
    
    # Save to files if output directory specified
    if output_dir:
        files = generator.generate_both_formats(output_dir)
        result.update(files)
        
    return result 