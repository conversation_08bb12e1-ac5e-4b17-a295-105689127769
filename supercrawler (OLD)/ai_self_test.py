#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IntelliCrawler AI Integration Self-Test

This module provides specific tests for the DeepSeek AI integration component,
helping to diagnose issues with the AI functionality.

Usage:
    python -m intellicrawler.ai_self_test
"""

import time
import traceback
import platform
from typing import Dict, <PERSON><PERSON>, Any

from intellicrawler.utils.logger import setup_logger, get_logger
from intellicrawler.ai_integration import DeepSeekAI

# Set up logger
logger = get_logger()

# Define platform-compatible symbols
if platform.system() == "Windows":
    PASS_SYMBOL = "[PASS]"
    FAIL_SYMBOL = "[FAIL]"
else:
    PASS_SYMBOL = "✓"
    FAIL_SYMBOL = "✗"

class AISelfTest:
    """Self-testing for AI integration component"""
    
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.logger = logger
        self.ai_instance = None
        self.results = {}
        self.test_count = 0
        self.passed_count = 0
    
    def log(self, message: str) -> None:
        """Log a message"""
        if self.verbose:
            self.logger.info(message)
            print(message)
    
    def run_all_tests(self) -> Dict[str, Dict[str, Any]]:
        """Run all AI integration tests"""
        self.log("Starting AI integration self-test...")
        self.results = {}
        self.test_count = 0
        self.passed_count = 0
        
        try:
            # Initialize the AI component
            self.ai_instance = DeepSeekAI()
            
            # Run tests
            self.run_test("AI Instance Creation", self._test_ai_creation)
            self.run_test("API Key Configuration", self._test_api_key_config)
            self.run_test("OpenAI Client Setup", self._test_client_setup)
            self.run_test("Available Models", self._test_available_models)
            
            # Only run these tests if we have an API key
            if self.ai_instance.api_key:
                self.run_test("API Authentication", self._test_authentication)
                self.run_test("Simple Content Analysis", self._test_content_analysis)
            
            self.log(f"\nTest Summary: {self.passed_count}/{self.test_count} tests passed")
            return self.results
            
        except Exception as e:
            error_msg = f"Error during AI self-test: {str(e)}"
            self.logger.error(error_msg)
            traceback.print_exc()
            return {"error": {"success": False, "message": error_msg}}
    
    def run_test(self, test_name: str, test_func, *args, **kwargs) -> Tuple[bool, str]:
        """Run a single test and record results"""
        self.test_count += 1
        
        self.log(f"\nRunning test: {test_name}")
        start_time = time.time()
        
        try:
            result, message = test_func(*args, **kwargs)
            elapsed = time.time() - start_time
            
            if result:
                self.passed_count += 1
                self.log(f"{PASS_SYMBOL} PASS: {test_name} ({elapsed:.2f}s)")
                self.log(f"  {message}")
            else:
                self.log(f"{FAIL_SYMBOL} FAIL: {test_name} ({elapsed:.2f}s)")
                self.log(f"  {message}")
            
            self.results[test_name] = {
                "success": result,
                "message": message,
                "elapsed": elapsed
            }
            
            return result, message
            
        except Exception as e:
            elapsed = time.time() - start_time
            error_msg = f"Test failed with exception: {str(e)}"
            self.logger.error(error_msg)
            traceback.print_exc()
            
            self.results[test_name] = {
                "success": False,
                "message": error_msg,
                "elapsed": elapsed,
                "exception": str(e)
            }
            
            return False, error_msg
    
    def _test_ai_creation(self) -> Tuple[bool, str]:
        """Test if the AI instance can be created"""
        try:
            if self.ai_instance is not None:
                # Check critical attributes
                attributes = [
                    'status_updated', 'analysis_completed', 'error_occurred',
                    'agent_progress', 'agent_completed', 'AVAILABLE_MODELS'
                ]
                
                missing = []
                for attr in attributes:
                    if not hasattr(self.ai_instance, attr):
                        missing.append(attr)
                
                if not missing:
                    return True, "AI instance created successfully with all required attributes"
                else:
                    return False, f"AI instance missing required attributes: {', '.join(missing)}"
            else:
                return False, "AI instance is None"
        except Exception as e:
            return False, f"Error creating AI instance: {str(e)}"
    
    def _test_api_key_config(self) -> Tuple[bool, str]:
        """Test if the API key is configured"""
        try:
            if self.ai_instance is None:
                return False, "AI instance is not initialized"
            
            api_key = self.ai_instance.api_key
            
            if api_key:
                # Mask most of the key for privacy
                masked_key = "********" + api_key[-4:] if len(api_key) > 4 else "********"
                return True, f"API key is configured ({masked_key}, length: {len(api_key)})"
            else:
                return False, "No API key is configured"
        except Exception as e:
            return False, f"Error checking API key configuration: {str(e)}"
    
    def _test_client_setup(self) -> Tuple[bool, str]:
        """Test if OpenAI client is set up correctly"""
        try:
            if self.ai_instance is None:
                return False, "AI instance is not initialized"
            
            # Try to set up the client
            self.ai_instance._setup_client()
            
            if self.ai_instance.client is not None:
                return True, "OpenAI client setup successful"
            else:
                return False, "OpenAI client is None after setup"
        except Exception as e:
            return False, f"Error setting up OpenAI client: {str(e)}"
    
    def _test_available_models(self) -> Tuple[bool, str]:
        """Test if available models are defined"""
        try:
            if self.ai_instance is None:
                return False, "AI instance is not initialized"
            
            models = self.ai_instance.AVAILABLE_MODELS
            
            if models and len(models) > 0:
                model_names = list(models.keys())
                return True, f"Available models: {', '.join(model_names)}"
            else:
                return False, "No available models defined"
        except Exception as e:
            return False, f"Error checking available models: {str(e)}"
    
    def _test_authentication(self) -> Tuple[bool, str]:
        """Test if authentication with the API works"""
        try:
            if self.ai_instance is None:
                return False, "AI instance is not initialized"
            
            auth_result = self.ai_instance.check_api_key()
            
            if auth_result:
                return True, "API authentication successful"
            else:
                return False, "API authentication failed"
        except Exception as e:
            return False, f"Error during API authentication: {str(e)}"
    
    def _test_content_analysis(self) -> Tuple[bool, str]:
        """Test basic content analysis functionality"""
        try:
            if self.ai_instance is None:
                return False, "AI instance is not initialized"
            
            # Create simple test content
            test_content = """
            SuperCrawler is a web crawling and AI analysis tool.
            It can extract information from websites and analyze it using DeepSeek AI.
            This test is checking if the AI integration is working correctly.
            """
            
            # Try to analyze the content with a quick summary
            analysis_result = None
            
            def on_completion(result):
                nonlocal analysis_result
                analysis_result = result
            
            def on_error(error_msg):
                nonlocal analysis_result
                analysis_result = {"error": error_msg}
            
            # Connect signals
            self.ai_instance.analysis_completed.connect(on_completion)
            self.ai_instance.error_occurred.connect(on_error)
            
            # Analyze content with a short timeout
            self.ai_instance.analyze_content(
                content=test_content,
                model="deepseek-chat",  # Use a simpler model
                analysis_type="summary",
                temperature=0.1  # Lower temperature for more deterministic results
            )
            
            # Wait for result with timeout
            timeout = 30  # 30 seconds max
            start_time = time.time()
            
            while analysis_result is None and (time.time() - start_time) < timeout:
                time.sleep(0.5)
            
            # Disconnect signals
            self.ai_instance.analysis_completed.disconnect(on_completion)
            self.ai_instance.error_occurred.disconnect(on_error)
            
            if analysis_result is None:
                return False, f"Content analysis timed out after {timeout} seconds"
            elif "error" in analysis_result:
                return False, f"Content analysis failed: {analysis_result['error']}"
            else:
                return True, "Content analysis completed successfully"
        except Exception as e:
            return False, f"Error during content analysis: {str(e)}"
    
    def format_results(self) -> str:
        """Format test results as readable output"""
        if not self.results:
            return "No test results available."
        
        output = []
        output.append("=" * 60)
        output.append("SUPERCRAWLER AI INTEGRATION SELF-TEST RESULTS")
        output.append("=" * 60)
        output.append("")
        
        for test_name, result in self.results.items():
            status = f"{PASS_SYMBOL} PASS" if result["success"] else f"{FAIL_SYMBOL} FAIL"
            elapsed = result.get("elapsed", 0)
            message = result.get("message", "No message")
            
            output.append(f"{status}: {test_name} ({elapsed:.2f}s)")
            output.append(f"  {message}")
            output.append("")
        
        output.append("-" * 60)
        output.append(f"SUMMARY: {self.passed_count}/{self.test_count} tests passed")
        
        # Add recommendations
        output.append("\nRECOMMENDATIONS:")
        
        if not self.results.get("API Key Configuration", {}).get("success", False):
            output.append("- Configure a valid DeepSeek API key in the application settings")
        
        if not self.results.get("API Authentication", {}).get("success", False) and self.results.get("API Key Configuration", {}).get("success", False):
            output.append("- The API key may be invalid or expired - check for typos")
            output.append("- Verify your internet connection and firewall settings")
        
        if not self.results.get("Simple Content Analysis", {}).get("success", False) and self.results.get("API Authentication", {}).get("success", False):
            output.append("- The API may be experiencing issues - try again later")
            output.append("- Check if your API usage quota has been exceeded")
        
        output.append("")
        output.append("=" * 60)
        
        return "\n".join(output)


def run_ai_self_test():
    """Run the AI integration self-test"""
    print("SuperCrawler AI Integration Self-Test")
    print("====================================")
    
    # Set up logger if not already set up
    setup_logger()
    
    # Create and run the self-test
    tester = AISelfTest(verbose=True)
    tester.run_all_tests()
    
    # Print formatted results
    print("\n" + tester.format_results())


if __name__ == "__main__":
    run_ai_self_test() 