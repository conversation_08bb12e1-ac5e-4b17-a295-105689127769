#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IntelliCrawler Debug Application
A simple version of the application for testing
"""

import sys
import os
import logging
import traceback
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QVBoxLayout, 
                           QHBoxLayout, QWidget, QLabel, QPushButton, QStatusBar, 
                           QTextEdit, QAction, QMessageBox)
from PyQt5.QtCore import Qt

# Set up basic logging
log_dir = os.path.expanduser("~/.intellicrawler/logs")
os.makedirs(log_dir, exist_ok=True)
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join(log_dir, "intellicrawler_debug.log"))
    ]
)
logger = logging.getLogger("IntelliCrawler_Debug")

# Set up error logging
error_dir = os.path.expanduser("~/.intellicrawler/logs/errors")
os.makedirs(error_dir, exist_ok=True)
error_handler = logging.FileHandler(
    os.path.join(error_dir, "intellicrawler_debug_errors.log")
)
error_handler.setLevel(logging.ERROR)
error_formatter = logging.Formatter(
    '%(asctime)s - ERROR - %(filename)s:%(lineno)d - %(message)s\n%(exc_info)s\n'
)
error_handler.setFormatter(error_formatter)
logger.addHandler(error_handler)

# Track uncaught exceptions
def handle_exception(exc_type, exc_value, exc_traceback):
    """Handle uncaught exceptions"""
    if issubclass(exc_type, KeyboardInterrupt):
        # Let KeyboardInterrupt pass through
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
        
    # Log the error with timestamp and traceback
    logger.error("Uncaught exception:", 
                exc_info=(exc_type, exc_value, exc_traceback))
    
    # Also print to stderr
    print("An uncaught exception occurred:", file=sys.stderr)
    traceback.print_exception(exc_type, exc_value, exc_traceback, file=sys.stderr)

# Set the exception handler
sys.excepthook = handle_exception

# Add a test error generation function
def generate_test_error():
    """Generate a test error to demonstrate error logging"""
    try:
        # Deliberately cause an error
        1 / 0
    except Exception as e:
        error_id = f"ERR-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        logger.error(f"[{error_id}] Test error generated: {str(e)}", 
                    exc_info=True)
        return error_id
    
    return None

class CrawlerTab(QWidget):
    """Placeholder for the crawler tab"""
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        
        # Add placeholder content
        label = QLabel("Crawler Tab - This is a test version")
        layout.addWidget(label)
        
        # Add a text area
        self.text_area = QTextEdit()
        self.text_area.setPlaceholderText("URL to crawl...")
        layout.addWidget(self.text_area)
        
        # Add buttons
        button_layout = QHBoxLayout()
        
        self.crawl_button = QPushButton("Start Crawling")
        self.crawl_button.clicked.connect(self.on_crawl_click)
        button_layout.addWidget(self.crawl_button)
        
        # Add a button to test error handling
        self.error_button = QPushButton("Test Error Handling")
        self.error_button.clicked.connect(self.test_error_handling)
        button_layout.addWidget(self.error_button)
        
        layout.addLayout(button_layout)
        
        # Set layout
        self.setLayout(layout)
    
    def on_crawl_click(self):
        """Handle crawl button click"""
        url = self.text_area.toPlainText().strip()
        if url:
            logger.info(f"Starting crawl for URL: {url}")
            QMessageBox.information(self, "Debug Info", f"Would crawl URL: {url}\nThis is just a debug version.")
        else:
            logger.warning("No URL provided for crawling")
            QMessageBox.warning(self, "Warning", "Please enter a URL to crawl.")
    
    def test_error_handling(self):
        """Test error handling functionality"""
        try:
            # Generate a deliberate error
            error_id = generate_test_error()
            QMessageBox.information(self, "Error Test", 
                                  f"Test error generated and logged with ID: {error_id}\n"
                                  f"Check the error log at:\n{error_dir}/supercrawler_debug_errors.log")
        except Exception as e:
            logger.error(f"Error in test_error_handling: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Error", f"An unexpected error occurred: {str(e)}")
    
    def reset(self):
        """Reset the tab state"""
        self.text_area.clear()

class AnalysisTab(QWidget):
    """Placeholder for the analysis tab"""
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        
        # Add placeholder content
        label = QLabel("Analysis Tab - This is a test version")
        layout.addWidget(label)
        
        # Add a text area for displaying analysis results
        self.results_area = QTextEdit()
        self.results_area.setPlaceholderText("Analysis results will appear here...")
        self.results_area.setReadOnly(True)
        layout.addWidget(self.results_area)
        
        # Add a button for testing
        self.analyze_button = QPushButton("Run Test Analysis")
        self.analyze_button.clicked.connect(self.run_test_analysis)
        layout.addWidget(self.analyze_button)
        
        # Set layout
        self.setLayout(layout)
    
    def run_test_analysis(self):
        """Run a test analysis"""
        logger.info("Running test analysis")
        self.results_area.setPlainText("This is a test analysis result.\n\n"
                                       "In the full version, this would use the DeepSeek API to analyze crawled content.\n\n"
                                       "Timestamp: " + str(import_datetime().now()))
    
    def reset(self):
        """Reset the tab state"""
        self.results_area.clear()

class SettingsTab(QWidget):
    """Placeholder for the settings tab"""
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        
        # Add placeholder content
        label = QLabel("Settings Tab - This is a test version")
        layout.addWidget(label)
        
        # Add a field for API key
        api_key_layout = QHBoxLayout()
        api_key_label = QLabel("DeepSeek API Key:")
        self.api_key_field = QTextEdit()
        self.api_key_field.setPlaceholderText("Enter your DeepSeek API key here")
        self.api_key_field.setMaximumHeight(30)
        api_key_layout.addWidget(api_key_label)
        api_key_layout.addWidget(self.api_key_field)
        
        layout.addLayout(api_key_layout)
        
        # Add save button
        self.save_button = QPushButton("Save Settings")
        self.save_button.clicked.connect(self.save_settings)
        layout.addWidget(self.save_button)
        
        # Set layout
        self.setLayout(layout)
    
    def save_settings(self):
        """Save settings"""
        api_key = self.api_key_field.toPlainText().strip()
        logger.info(f"Saving API key: {api_key[:5]}..." if api_key else "No API key provided")
        QMessageBox.information(self, "Settings Saved", "Settings have been saved.\nThis is just a debug version.")

class ErrorReportTab(QWidget):
    """Simple error reporting tab for debug version"""
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        
        # Add heading
        label = QLabel("Error Reports - Debug Version")
        label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(label)
        
        # Add a text area for displaying errors
        self.error_text = QTextEdit()
        self.error_text.setReadOnly(True)
        layout.addWidget(self.error_text)
        
        # Add controls
        button_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("Refresh Error Log")
        self.refresh_button.clicked.connect(self.refresh_errors)
        button_layout.addWidget(self.refresh_button)
        
        self.generate_button = QPushButton("Generate Test Error")
        self.generate_button.clicked.connect(self.generate_test_error)
        button_layout.addWidget(self.generate_button)
        
        self.clear_button = QPushButton("Clear Display")
        self.clear_button.clicked.connect(self.clear_display)
        button_layout.addWidget(self.clear_button)
        
        layout.addLayout(button_layout)
        
        # Set layout
        self.setLayout(layout)
        
        # Initial refresh
        self.refresh_errors()
    
    def refresh_errors(self):
        """Refresh error log display"""
        try:
            error_log_path = os.path.join(error_dir, "supercrawler_debug_errors.log")
            if os.path.exists(error_log_path):
                with open(error_log_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.error_text.setPlainText(content)
            else:
                self.error_text.setPlainText("No error log file found.")
        except Exception as e:
            self.error_text.setPlainText(f"Error reading log file: {str(e)}")
    
    def generate_test_error(self):
        """Generate a test error for demonstration"""
        try:
            error_id = generate_test_error()
            self.refresh_errors()
            QMessageBox.information(self, "Test Error", 
                                  f"Generated test error with ID: {error_id}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to generate test error: {str(e)}")
    
    def clear_display(self):
        """Clear the error display"""
        self.error_text.clear()

class MainWindow(QMainWindow):
    """Main application window with tab-based interface"""
    
    def __init__(self):
        super().__init__()
        
        # Set window properties
        self.setWindowTitle("SuperCrawler - Debug Version")
        self.setMinimumSize(800, 600)
        
        # Setup central widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        
        # Create tab widget
        self.tabs = QTabWidget()
        
        # Create individual tabs
        self.crawler_tab = CrawlerTab(self)
        self.analysis_tab = AnalysisTab(self)
        self.settings_tab = SettingsTab(self)
        self.error_report_tab = ErrorReportTab(self)
        
        # Add tabs to tab widget
        self.tabs.addTab(self.crawler_tab, "Web Crawler")
        self.tabs.addTab(self.analysis_tab, "AI Analysis")
        self.tabs.addTab(self.settings_tab, "Settings")
        self.tabs.addTab(self.error_report_tab, "Error Reports")
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - DEBUG VERSION")
        
        # Add tab widget to main layout
        self.main_layout.addWidget(self.tabs)
        
        # Setup menu bar
        self.setup_menu()
        
        logger.info("MainWindow initialized")
    
    def setup_menu(self):
        """Create application menu"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        # Exit
        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        # About
        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About SuperCrawler",
                         "SuperCrawler v1.0 (DEBUG VERSION)\n\n"
                         "A powerful web scraping and AI analysis tool powered by DeepSeek AI.\n\n"
                         "© 2023 SuperCrawler")
    
    def closeEvent(self, event):
        """Handle window close event"""
        reply = QMessageBox.question(self, "Exit Confirmation",
                                    "Are you sure you want to exit?",
                                    QMessageBox.Yes | QMessageBox.No,
                                    QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            logger.info("Application closing")
            event.accept()
        else:
            event.ignore()

def import_datetime():
    """Helper function to import datetime"""
    from datetime import datetime
    return datetime

def main():
    """Main entry point for the debug application"""
    try:
        logger.info("Starting SuperCrawler Debug Application")
        
        # Log system information
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Operating system: {sys.platform}")
        logger.info(f"Debug log location: {log_dir}/supercrawler_debug.log")
        logger.info(f"Error log location: {error_dir}/supercrawler_debug_errors.log")
        
        # Create Qt application
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app = QApplication(sys.argv)
        app.setApplicationName("SuperCrawler Debug")
        
        # Create and show main window
        window = MainWindow()
        window.show()
        
        # Start the event loop
        sys.exit(app.exec_())
    except Exception as e:
        logger.critical(f"Fatal error in main: {str(e)}", exc_info=True)
        print(f"FATAL ERROR: {str(e)}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main() 