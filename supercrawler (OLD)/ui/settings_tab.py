from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QLineEdit,
                            QGroupBox, QFormLayout, QCheckBox,
                            QMessageBox, QComboBox, QFrame, QScrollArea)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import Q<PERSON>ont, QPalette

from intellicrawler.utils.config import load_config, save_config, create_env_template
from intellicrawler.utils.logger import get_logger
from intellicrawler.ai_integration import DeepSeekAI
import os

class SettingsTab(QWidget):
    """Tab for application settings with modern, enhanced UI"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self.config = load_config()
        self.ai_integration = DeepSeekAI()
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        # Create scroll area for the settings
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setFrameShape(QFrame.NoFrame)
        scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #f8f9fa;
            }
        """)
        
        # Main content widget
        content_widget = QWidget()
        main_layout = QVBoxLayout(content_widget)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(25)
        
        # Header section with modern styling
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                margin-bottom: 10px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(25, 20, 25, 20)
        
        # Title with emoji
        title = QLabel("🕷️ SuperCrawler Settings")
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setStyleSheet("color: white; margin: 0px;")
        title.setAlignment(Qt.AlignCenter)
        
        subtitle = QLabel("Configure your web scraping and AI analysis preferences")
        subtitle_font = QFont()
        subtitle_font.setPointSize(12)
        subtitle.setFont(subtitle_font)
        subtitle.setStyleSheet("color: rgba(255, 255, 255, 0.9); margin-top: 5px;")
        subtitle.setAlignment(Qt.AlignCenter)
        
        header_layout.addWidget(title)
        header_layout.addWidget(subtitle)
        main_layout.addWidget(header_frame)
        
        # AI Configuration Section
        ai_section = self._create_modern_section(
            "🤖 AI Configuration", 
            "Configure your DeepSeek AI integration for advanced analysis"
        )
        
        # API Key input with modern styling
        api_key_container = QFrame()
        api_key_container.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 15px;
            }
            QFrame:hover {
                border-color: #667eea;
            }
        """)
        api_key_layout = QVBoxLayout(api_key_container)
        
        api_key_label = QLabel("🔑 DeepSeek API Key")
        api_key_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                margin-bottom: 8px;
            }
        """)
        
        # API key source info label
        self.api_key_source_label = QLabel("")
        self.api_key_source_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 11px;
                font-style: italic;
                margin-bottom: 5px;
            }
        """)
        
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.Password)
        self.api_key_input.setPlaceholderText("Enter your DeepSeek API key (sk-...)")
        self.api_key_input.setStyleSheet("""
            QLineEdit {
                padding: 12px 15px;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-size: 13px;
                background-color: #f8f9fa;
                selection-background-color: #667eea;
            }
            QLineEdit:focus {
                border-color: #667eea;
                background-color: white;
                outline: none;
            }
        """)
        
        # Toggle visibility button
        self.show_api_key_button = QPushButton("👁️ Show")
        self.show_api_key_button.clicked.connect(self.toggle_api_key_visibility)
        self.show_api_key_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 6px;
                font-weight: bold;
                margin-top: 8px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        
        api_key_layout.addWidget(api_key_label)
        api_key_layout.addWidget(self.api_key_source_label)
        api_key_layout.addWidget(self.api_key_input)
        api_key_layout.addWidget(self.show_api_key_button)
        
        # Get the layout from the section and add the container
        ai_section.layout().addWidget(api_key_container)
        main_layout.addWidget(ai_section)
        
        # Crawler Settings Section
        crawler_section = self._create_modern_section(
            "🕸️ Crawler Settings", 
            "Configure web scraping behavior and preferences"
        )
        
        # User Agent Selection
        user_agent_container = QFrame()
        user_agent_container.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 15px;
            }
            QFrame:hover {
                border-color: #28a745;
            }
        """)
        user_agent_layout = QVBoxLayout(user_agent_container)
        
        user_agent_label = QLabel("🌐 User Agent")
        user_agent_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                margin-bottom: 8px;
            }
        """)
        
        self.user_agent_combo = QComboBox()
        self.user_agent_combo.setEditable(True)
        self.user_agent_combo.addItems(self._get_browser_user_agents())
        self.user_agent_combo.currentTextChanged.connect(self._on_user_agent_changed)
        self.user_agent_combo.setStyleSheet("""
            QComboBox {
                padding: 12px 15px;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-size: 13px;
                background-color: #f8f9fa;
                selection-background-color: #28a745;
            }
            QComboBox:focus {
                border-color: #28a745;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 6px solid #6c757d;
                margin-right: 10px;
            }
        """)
        
        user_agent_layout.addWidget(user_agent_label)
        user_agent_layout.addWidget(self.user_agent_combo)
        
        # Robots.txt checkbox
        robots_container = QFrame()
        robots_container.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 15px;
            }
            QFrame:hover {
                border-color: #28a745;
            }
        """)
        robots_layout = QHBoxLayout(robots_container)
        
        self.respect_robots_checkbox = QCheckBox("🤖 Respect robots.txt")
        self.respect_robots_checkbox.setChecked(True)
        self.respect_robots_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 4px;
                border: 2px solid #dee2e6;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #28a745;
                border-color: #28a745;
                image: none;
            }
            QCheckBox::indicator:checked:after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
        """)
        
        robots_info = QLabel("Follows website crawling guidelines")
        robots_info.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 12px;
                font-style: italic;
            }
        """)
        
        robots_layout.addWidget(self.respect_robots_checkbox)
        robots_layout.addStretch()
        robots_layout.addWidget(robots_info)
        
        # Add containers to the crawler section layout
        crawler_section.layout().addWidget(user_agent_container)
        crawler_section.layout().addWidget(robots_container)
        main_layout.addWidget(crawler_section)
        
        # Action buttons
        button_container = QFrame()
        button_container.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 15px;
                padding: 20px;
            }
        """)
        button_layout = QHBoxLayout(button_container)
        
        # Create .env template button
        self.create_env_button = QPushButton("📄 Create .env Template")
        self.create_env_button.clicked.connect(self.create_env_file)
        self.create_env_button.setStyleSheet(self._get_button_style("#17a2b8"))
        
        # Save button
        self.save_button = QPushButton("💾 Save Settings")
        self.save_button.clicked.connect(self.save_settings)
        self.save_button.setStyleSheet(self._get_button_style("#28a745"))
        
        button_layout.addWidget(self.create_env_button)
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        
        main_layout.addWidget(button_container)
        
        # Add some spacing at the bottom
        main_layout.addStretch()
        
        # Set up scroll area
        scroll.setWidget(content_widget)
        
        # Main layout for the entire tab
        tab_layout = QVBoxLayout(self)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll)
        
        # Load existing settings automatically
        self.load_settings()
    
    def _create_modern_section(self, title, description):
        """Create a modern-looking section with title and description"""
        section_frame = QGroupBox()
        section_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 2px solid #dee2e6;
                border-radius: 15px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 8px;
            }
        """)
        section_frame.setTitle(title)
        
        layout = QVBoxLayout(section_frame)
        layout.setContentsMargins(20, 25, 20, 20)
        layout.setSpacing(15)
        
        # Add description
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 13px;
                font-weight: normal;
                font-style: italic;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(desc_label)
        
        return section_frame
    
    def _get_button_style(self, color):
        """Get consistent button styling"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background-color: {self._darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self._darken_color(color, 0.2)};
            }}
        """
    
    def _darken_color(self, hex_color, factor=0.1):
        """Darken a hex color by a given factor"""
        # Simple darkening - remove # and reduce each component
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(max(0, int(c * (1 - factor))) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"
    
    def _get_browser_user_agents(self):
        """Get a comprehensive list of real browser user agents"""
        return [
            # IntelliCrawler defaults
            "IntelliCrawler/2.0 (AI-powered web analysis)",
            "IntelliCrawler/1.0 (educational purposes only)",
            
            # Chrome - Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            
            # Chrome - macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            
            # Chrome - Linux
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            
            # Firefox - Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0",
            
            # Firefox - macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0",
            
            # Firefox - Linux
            "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0",
            
            # Safari - macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15",
            
            # Safari - iOS
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (iPad; CPU OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
            
            # Edge - Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            
            # Edge - macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            
            # Opera - Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/*********",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********",
            
            # Opera - macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/*********",
            
            # Mobile Chrome - Android
            "Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
            
            # Mobile Firefox - Android
            "Mozilla/5.0 (Mobile; rv:121.0) Gecko/121.0 Firefox/121.0",
            "Mozilla/5.0 (Android 14; Mobile; rv:121.0) Gecko/121.0 Firefox/121.0",
            
            # Specialized crawlers and bots (legitimate ones)
            "Googlebot/2.1 (+http://www.google.com/bot.html)",
            "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)",
            
            # Older but still common browsers
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0",
        ]
    
    def _on_user_agent_changed(self, user_agent):
        """Real-time update when user agent is changed"""
        try:
            if not user_agent.strip():
                return
                
            self.logger.info(f"User agent changed to: {user_agent[:50]}...")
            
            # Update the config immediately
            self.config["user_agent"] = user_agent
            
            # Try to update the crawler instance if available
            try:
                # Update parent window's crawler if available
                parent = self.parent()
                if parent and hasattr(parent, 'crawler_tab'):
                    crawler_tab = parent.crawler_tab
                    if hasattr(crawler_tab, 'crawler') and crawler_tab.crawler:
                        # Update the session headers
                        crawler_tab.crawler.session.headers.update({
                            'User-Agent': user_agent
                        })
                        self.logger.info("Updated active crawler user agent in real-time")
                        
            except Exception as e:
                self.logger.warning(f"Could not update active crawler user agent: {str(e)}")
                
            # Auto-save the config for persistence
            try:
                save_config(self.config)
                self.logger.info("User agent preference saved automatically")
            except Exception as e:
                self.logger.warning(f"Could not auto-save user agent preference: {str(e)}")
                
        except Exception as e:
            self.logger.error(f"Error in user agent change handler: {str(e)}")
        
    def load_settings(self):
        """Load settings from config file and detect API key source"""
        try:
            # Load API key and detect its source
            api_key = self.config.get("deepseek_api_key", "")
            api_key_source = self._detect_api_key_source()
            
            self.api_key_input.setText(api_key)
            
            # Update source label
            if api_key_source == "env":
                self.api_key_source_label.setText("📄 API key loaded from .env file (takes priority)")
                self.api_key_source_label.setStyleSheet("""
                    QLabel {
                        color: #28a745;
                        font-size: 11px;
                        font-style: italic;
                        margin-bottom: 5px;
                        font-weight: bold;
                    }
                """)
            elif api_key_source == "config":
                self.api_key_source_label.setText("⚙️ API key loaded from config.json")
                self.api_key_source_label.setStyleSheet("""
                    QLabel {
                        color: #17a2b8;
                        font-size: 11px;
                        font-style: italic;
                        margin-bottom: 5px;
                    }
                """)
            else:
                self.api_key_source_label.setText("❌ No API key found")
                self.api_key_source_label.setStyleSheet("""
                    QLabel {
                        color: #dc3545;
                        font-size: 11px;
                        font-style: italic;
                        margin-bottom: 5px;
                    }
                """)
            
            # Load user agent
            user_agent = self.config.get("user_agent", "SuperCrawler/1.0 (educational purposes only)")
            
            # Set the user agent in the combo box
            index = self.user_agent_combo.findText(user_agent)
            if index >= 0:
                self.user_agent_combo.setCurrentIndex(index)
            else:
                # If not found, add it as a custom option and select it
                self.user_agent_combo.addItem(user_agent)
                self.user_agent_combo.setCurrentText(user_agent)
            
            # Load respect robots.txt setting
            respect_robots = self.config.get("respect_robots", True)
            self.respect_robots_checkbox.setChecked(respect_robots)
            
            # Automatically apply settings to active components
            self._apply_settings_to_components()
            
            self.logger.info("Settings loaded and applied successfully")
        except Exception as e:
            self.logger.error(f"Error loading settings: {str(e)}")
    
    def _detect_api_key_source(self):
        """Detect where the API key is coming from"""
        try:
            import os
            
            # Check if .env files exist and contain API key
            env_files = [".env", os.path.join(os.path.expanduser("~/.intellicrawler"), ".env")]
            
            for env_file in env_files:
                if os.path.exists(env_file):
                    try:
                        with open(env_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if 'DEEPSEEK_API_KEY=' in content and content.split('DEEPSEEK_API_KEY=')[1].split('\n')[0].strip():
                                return "env"
                    except:
                        continue
            
            # Check if config.json has API key
            config_file = os.path.join(os.path.expanduser("~/.intellicrawler"), "config.json")
            if os.path.exists(config_file):
                try:
                    import json
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                        if config_data.get("deepseek_api_key"):
                            return "config"
                except:
                    pass
            
            return "none"
            
        except Exception as e:
            self.logger.error(f"Error detecting API key source: {str(e)}")
            return "unknown"
    
    def _apply_settings_to_components(self):
        """Apply current settings to active components without saving"""
        try:
            # Get current values
            api_key = self.api_key_input.text().strip()
            user_agent = self.user_agent_combo.currentText().strip()
            respect_robots = self.respect_robots_checkbox.isChecked()
            
            # Update AI integration instance
            if hasattr(self, 'ai_integration') and api_key:
                self.ai_integration.set_api_key(api_key)
                self.logger.info("Applied API key to local AI integration")
            
            # Try to update parent window components
            try:
                parent = self.parent()
                if parent:
                    # Update main window AI integration
                    if hasattr(parent, 'ai_integration') and api_key:
                        parent.ai_integration.set_api_key(api_key)
                        self.logger.info("Applied API key to main window AI integration")
                    
                    # Update all tabs that might have AI integration
                    for tab_name in ['crawler_tab', 'analysis_tab', 'proxy_scraper_tab']:
                        if hasattr(parent, tab_name):
                            tab = getattr(parent, tab_name)
                            if hasattr(tab, 'ai_integration') and api_key:
                                tab.ai_integration.set_api_key(api_key)
                                self.logger.info(f"Applied API key to {tab_name}")
                            
                            # Apply crawler-specific settings
                            if tab_name == 'crawler_tab' and hasattr(tab, 'apply_settings'):
                                tab.apply_settings(user_agent, respect_robots)
                                self.logger.info(f"Applied crawler settings to {tab_name}")
                    
                    # Update status if main window has status bar
                    if hasattr(parent, 'status_bar'):
                        parent.status_bar.showMessage("Settings loaded and applied", 2000)
                        
            except Exception as component_error:
                self.logger.warning(f"Could not apply settings to some components: {str(component_error)}")
                
        except Exception as e:
            self.logger.error(f"Error applying settings to components: {str(e)}")
        
    def save_settings(self):
        """Save settings to config file and .env file if it exists"""
        try:
            # Get values from UI
            api_key = self.api_key_input.text().strip()
            user_agent = self.user_agent_combo.currentText().strip()
            respect_robots = self.respect_robots_checkbox.isChecked()
            
            # Log the API key length for debugging
            self.logger.info(f"Saving API key (length: {len(api_key) if api_key else 0})")
            
            # Update config
            self.config["deepseek_api_key"] = api_key
            self.config["user_agent"] = user_agent
            self.config["respect_robots"] = respect_robots
            
            # Ensure config directory exists
            import os
            config_dir = os.path.expanduser("~/.intellicrawler")
            os.makedirs(config_dir, exist_ok=True)
            self.logger.info(f"Config directory: {config_dir}")
            
            # Check for .env file and update it if it exists
            env_file_updated = False
            env_files_to_check = [".env", os.path.join(config_dir, ".env")]
            
            for env_file_path in env_files_to_check:
                if os.path.exists(env_file_path):
                    try:
                        self.logger.info(f"Found .env file at {env_file_path}, updating it...")
                        self._update_env_file(env_file_path, api_key, user_agent)
                        env_file_updated = True
                        self.logger.info(f"Successfully updated .env file: {env_file_path}")
                    except Exception as env_err:
                        self.logger.error(f"Failed to update .env file {env_file_path}: {str(env_err)}")
            
            # Save config (this is still important for other settings)
            config_saved = save_config(self.config)
            
            if config_saved or env_file_updated:
                self.logger.info("Settings saved successfully")
                
                # Update AI integration instance first
                try:
                    self.ai_integration.set_api_key(api_key)
                    self.logger.info("Updated local AI integration instance with new API key")
                except Exception as ai_err:
                    self.logger.error(f"Error updating local AI integration: {str(ai_err)}")
                
                # Try to update parent window components
                try:
                    parent = self.parent()
                    if parent:
                        # Update main window AI integration
                        if hasattr(parent, 'ai_integration'):
                            parent.ai_integration.set_api_key(api_key)
                            self.logger.info("Updated main window AI integration instance")
                        
                        # Update all tabs that might have AI integration
                        for tab_name in ['crawler_tab', 'analysis_tab']:
                            if hasattr(parent, tab_name):
                                tab = getattr(parent, tab_name)
                                if hasattr(tab, 'ai_integration'):
                                    tab.ai_integration.set_api_key(api_key)
                                    self.logger.info(f"Updated {tab_name} AI integration instance")
                                if hasattr(tab, 'refresh_settings'):
                                    tab.refresh_settings()
                                    self.logger.info(f"Refreshed {tab_name} settings")
                        
                        # Call parent refresh method if it exists
                        if hasattr(parent, 'refresh_settings'):
                            parent.refresh_settings()
                            self.logger.info("Refreshed parent window settings")
                            
                except Exception as parent_err:
                    self.logger.warning(f"Could not update parent components: {str(parent_err)}")
                
                # Show success message with details about what was updated
                success_message = "Your settings have been saved successfully!\n\n"
                
                if env_file_updated:
                    success_message += "✅ .env file updated with new API key\n"
                if config_saved:
                    success_message += "✅ Config file updated\n"
                success_message += "✅ Crawler settings applied\n"
                success_message += "✅ Settings refreshed throughout application"
                
                QMessageBox.information(self, "✅ Settings Saved", success_message)
                
                self.logger.info("Settings refreshed throughout application")
            else:
                self.logger.error("Failed to save settings to any file")
                QMessageBox.warning(self, "❌ Save Error", "There was an error saving your settings. Check the logs for details.")
        except Exception as e:
            self.logger.error(f"Error in save_settings: {str(e)}")
            QMessageBox.critical(self, "❌ Save Error", f"Error saving settings: {str(e)}")
    
    def _update_env_file(self, env_file_path, api_key, user_agent):
        """Update .env file with new values"""
        try:
            # Read current .env file
            with open(env_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Update the lines
            updated_lines = []
            api_key_updated = False
            user_agent_updated = False
            
            for line in lines:
                if line.strip().startswith('DEEPSEEK_API_KEY='):
                    updated_lines.append(f"DEEPSEEK_API_KEY={api_key}\n")
                    api_key_updated = True
                elif line.strip().startswith('USER_AGENT='):
                    updated_lines.append(f"USER_AGENT={user_agent}\n")
                    user_agent_updated = True
                else:
                    updated_lines.append(line)
            
            # Add lines if they weren't found
            if not api_key_updated:
                updated_lines.append(f"DEEPSEEK_API_KEY={api_key}\n")
            if not user_agent_updated:
                updated_lines.append(f"USER_AGENT={user_agent}\n")
            
            # Write back to file
            with open(env_file_path, 'w', encoding='utf-8') as f:
                f.writelines(updated_lines)
                
            self.logger.info(f"Successfully updated .env file: {env_file_path}")
            
        except Exception as e:
            self.logger.error(f"Error updating .env file {env_file_path}: {str(e)}")
            raise
    
    def toggle_api_key_visibility(self):
        """Toggle API key visibility"""
        if self.api_key_input.echoMode() == QLineEdit.Password:
            self.api_key_input.setEchoMode(QLineEdit.Normal)
            self.show_api_key_button.setText("🙈 Hide")
        else:
            self.api_key_input.setEchoMode(QLineEdit.Password)
            self.show_api_key_button.setText("👁️ Show")
    
    def create_env_file(self):
        """Create .env template file"""
        try:
            if create_env_template():
                config_dir = os.path.expanduser("~/.intellicrawler")
                project_dir = os.getcwd()
                
                message = "✅ .env template files created successfully!\n\n"
                message += f"📁 Config directory: {config_dir}/.env.template\n"
                
                if os.path.exists("intellicrawler"):  # We're in project root
                    message += f"📁 Project root: {project_dir}/.env.template\n"
                
                message += "\n📝 Instructions:\n"
                message += "1. Copy .env.template to .env\n"
                message += "2. Edit .env and add your actual API key\n"
                message += "3. Never commit .env to version control!\n"
                message += "\n🔒 .env files take priority over config.json settings."
                
                QMessageBox.information(self, "📄 .env Template Created", message)
                self.logger.info("Created .env template files")
            else:
                QMessageBox.warning(self, "❌ Error", "Failed to create .env template files. Check the logs for details.")
        except Exception as e:
            self.logger.error(f"Error creating .env template: {str(e)}")
            QMessageBox.critical(self, "❌ Error", f"Error creating .env template: {str(e)}")
    
    def refresh_settings(self):
        """Refresh settings from config (useful when config changes)"""
        try:
            self.config = load_config()
            self.load_settings()
            self.logger.info("Settings refreshed from config")
        except Exception as e:
            self.logger.error(f"Error refreshing settings: {str(e)}") 