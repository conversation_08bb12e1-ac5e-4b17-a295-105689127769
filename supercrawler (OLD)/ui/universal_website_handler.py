#!/usr/bin/env python3
"""
SuperCrawler Universal Website Handler
AI-Powered Universal Scraping System that adapts to any website
"""

import os
import json
import re
import time
import random
import base64
import html
import urllib.parse
import requests
import chardet
from bs4 import BeautifulSoup
from datetime import datetime
from urllib.parse import urljoin, urlparse, parse_qs, urlencode
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class UniversalWebsiteHandler:
    """
    Universal AI-Powered Website Handler
    Automatically detects pagination, content structure, and anti-scraping measures
    Works with ANY website without requiring site-specific handlers
    """
    
    def __init__(self, logger=None):
        self.logger = logger
        self.session = self._create_robust_session()
        
        # Learning database - stores patterns discovered across websites
        self.learned_patterns = {
            'pagination_patterns': {},
            'extraction_patterns': {},
            'anti_scraping_patterns': {},
            'success_rates': {},
            'encoding_patterns': {}
        }
        
        # Universal headers that work across most sites
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        
        # User agent rotation for stealth
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        # Proxy regex patterns for universal extraction
        self.proxy_patterns = [
            # Standard IP:Port patterns
            r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):(?:6553[0-5]|655[0-2][0-9]|65[0-4][0-9]{2}|6[0-4][0-9]{3}|[1-5][0-9]{4}|[1-9][0-9]{1,3}|[0-9])\b',
            # With protocol prefixes
            r'(?:https?://|socks[45]://)?(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):(?:6553[0-5]|655[0-2][0-9]|65[0-4][0-9]{2}|6[0-4][0-9]{3}|[1-5][0-9]{4}|[1-9][0-9]{1,3}|[0-9])',
            # In various formats with separators
            r'(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)[:\s,;|]+(?:6553[0-5]|655[0-2][0-9]|65[0-4][0-9]{2}|6[0-4][0-9]{3}|[1-5][0-9]{4}|[1-9][0-9]{1,3}|[0-9])',
        ]
        
        # Encoding detection patterns
        self.encoding_patterns = {
            'base64': r'[A-Za-z0-9+/]{20,}={0,2}',
            'url_encoding': r'%[0-9A-Fa-f]{2}',
            'html_entities': r'&[a-zA-Z]+;|&#\d+;',
            'unicode_escape': r'\\u[0-9A-Fa-f]{4}',
            'hexadecimal': r'\b[0-9A-Fa-f]{20,}\b',
            'rot13': r'[a-zA-Z]',
        }
        
        # Universal pagination patterns that work across many sites
        self.universal_pagination_patterns = [
            lambda url, page: f"{url.rstrip('/')}/page/{page}/",
            lambda url, page: f"{url}?page={page}",
            lambda url, page: f"{url}{'&' if '?' in url else '?'}page={page}",
            lambda url, page: f"{url.rstrip('/')}/{page}/",
            lambda url, page: f"{url}{'&' if '?' in url else '?'}p={page}",
            lambda url, page: f"{url}{'&' if '?' in url else '?'}offset={page*20}",
            lambda url, page: f"{url}{'&' if '?' in url else '?'}start={page*10}",
            lambda url, page: f"{url}{'&' if '?' in url else '?'}pagenum={page}",
            lambda url, page: f"{url}{'&' if '?' in url else '?'}pn={page}",
        ]
        
    def _create_robust_session(self):
        """Create a robust session with retry strategy and timeout"""
        session = requests.Session()
        
        # Retry strategy
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "OPTIONS"],
            backoff_factor=1
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _log(self, message):
        """Safe logging with fallback"""
        # Strip emojis and replace with ASCII equivalents for Windows compatibility
        if isinstance(message, str):
            message = message.replace('🤖', '[AI]')
            message = message.replace('✅', '[OK]')
            message = message.replace('❌', '[ERR]')
            message = message.replace('🚀', '[FAST]')
            message = message.replace('🔍', '[SEARCH]')
            message = message.replace('📊', '[STATS]')
            message = message.replace('⚡', '[QUICK]')
            message = message.replace('🎯', '[TARGET]')
            message = message.replace('🛡️', '[SHIELD]')
            # Remove any remaining non-ASCII characters
            message = ''.join(char if ord(char) < 128 else '?' for char in message)
        
        if self.logger:
            self.logger.info(message)
        else:
            # Fallback to logger import if no logger provided
            try:
                from supercrawler.utils.logger import get_logger
                fallback_logger = get_logger(__name__)
                fallback_logger.info(f"[UniversalHandler] {message}")
            except:
                print(f"[UniversalHandler] {message}")
    
    def analyze_website_intelligence(self, url, source_name):
        """
        AI-powered website analysis that learns and adapts
        Returns comprehensive analysis including pagination strategy and anti-scraping level
        """
        self._log(f"[AI] Analyzing website structure for {source_name}")
        
        try:
            # Rotate User-Agent for stealth
            self.headers['User-Agent'] = random.choice(self.user_agents)
            
            # Get initial page
            response = self.session.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()
            
            # Detect encoding
            encoding = self._detect_encoding(response.content, url)
            content = response.content.decode(encoding, errors='ignore')
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # Phase 1: Pagination Analysis
            pagination_analysis = self._analyze_pagination_patterns(soup, url)
            
            # Phase 2: Content Structure Analysis
            content_analysis = self._analyze_content_structure(soup, source_name)
            
            # Phase 3: Anti-scraping Intelligence
            anti_scraping_analysis = self._ai_detect_anti_scraping(soup, url, source_name)
            
            # Phase 4: Encoding Detection Intelligence
            encoding_analysis = self._analyze_encoding_patterns(content, source_name)
            
            # Phase 5: Success Probability Calculation
            success_probability = self._calculate_success_probability(
                pagination_analysis, content_analysis, anti_scraping_analysis
            )
            
            # Comprehensive analysis result
            analysis = {
                'pagination_strategy': pagination_analysis['strategy'],
                'pagination_confidence': pagination_analysis['confidence'],
                'content_structure': content_analysis['structure'],
                'content_confidence': content_analysis['confidence'],
                'anti_scraping_level': anti_scraping_analysis['level'],
                'anti_scraping_score': anti_scraping_analysis['score'],
                'anti_scraping_methods': anti_scraping_analysis['methods'],
                'encoding_methods': encoding_analysis['methods'],
                'encoding_complexity': encoding_analysis['complexity'],
                'success_probability': success_probability,
                'recommended_approach': self._get_recommended_approach(success_probability),
                'total_pages_estimate': pagination_analysis.get('estimated_pages', 1)
            }
            
            # Phase 6: Learn from analysis for future use
            self._learn_from_analysis(source_name, analysis)
            
            self._log(f"[TARGET] AI analysis complete for {source_name}:")
            self._log(f"   - Pagination Strategy: {analysis['pagination_strategy']}")
            self._log(f"   - Anti-scraping Level: {analysis['anti_scraping_level']}")
            self._log(f"   - Success Probability: {analysis['success_probability']:.2f}")
            
            return analysis
            
        except Exception as e:
            self._log(f"[ERR] Analysis failed for {source_name}: {str(e)}")
            return self._get_fallback_analysis()
    
    def _analyze_pagination_patterns(self, soup, url):
        """Analyze pagination patterns on the page"""
        patterns_found = []
        
        # Detection methods in order of sophistication
        detection_methods = [
            self._detect_numbered_pagination,
            self._detect_parameter_pagination,
            self._detect_next_previous_pagination,
            self._detect_infinite_scroll,
            self._detect_ajax_pagination,
            self._detect_form_based_pagination
        ]
        
        for method in detection_methods:
            try:
                result = method(soup, url)
                if result['detected']:
                    patterns_found.append(result)
            except:
                continue
        
        if patterns_found:
            # Return the most confident detection
            best_pattern = max(patterns_found, key=lambda x: x['confidence'])
            return {
                'strategy': best_pattern['strategy'],
                'confidence': best_pattern['confidence'],
                'estimated_pages': best_pattern.get('estimated_pages', 10)
            }
        
        return {
            'strategy': 'single_page',
            'confidence': 0.5,
            'estimated_pages': 1
        }
    
    def _detect_numbered_pagination(self, soup, url):
        """Detect numbered pagination (1, 2, 3, 4, 5...)"""
        # Look for pagination with numbers
        pagination_selectors = [
            'a[href*="page"]',
            'a[href*="p="]',
            '.pagination a',
            '.pager a',
            '.page-numbers a'
        ]
        
        for selector in pagination_selectors:
            links = soup.select(selector)
            if len(links) >= 3:  # At least 3 page links
                # Try to find the highest page number
                max_page = 1
                for link in links:
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    
                    # Extract page numbers from href or text
                    page_nums = re.findall(r'\d+', href + text)
                    if page_nums:
                        max_page = max(max_page, max(int(num) for num in page_nums if int(num) <= 9999))
                
                return {
                    'detected': True,
                    'strategy': 'numbered',
                    'confidence': 0.9,
                    'estimated_pages': min(max_page, 500)  # Cap at 500 for safety
                }
        
        return {'detected': False}
    
    def _detect_parameter_pagination(self, soup, url):
        """Detect parameter-based pagination (?page=1, ?page=2...)"""
        # Check if current URL has pagination parameters
        parsed_url = urlparse(url)
        params = parse_qs(parsed_url.query)
        
        pagination_params = ['page', 'p', 'offset', 'start', 'pagenum', 'pn']
        
        for param in pagination_params:
            if param in params:
                return {
                    'detected': True,
                    'strategy': 'parameter',
                    'confidence': 0.8,
                    'estimated_pages': 50  # Conservative estimate
                }
        
        # Look for links with pagination parameters
        links = soup.find_all('a', href=True)
        for link in links:
            href = link['href']
            if any(f"{param}=" in href for param in pagination_params):
                return {
                    'detected': True,
                    'strategy': 'parameter',
                    'confidence': 0.7,
                    'estimated_pages': 30
                }
        
        return {'detected': False}
    
    def _detect_next_previous_pagination(self, soup, url):
        """Detect Next/Previous button pagination"""
        next_indicators = ['next', 'continue', '>', '>>', 'more', 'forward']
        prev_indicators = ['prev', 'previous', '<', '<<', 'back']
        
        next_found = False
        prev_found = False
        
        for link in soup.find_all(['a', 'button'], href=True):
            text = link.get_text(strip=True).lower()
            classes = ' '.join(link.get('class', [])).lower()
            
            if any(indicator in text or indicator in classes for indicator in next_indicators):
                next_found = True
            if any(indicator in text or indicator in classes for indicator in prev_indicators):
                prev_found = True
        
        if next_found:
            return {
                'detected': True,
                'strategy': 'next_previous',
                'confidence': 0.8 if prev_found else 0.6,
                'estimated_pages': 25  # Conservative estimate
            }
        
        return {'detected': False}
    
    def _detect_infinite_scroll(self, soup, url):
        """Detect infinite scroll patterns"""
        # Look for JavaScript patterns that suggest infinite scroll
        scripts = soup.find_all('script')
        scroll_indicators = ['infinite', 'scroll', 'lazy', 'load more', 'pagination']
        
        for script in scripts:
            script_text = script.get_text().lower()
            if any(indicator in script_text for indicator in scroll_indicators):
                return {
                    'detected': True,
                    'strategy': 'infinite_scroll',
                    'confidence': 0.6,
                    'estimated_pages': 1  # Infinite scroll is typically one page
                }
        
        return {'detected': False}
    
    def _detect_ajax_pagination(self, soup, url):
        """Detect AJAX-based pagination"""
        # Look for AJAX pagination indicators
        ajax_indicators = ['ajax', 'xhr', 'fetch', 'load-more', 'dynamic']
        
        for element in soup.find_all(['div', 'button', 'a']):
            classes = ' '.join(element.get('class', [])).lower()
            if any(indicator in classes for indicator in ajax_indicators):
                return {
                    'detected': True,
                    'strategy': 'ajax',
                    'confidence': 0.5,
                    'estimated_pages': 20
                }
        
        return {'detected': False}
    
    def _detect_form_based_pagination(self, soup, url):
        """Detect form-based pagination (POST requests)"""
        forms = soup.find_all('form')
        
        for form in forms:
            # Look for pagination-related form elements
            inputs = form.find_all('input')
            for input_elem in inputs:
                name = input_elem.get('name', '').lower()
                if 'page' in name or 'offset' in name:
                    return {
                        'detected': True,
                        'strategy': 'form_based',
                        'confidence': 0.7,
                        'estimated_pages': 15
                    }
        
        return {'detected': False}
    
    def _analyze_content_structure(self, soup, source_name):
        """Analyze the content structure for proxy extraction"""
        structures_found = []
        
        # Check for tables
        tables = soup.find_all('table')
        if tables:
            structures_found.append({
                'structure': 'table',
                'confidence': 0.9,
                'count': len(tables)
            })
        
        # Check for lists
        lists = soup.find_all(['ul', 'ol'])
        if lists:
            structures_found.append({
                'structure': 'list',
                'confidence': 0.7,
                'count': len(lists)
            })
        
        # Check for card/div structures
        cards = soup.find_all(['div'], class_=re.compile(r'card|item|proxy|entry', re.I))
        if cards:
            structures_found.append({
                'structure': 'card',
                'confidence': 0.6,
                'count': len(cards)
            })
        
        # Check for pre-formatted text
        pre_elements = soup.find_all(['pre', 'code'])
        if pre_elements:
            structures_found.append({
                'structure': 'preformatted',
                'confidence': 0.8,
                'count': len(pre_elements)
            })
        
        if structures_found:
            best_structure = max(structures_found, key=lambda x: x['confidence'])
            return best_structure
        
        return {
            'structure': 'mixed',
            'confidence': 0.4,
            'count': 0
        }
    
    def _ai_detect_anti_scraping(self, soup, url, source_name):
        """AI-powered anti-scraping detection"""
        anti_scraping_score = 0
        methods_detected = []
        
        # CAPTCHA detection (+3 points)
        if soup.find(string=re.compile('captcha|recaptcha|hcaptcha', re.IGNORECASE)) or \
           soup.find(class_=re.compile('captcha|recaptcha', re.IGNORECASE)):
            anti_scraping_score += 3
            methods_detected.append('captcha')
        
        # JavaScript requirement detection (+2 points)
        noscript = soup.find('noscript')
        if noscript and 'javascript' in noscript.get_text().lower():
            anti_scraping_score += 2
            methods_detected.append('javascript_required')
        
        # Rate limiting indicators (+2 points)
        if soup.find(string=re.compile('rate limit|too many requests|slow down', re.IGNORECASE)):
            anti_scraping_score += 2
            methods_detected.append('rate_limiting')
        
        # Bot protection services detection (+2 points)
        scripts = soup.find_all('script')
        for script in scripts:
            script_text = script.get_text().lower()
            if any(service in script_text for service in 
                   ['cloudflare', 'incapsula', 'distil', 'perimeterx', 'datadome']):
                anti_scraping_score += 2
                methods_detected.append('bot_protection_service')
                break
        
        # Hidden content detection (+1 point)
        if soup.find(attrs={'style': re.compile('display.*none|visibility.*hidden', re.IGNORECASE)}):
            anti_scraping_score += 1
            methods_detected.append('hidden_content')
        
        # Encoded content detection (+1 point)
        page_text = str(soup)
        if any(re.search(pattern, page_text) for pattern in self.encoding_patterns.values()):
            anti_scraping_score += 1
            methods_detected.append('encoded_content')
        
        # Determine protection level
        if anti_scraping_score >= 5:
            level = 'high'
        elif anti_scraping_score >= 3:
            level = 'medium'
        elif anti_scraping_score >= 1:
            level = 'low'
        else:
            level = 'none'
        
        return {
            'level': level,
            'score': anti_scraping_score,
            'methods': methods_detected
        }
    
    def _analyze_encoding_patterns(self, content, source_name):
        """Analyze encoding patterns in the content"""
        encoding_methods = []
        complexity_score = 0
        
        # Check for various encoding methods
        for encoding_type, pattern in self.encoding_patterns.items():
            if re.search(pattern, content):
                encoding_methods.append(encoding_type)
                complexity_score += 1
        
        # Determine complexity
        if complexity_score >= 3:
            complexity = 'high'
        elif complexity_score >= 2:
            complexity = 'medium'
        elif complexity_score >= 1:
            complexity = 'low'
        else:
            complexity = 'none'
        
        return {
            'methods': encoding_methods,
            'complexity': complexity,
            'score': complexity_score
        }
    
    def _calculate_success_probability(self, pagination, content, anti_scraping):
        """Calculate success probability based on analysis"""
        base_probability = 0.8
        
        # Adjust based on pagination confidence
        base_probability *= pagination['confidence']
        
        # Adjust based on content structure confidence
        base_probability *= content['confidence']
        
        # Adjust based on anti-scraping level
        anti_scraping_adjustments = {
            'none': 1.0,
            'low': 0.9,
            'medium': 0.7,
            'high': 0.4
        }
        base_probability *= anti_scraping_adjustments.get(anti_scraping['level'], 0.5)
        
        return min(max(base_probability, 0.1), 0.95)  # Keep between 0.1 and 0.95
    
    def _get_recommended_approach(self, success_probability):
        """Get recommended scraping approach based on success probability"""
        if success_probability >= 0.8:
            return 'standard_scraping'
        elif success_probability >= 0.6:
            return 'enhanced_headers'
        elif success_probability >= 0.4:
            return 'selenium_fallback'
        else:
            return 'advanced_evasion'
    
    def _learn_from_analysis(self, source_name, analysis):
        """Learn from successful analysis for future improvement"""
        if source_name not in self.learned_patterns['pagination_patterns']:
            self.learned_patterns['pagination_patterns'][source_name] = analysis['pagination_strategy']
        
        if source_name not in self.learned_patterns['anti_scraping_patterns']:
            self.learned_patterns['anti_scraping_patterns'][source_name] = analysis['anti_scraping_level']
    
    def _get_fallback_analysis(self):
        """Get fallback analysis when primary analysis fails"""
        return {
            'pagination_strategy': 'single_page',
            'pagination_confidence': 0.3,
            'content_structure': 'mixed',
            'content_confidence': 0.3,
            'anti_scraping_level': 'medium',
            'anti_scraping_score': 2,
            'anti_scraping_methods': [],
            'encoding_methods': [],
            'encoding_complexity': 'low',
            'success_probability': 0.5,
            'recommended_approach': 'enhanced_headers',
            'total_pages_estimate': 1
        }
    
    def _detect_encoding(self, content, url):
        """Intelligent encoding detection"""
        # Try chardet first
        detected = chardet.detect(content)
        if detected and detected['confidence'] > 0.7:
            return detected['encoding']
        
        # Fallback to common encodings
        for encoding in ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']:
            try:
                content.decode(encoding)
                return encoding
            except:
                continue
        
        return 'utf-8'  # Final fallback
    
    def discover_all_pages_universal(self, url, source_name, max_pages=25):
        """
        Universal page discovery system
        Automatically discovers all pages for any website
        """
        self._log(f"🔍 Starting universal page discovery for {source_name}")
        
        # Phase 1: Analyze the website
        analysis = self.analyze_website_intelligence(url, source_name)
        
        # Phase 2: Generate page URLs based on analysis
        pages = self._generate_page_urls(url, analysis, max_pages)
        
        # Phase 3: Validate discovered pages
        validated_pages = self._validate_page_urls(pages, source_name)
        
        self._log(f"🚀 Discovered {len(validated_pages)} pages for {source_name}")
        
        return validated_pages
    
    def _generate_page_urls(self, base_url, analysis, max_pages):
        """Generate page URLs based on analysis"""
        pages = [base_url]  # Always include the base URL
        
        strategy = analysis['pagination_strategy']
        estimated_pages = min(analysis.get('total_pages_estimate', max_pages), max_pages)
        
        if strategy == 'numbered':
            pages.extend(self._generate_numbered_pages(base_url, estimated_pages))
        elif strategy == 'parameter':
            pages.extend(self._generate_parameter_pages(base_url, estimated_pages))
        elif strategy == 'next_previous':
            pages.extend(self._generate_sequential_pages(base_url, estimated_pages))
        else:
            # Try universal patterns as fallback
            pages.extend(self._generate_universal_pattern_pages(base_url, max_pages))
        
        return list(set(pages))  # Remove duplicates
    
    def _generate_numbered_pages(self, base_url, max_pages):
        """Generate numbered pagination pages"""
        pages = []
        
        # Common numbered pagination patterns
        patterns = [
            lambda url, page: f"{url.rstrip('/')}/page/{page}/",
            lambda url, page: f"{url.rstrip('/')}/{page}/",
            lambda url, page: f"{url}?page={page}",
            lambda url, page: f"{url}{'&' if '?' in url else '?'}page={page}",
        ]
        
        for page_num in range(2, max_pages + 1):
            for pattern in patterns:
                try:
                    page_url = pattern(base_url, page_num)
                    pages.append(page_url)
                except:
                    continue
        
        return pages
    
    def _generate_parameter_pages(self, base_url, max_pages):
        """Generate parameter-based pagination pages"""
        pages = []
        
        # Extract existing parameters
        parsed_url = urlparse(base_url)
        params = parse_qs(parsed_url.query)
        
        # Parameter variations
        param_names = ['page', 'p', 'offset', 'start', 'pagenum', 'pn']
        
        for page_num in range(2, max_pages + 1):
            for param_name in param_names:
                new_params = params.copy()
                new_params[param_name] = [str(page_num)]
                
                new_query = urlencode(new_params, doseq=True)
                new_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}?{new_query}"
                pages.append(new_url)
        
        return pages
    
    def _generate_sequential_pages(self, base_url, max_pages):
        """Generate sequential pages using various patterns"""
        pages = []
        
        for page_num in range(2, max_pages + 1):
            for pattern in self.universal_pagination_patterns:
                try:
                    page_url = pattern(base_url, page_num)
                    pages.append(page_url)
                except:
                    continue
        
        return pages
    
    def _generate_universal_pattern_pages(self, base_url, max_pages):
        """Generate pages using universal patterns as fallback"""
        pages = []
        
        for page_num in range(2, min(max_pages + 1, 11)):  # Limit fallback attempts
            for pattern in self.universal_pagination_patterns:
                try:
                    page_url = pattern(base_url, page_num)
                    pages.append(page_url)
                except:
                    continue
        
        return pages
    
    def _validate_page_urls(self, pages, source_name):
        """Validate that generated page URLs actually exist"""
        validated_pages = []
        
        for page_url in pages[:50]:  # Limit validation attempts
            try:
                # Add random delay to avoid rate limiting
                time.sleep(random.uniform(0.5, 1.5))
                
                # Rotate User-Agent
                self.headers['User-Agent'] = random.choice(self.user_agents)
                
                response = self.session.head(page_url, headers=self.headers, timeout=10)
                
                if response.status_code == 200:
                    validated_pages.append(page_url)
                elif response.status_code == 404:
                    # If we get 404, likely no more pages
                    break
                    
            except Exception:
                # Continue on errors but log them
                continue
        
        return validated_pages
    
    def extract_content_universal(self, content, source_name, page_url):
        """
        Universal content extraction system
        Automatically extracts proxies from any content structure
        """
        self._log(f"🎯 Starting universal content extraction for {source_name}")
        
        try:
            # Phase 1: Decode any encoding
            decoded_content = self._universal_decode_content(content, source_name)
            
            # Phase 2: Parse with BeautifulSoup
            soup = BeautifulSoup(decoded_content, 'html.parser')
            
            # Phase 3: Try extraction methods in order of sophistication
            extraction_methods = [
                self._extract_table_content,
                self._extract_list_content,
                self._extract_card_content,
                self._extract_json_content,
                self._extract_encoded_content,
                self._extract_regex_content
            ]
            
            all_proxies = []
            
            for method in extraction_methods:
                try:
                    proxies = method(soup, decoded_content, source_name)
                    if proxies:
                        all_proxies.extend(proxies)
                        self._log(f"✅ Extracted {len(proxies)} proxies using {method.__name__}")
                except Exception:
                    continue
            
            # Remove duplicates
            unique_proxies = self._remove_duplicate_proxies(all_proxies)
            
            self._log(f"🚀 Total unique proxies extracted: {len(unique_proxies)}")
            
            return unique_proxies
            
        except Exception as e:
            self._log(f"❌ Universal extraction failed for {source_name}: {str(e)}")
            return []
    
    def _universal_decode_content(self, content, source_name):
        """Universal content decoding system"""
        decoded_content = content
        
        # Try multiple decoding methods
        decoding_methods = [
            self._decode_base64_content,
            self._decode_url_encoding,
            self._decode_html_entities,
            self._decode_unicode_escapes,
            self._decode_hexadecimal_content,
        ]
        
        for method in decoding_methods:
            try:
                decoded_content = method(decoded_content)
            except:
                continue
        
        return decoded_content
    
    def _decode_base64_content(self, content):
        """Decode Base64 encoded content"""
        # Find Base64 patterns
        base64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
        matches = re.findall(base64_pattern, content)
        
        for match in matches:
            try:
                decoded = base64.b64decode(match).decode('utf-8', errors='ignore')
                content = content.replace(match, decoded)
            except:
                continue
        
        return content
    
    def _decode_url_encoding(self, content):
        """Decode URL encoded content"""
        return urllib.parse.unquote(content)
    
    def _decode_html_entities(self, content):
        """Decode HTML entities"""
        return html.unescape(content)
    
    def _decode_unicode_escapes(self, content):
        """Decode Unicode escape sequences"""
        try:
            return content.encode().decode('unicode_escape')
        except:
            return content
    
    def _decode_hexadecimal_content(self, content):
        """Decode hexadecimal encoded content"""
        hex_pattern = r'\b[0-9A-Fa-f]{20,}\b'
        matches = re.findall(hex_pattern, content)
        
        for match in matches:
            try:
                if len(match) % 2 == 0:  # Even length
                    decoded = bytes.fromhex(match).decode('utf-8', errors='ignore')
                    if self._contains_proxy_data(decoded):
                        content = content.replace(match, decoded)
            except:
                continue
        
        return content
    
    def _contains_proxy_data(self, text):
        """Check if text contains proxy-like data"""
        return bool(re.search(r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+', text))
    
    def _extract_table_content(self, soup, content, source_name):
        """Extract proxies from HTML tables with complete data"""
        proxies = []
        tables = soup.find_all('table')
        
        for table in tables:
            rows = table.find_all('tr')
            
            # Try to identify header structure
            header_row = rows[0] if rows else None
            header_texts = []
            if header_row:
                header_texts = [th.get_text().lower().strip() for th in header_row.find_all(['th', 'td'])]
            
            # Map column indices based on headers
            ip_col = port_col = country_col = protocol_col = anonymity_col = 0
            
            for i, header in enumerate(header_texts):
                if any(keyword in header for keyword in ['ip', 'address', 'host']):
                    ip_col = i
                elif 'port' in header:
                    port_col = i
                elif any(keyword in header for keyword in ['country', 'location', 'nation']):
                    country_col = i
                elif any(keyword in header for keyword in ['protocol', 'type', 'scheme']):
                    protocol_col = i
                elif any(keyword in header for keyword in ['anonymous', 'level', 'anon']):
                    anonymity_col = i
            
            for row in rows[1:]:  # Skip header row
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    # Extract IP and port from appropriate columns
                    ip_text = cells[ip_col].get_text(strip=True) if ip_col < len(cells) else ''
                    port_text = cells[port_col].get_text(strip=True) if port_col < len(cells) else ''
                    
                    # Handle combined IP:PORT in single cell
                    if ':' in ip_text and not port_text:
                        ip_port = ip_text.split(':')
                        if len(ip_port) == 2:
                            ip_text, port_text = ip_port
                    
                    # Validate IP and port
                    if self._is_valid_ip(ip_text) and self._is_valid_port(port_text):
                        proxy = {
                            'ip': ip_text,
                            'port': port_text,  # Keep as string for UI display
                            'country': cells[country_col].get_text(strip=True) if country_col < len(cells) else 'Unknown',
                            'protocol': self._determine_protocol_enhanced(cells, protocol_col, source_name),
                            'anonymity': cells[anonymity_col].get_text(strip=True) if anonymity_col < len(cells) else 'Unknown',
                            'source': source_name,
                            'timestamp': datetime.now().isoformat()
                        }
                        proxies.append(proxy)
        
        return proxies
    
    def _extract_list_content(self, soup, content, source_name):
        """Extract proxies from HTML lists with enhanced data"""
        proxies = []
        lists = soup.find_all(['ul', 'ol'])
        
        for list_elem in lists:
            items = list_elem.find_all('li')
            
            for item in items:
                text = item.get_text(strip=True)
                
                # Look for IP:PORT patterns
                proxy_matches = re.findall(r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{2,5})', text)
                
                for ip, port in proxy_matches:
                    if self._is_valid_ip(ip) and self._is_valid_port(port):
                        # Try to extract additional info from the same text
                        country = self._extract_country_from_text(text)
                        protocol = self._extract_protocol_from_text(text, source_name)
                        anonymity = self._extract_anonymity_from_text(text)
                        
                        proxy = {
                            'ip': ip,
                            'port': port,
                            'country': country,
                            'protocol': protocol,
                            'anonymity': anonymity,
                            'source': source_name,
                            'timestamp': datetime.now().isoformat()
                        }
                        proxies.append(proxy)
        
        return proxies
    
    def _extract_card_content(self, soup, content, source_name):
        """Extract proxies from card/div structures with enhanced data"""
        proxies = []
        
        # Look for common card patterns
        card_selectors = [
            'div[class*="card"]',
            'div[class*="item"]',
            'div[class*="proxy"]',
            'div[class*="entry"]',
            '.proxy-item',
            '.proxy-card',
            '.list-item'
        ]
        
        for selector in card_selectors:
            cards = soup.select(selector)
            
            for card in cards:
                text = card.get_text(strip=True)
                
                # Look for IP:PORT patterns
                proxy_matches = re.findall(r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{2,5})', text)
                
                for ip, port in proxy_matches:
                    if self._is_valid_ip(ip) and self._is_valid_port(port):
                        # Extract additional info from card content
                        country = self._extract_country_from_text(text)
                        protocol = self._extract_protocol_from_text(text, source_name)
                        anonymity = self._extract_anonymity_from_text(text)
                        
                        proxy = {
                            'ip': ip,
                            'port': port,
                            'country': country,
                            'protocol': protocol,
                            'anonymity': anonymity,
                            'source': source_name,
                            'timestamp': datetime.now().isoformat()
                        }
                        proxies.append(proxy)
        
        return proxies
    
    def _extract_json_content(self, soup, content, source_name):
        """Extract proxies from embedded JSON with complete data"""
        proxies = []
        
        # Look for JSON in script tags
        scripts = soup.find_all('script')
        
        for script in scripts:
            script_text = script.get_text()
            
            # Try to find JSON structures
            json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
            json_matches = re.findall(json_pattern, script_text)
            
            for json_str in json_matches:
                try:
                    data = json.loads(json_str)
                    proxies.extend(self._extract_proxies_from_json_enhanced(data, source_name))
                except:
                    continue
        
        return proxies
    
    def _extract_encoded_content(self, soup, content, source_name):
        """Extract proxies from encoded content with enhanced data"""
        proxies = []
        
        # The content should already be decoded by _universal_decode_content
        # Extract proxies using enhanced regex
        
        # Enhanced patterns for better data extraction
        enhanced_patterns = [
            r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{2,5})\s*\|\s*([A-Z]{2})\s*\|\s*([^|]+)\s*\|\s*([^|]+)',  # IP:PORT|CC|Protocol|Anonymity
            r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{2,5})\s*,\s*([A-Z]{2})\s*,\s*([^,]+)',  # IP:PORT,CC,Protocol
            r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{2,5})',  # Basic IP:PORT
        ]
        
        for pattern in enhanced_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if len(match) >= 2:
                    ip = match[0]
                    port = match[1]
                    country = match[2] if len(match) > 2 else 'Unknown'
                    protocol = match[3] if len(match) > 3 else 'HTTP'
                    anonymity = match[4] if len(match) > 4 else 'Unknown'
                    
                    if self._is_valid_ip(ip) and self._is_valid_port(port):
                        proxy = {
                            'ip': ip,
                            'port': port,
                            'country': country.strip() if country != 'Unknown' else 'Unknown',
                            'protocol': protocol.upper().strip() if protocol != 'HTTP' else 'HTTP',
                            'anonymity': anonymity.strip() if anonymity != 'Unknown' else 'Unknown',
                            'source': source_name,
                            'timestamp': datetime.now().isoformat()
                        }
                        proxies.append(proxy)
        
        return proxies
    
    def _extract_regex_content(self, soup, content, source_name):
        """Extract proxies using regex patterns with enhanced data"""
        proxies = []
        
        # Use enhanced patterns for better data extraction
        enhanced_patterns = [
            r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{2,5})\s*\|\s*([A-Z]{2})\s*\|\s*([^|]+)\s*\|\s*([^|]+)',
            r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{2,5})\s*,\s*([A-Z]{2})\s*,\s*([^,]+)',
            r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{2,5})'
        ]
        
        for pattern in enhanced_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            
            for match in matches:
                ip = match[0]
                port = match[1]
                country = match[2] if len(match) > 2 else 'Unknown'
                protocol = match[3] if len(match) > 3 else self._determine_protocol_from_source(source_name)
                anonymity = match[4] if len(match) > 4 else 'Unknown'
                
                # Clean up the match
                if self._is_valid_ip(ip) and self._is_valid_port(port):
                    proxy = {
                        'ip': ip,
                        'port': port,
                        'country': country.strip() if country != 'Unknown' else 'Unknown',
                        'protocol': protocol.upper().strip() if protocol else 'HTTP',
                        'anonymity': anonymity.strip() if anonymity != 'Unknown' else 'Unknown',
                        'source': source_name,
                        'timestamp': datetime.now().isoformat()
                    }
                    proxies.append(proxy)
        
        return proxies
    
    def _extract_proxies_from_json_enhanced(self, data, source_name):
        """Extract proxies from JSON data structure with complete data"""
        proxies = []
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, list):
                    proxies.extend(self._extract_proxies_from_json_enhanced(value, source_name))
                elif isinstance(value, dict):
                    proxies.extend(self._extract_proxies_from_json_enhanced(value, source_name))
                elif isinstance(value, str) and ':' in value:
                    # Check if it's a proxy string
                    if self._contains_proxy_data(value):
                        ip_port = value.split(':')
                        if len(ip_port) == 2:
                            ip, port = ip_port
                            if self._is_valid_ip(ip) and self._is_valid_port(port):
                                proxy = {
                                    'ip': ip,
                                    'port': port,
                                    'country': 'Unknown',
                                    'protocol': 'HTTP',
                                    'anonymity': 'Unknown',
                                    'source': source_name,
                                    'timestamp': datetime.now().isoformat()
                                }
                                proxies.append(proxy)
        
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, dict):
                    # Enhanced JSON object parsing
                    ip = item.get('ip') or item.get('host') or item.get('address')
                    port = str(item.get('port', ''))
                    if ip and port and self._is_valid_ip(ip) and self._is_valid_port(port):
                        proxy = {
                            'ip': ip,
                            'port': port,
                            'country': item.get('country', item.get('countryCode', item.get('location', 'Unknown'))),
                            'protocol': (item.get('protocol') or item.get('type') or item.get('scheme') or 'HTTP').upper(),
                            'anonymity': item.get('anonymity', item.get('level', item.get('anonymityLevel', 'Unknown'))),
                            'source': source_name,
                            'timestamp': datetime.now().isoformat()
                        }
                        proxies.append(proxy)
                else:
                    proxies.extend(self._extract_proxies_from_json_enhanced(item, source_name))
        
        return proxies
    
    def _determine_protocol_enhanced(self, cells, protocol_col, source_name):
        """Enhanced protocol determination"""
        protocol = 'HTTP'  # Default
        
        # Check protocol column if available
        if protocol_col < len(cells):
            protocol_text = cells[protocol_col].get_text().lower().strip()
            if 'https' in protocol_text or 'ssl' in protocol_text:
                protocol = 'HTTPS'
            elif 'socks5' in protocol_text:
                protocol = 'SOCKS5'
            elif 'socks4' in protocol_text:
                protocol = 'SOCKS4'
            elif 'http' in protocol_text:
                protocol = 'HTTP'
        
        # Check source name for hints
        return self._determine_protocol_from_source(source_name) if protocol == 'HTTP' else protocol

    def _determine_protocol_from_source(self, source_name):
        """Determine protocol from source name"""
        source_lower = source_name.lower()
        if 'socks5' in source_lower:
            return 'SOCKS5'
        elif 'socks4' in source_lower:
            return 'SOCKS4'
        elif 'ssl' in source_lower or 'https' in source_lower:
            return 'HTTPS'
        return 'HTTP'

    def _extract_country_from_text(self, text):
        """Extract country information from text"""
        # Common country code patterns
        country_patterns = [
            r'\b([A-Z]{2})\b',  # 2-letter country codes
            r'\b(United States|US|USA)\b',
            r'\b(United Kingdom|UK|GB)\b',
            r'\b(Germany|DE)\b',
            r'\b(France|FR)\b',
            r'\b(Canada|CA)\b',
            r'\b(Japan|JP)\b',
            r'\b(China|CN)\b',
            r'\b(Russia|RU)\b',
            r'\b(Brazil|BR)\b',
            r'\b(India|IN)\b',
            r'\b(Australia|AU)\b'
        ]
        
        for pattern in country_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).upper()
        
        return 'Unknown'

    def _extract_protocol_from_text(self, text, source_name):
        """Extract protocol from text"""
        text_lower = text.lower()
        if 'socks5' in text_lower:
            return 'SOCKS5'
        elif 'socks4' in text_lower:
            return 'SOCKS4'
        elif 'https' in text_lower or 'ssl' in text_lower:
            return 'HTTPS'
        elif 'http' in text_lower:
            return 'HTTP'
        
        return self._determine_protocol_from_source(source_name)

    def _extract_anonymity_from_text(self, text):
        """Extract anonymity level from text"""
        text_lower = text.lower()
        if any(word in text_lower for word in ['elite', 'high', 'level1']):
            return 'Elite'
        elif any(word in text_lower for word in ['anonymous', 'anon', 'level2']):
            return 'Anonymous'
        elif any(word in text_lower for word in ['transparent', 'none', 'level3']):
            return 'Transparent'
        
        return 'Unknown'
    
    def _remove_duplicate_proxies(self, proxies):
        """Remove duplicate proxies"""
        seen = set()
        unique_proxies = []
        
        for proxy in proxies:
            key = f"{proxy['ip']}:{proxy['port']}"
            if key not in seen:
                seen.add(key)
                unique_proxies.append(proxy)
        
        return unique_proxies
    
    def save_learned_patterns(self, filepath):
        """Save learned patterns to file for future use"""
        try:
            with open(filepath, 'w') as f:
                json.dump(self.learned_patterns, f, indent=2)
            self._log(f"✅ Learned patterns saved to {filepath}")
        except Exception as e:
            self._log(f"❌ Failed to save learned patterns: {str(e)}")
    
    def load_learned_patterns(self, filepath):
        """Load learned patterns from file"""
        try:
            if os.path.exists(filepath):
                with open(filepath, 'r') as f:
                    self.learned_patterns = json.load(f)
                self._log(f"✅ Learned patterns loaded from {filepath}")
        except Exception as e:
            self._log(f"❌ Failed to load learned patterns: {str(e)}")