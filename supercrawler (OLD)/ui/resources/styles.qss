
            /* Dark theme stylesheet for SuperCrawler */
            QWidget {
                background-color: #2D2D2D;
                color: #EEEEEE;
                font-family: 'Segoe UI', <PERSON>l, sans-serif;
            }
            
            QMainWindow {
                background-color: #2D2D2D;
            }
            
            QTabWidget::pane {
                border: 1px solid #444444;
                background-color: #2D2D2D;
            }
            
            QTabWidget::tab-bar {
                left: 5px;
            }
            
            QTabBar::tab {
                background-color: #3D3D3D;
                color: #CCCCCC;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            
            QTabBar::tab:selected {
                background-color: #4D4D4D;
                color: #FFFFFF;
            }
            
            QPushButton {
                background-color: #444444;
                color: #EEEEEE;
                border: 1px solid #555555;
                padding: 6px 12px;
                border-radius: 4px;
            }
            
            QPushButton:hover {
                background-color: #555555;
            }
            
            QPushButton:pressed {
                background-color: #505050;
            }
            
            QLineEdit, QTextEdit, QPlainTextEdit {
                background-color: #3A3A3A;
                color: #EEEEEE;
                border: 1px solid #555555;
                border-radius: 3px;
                padding: 3px;
            }
            
            QLabel {
                color: #EEEEEE;
            }
            
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 3px;
                background-color: #3A3A3A;
                color: #EEEEEE;
                text-align: center;
            }
            
            QProgressBar::chunk {
                background-color: #3A7734;
            }
            
            QStatusBar {
                background-color: #363636;
                color: #DDDDDD;
            }
            
            QMenuBar {
                background-color: #363636;
                color: #EEEEEE;
            }
            
            QMenuBar::item {
                background-color: transparent;
                padding: 5px 10px;
            }
            
            QMenuBar::item:selected {
                background-color: #444444;
            }
            
            QMenu {
                background-color: #363636;
                color: #EEEEEE;
                border: 1px solid #555555;
            }
            
            QMenu::item {
                padding: 5px 20px;
            }
            
            QMenu::item:selected {
                background-color: #444444;
            }
            