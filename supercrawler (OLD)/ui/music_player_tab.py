import os
import pygame
import glob
import time
import numpy as np
import random
import math
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QSlider, QLabel, QListWidget, QGroupBox, QMessageBox,
                            QProgressBar)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPainter, QColor, QLinearGradient, QPen, QBrush

# Try to import mutagen for getting song duration
try:
    from mutagen.mp3 import MP3
    from mutagen.mp4 import MP4
    from mutagen.flac import FLAC
    from mutagen.oggvorbis import OggVorbis
    MUTAGEN_AVAILABLE = True
except ImportError:
    MUTAGEN_AVAILABLE = False

# Try to import librosa for audio analysis
try:
    import librosa
    import scipy.signal
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False

class MusicVisualizerWidget(QWidget):
    """Music visualizer widget with spectrum bars"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(150)
        self.setMinimumWidth(400)
        
        # Visualizer settings
        self.num_bars = 64  # Number of frequency bars
        self.bar_heights = [0] * self.num_bars
        self.target_heights = [0] * self.num_bars
        self.peak_heights = [0] * self.num_bars
        self.peak_timers = [0] * self.num_bars
        
        # Animation settings
        self.smoothing_factor = 0.3  # Slower, smoother transitions
        self.peak_decay = 0.98  # Slower peak decay
        self.peak_hold_time = 15  # frames to hold peak (adjusted for slower FPS)
        
        # Colors for gradient effect
        self.colors = [
            QColor(0, 255, 0),    # Green (low frequencies)
            QColor(255, 255, 0),  # Yellow (mid frequencies)  
            QColor(255, 128, 0),  # Orange (mid-high frequencies)
            QColor(255, 0, 0),    # Red (high frequencies)
        ]
        
        # Timer for animation
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(100)  # ~10 FPS - slower for more relaxed feel
        
        # Playing state
        self.is_playing = False
        
        # Audio analysis data
        self.audio_data = None
        self.sample_rate = None
        self.spectrogram = None
        self.current_audio_file = None
        
        # Set background
        self.setStyleSheet("""
            MusicVisualizerWidget {
                background-color: #1a1a1a;
                border: 2px solid #333333;
                border-radius: 8px;
            }
        """)
        
    def analyze_audio_file(self, file_path):
        """Analyze audio file to get real frequency data"""
        if not LIBROSA_AVAILABLE:
            return False
            
        try:
            # Load audio file
            y, sr = librosa.load(file_path, sr=22050)  # Resample to 22kHz for efficiency
            
            # Compute spectrogram
            hop_length = 512
            n_fft = 2048
            S = librosa.stft(y, hop_length=hop_length, n_fft=n_fft)
            S_magnitude = np.abs(S)
            
            # Convert to dB scale
            S_db = librosa.amplitude_to_db(S_magnitude, ref=np.max)
            
            # Store analysis data
            self.audio_data = y
            self.sample_rate = sr
            self.spectrogram = S_db
            self.hop_length = hop_length
            self.current_audio_file = file_path
            
            return True
            
        except Exception as e:
            print(f"Error analyzing audio file {file_path}: {e}")
            return False
    
    def get_real_audio_data(self, current_time, is_playing):
        """Get real audio frequency data for current playback time"""
        if not is_playing:
            # Fade out when not playing
            for i in range(self.num_bars):
                self.target_heights[i] *= 0.85
            return
        
        # If we have real audio analysis, use it
        if LIBROSA_AVAILABLE and self.spectrogram is not None:
            try:
                # Calculate current frame in spectrogram
                frames_per_second = self.sample_rate / self.hop_length
                current_frame = int(current_time * frames_per_second)
                
                # Make sure we don't go out of bounds
                if current_frame >= self.spectrogram.shape[1]:
                    current_frame = self.spectrogram.shape[1] - 1
                
                # Get frequency data for current time
                current_spectrum = self.spectrogram[:, current_frame]
                
                # Map frequency bins to our visualization bars
                n_freq_bins = len(current_spectrum)
                bars_per_bin = max(1, n_freq_bins // self.num_bars)
                
                for i in range(self.num_bars):
                    # Get average magnitude for this frequency range
                    start_bin = i * bars_per_bin
                    end_bin = min((i + 1) * bars_per_bin, n_freq_bins)
                    
                    if start_bin < end_bin:
                        # Average the magnitude in this frequency range
                        avg_magnitude = np.mean(current_spectrum[start_bin:end_bin])
                        
                        # Convert from dB to linear scale (0-1)
                        # dB values are typically -80 to 0, so we normalize
                        normalized = (avg_magnitude + 80) / 80  # Scale -80dB to 0dB -> 0 to 1
                        normalized = max(0, min(1, normalized))  # Clamp
                        
                        # Apply some smoothing with previous value
                        if len(self.target_heights) > i:
                            normalized = (self.target_heights[i] * 0.3) + (normalized * 0.7)
                        
                        self.target_heights[i] = normalized
                    else:
                        self.target_heights[i] *= 0.9  # Fade out unused bars
                return
                        
            except Exception as e:
                print(f"Error getting real audio data: {e}")
                # Fall back to simulation
        
        # Fallback to simulated data if real analysis isn't available
        self.simulate_audio_data_fallback(is_playing)
    
    def simulate_audio_data_fallback(self, is_playing):
        """Fallback simulated audio data when real analysis isn't available"""
        if not is_playing:
            for i in range(self.num_bars):
                self.target_heights[i] *= 0.85
            return
            
        # Generate simulated frequency data
        for i in range(self.num_bars):
            # Create more realistic frequency response
            freq_factor = i / self.num_bars
            
            # Bass frequencies (0-0.2) - higher amplitude
            if freq_factor < 0.2:
                base_amplitude = random.uniform(0.4, 0.8)
            # Mid frequencies (0.2-0.6) - medium amplitude  
            elif freq_factor < 0.6:
                base_amplitude = random.uniform(0.3, 0.6)
            # High frequencies (0.6-1.0) - lower amplitude
            else:
                base_amplitude = random.uniform(0.1, 0.4)
            
            # Add some randomness but with less variation
            amplitude = base_amplitude * random.uniform(0.7, 1.1)
            amplitude = max(0, min(1, amplitude))  # Clamp between 0-1
            
            # Blend with previous value for smoother transitions
            if len(self.target_heights) > i:
                amplitude = (self.target_heights[i] * 0.6) + (amplitude * 0.4)
            
            self.target_heights[i] = amplitude
    
    def update_animation(self):
        """Update animation frame"""
        # Get current playback time from parent if available
        current_time = 0
        if hasattr(self.parent(), 'get_current_playback_time'):
            current_time = self.parent().get_current_playback_time()
        
        # Generate real audio data based on current playback time
        self.get_real_audio_data(current_time, self.is_playing)
        
        updated = False
        
        for i in range(self.num_bars):
            # Smooth animation towards target
            if abs(self.bar_heights[i] - self.target_heights[i]) > 0.01:
                self.bar_heights[i] += (self.target_heights[i] - self.bar_heights[i]) * self.smoothing_factor
                updated = True
            
            # Handle peaks
            if self.bar_heights[i] > self.peak_heights[i]:
                self.peak_heights[i] = self.bar_heights[i]
                self.peak_timers[i] = self.peak_hold_time
            else:
                if self.peak_timers[i] > 0:
                    self.peak_timers[i] -= 1
                else:
                    self.peak_heights[i] *= self.peak_decay
                    if self.peak_heights[i] > 0:
                        updated = True
        
        if updated:
            self.update()
    
    def paintEvent(self, event):
        """Paint the visualizer"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        rect = self.rect()
        bar_width = rect.width() / self.num_bars
        max_height = rect.height() - 20  # Leave some margin
        
        for i in range(self.num_bars):
            # Calculate bar position and height
            x = i * bar_width
            bar_height = self.bar_heights[i] * max_height
            peak_height = self.peak_heights[i] * max_height
            
            # Choose color based on frequency (lower = green, higher = red)
            color_index = min(3, int((i / self.num_bars) * 4))
            base_color = self.colors[color_index]
            
            # Create gradient for bar
            gradient = QLinearGradient(0, rect.height(), 0, rect.height() - bar_height)
            
            # Darker color at bottom, brighter at top
            dark_color = QColor(base_color)
            dark_color.setAlpha(100)
            bright_color = QColor(base_color)
            bright_color.setAlpha(255)
            
            gradient.setColorAt(0, dark_color)
            gradient.setColorAt(1, bright_color)
            
            # Draw main bar
            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.NoPen)
            painter.drawRect(int(x + 1), int(rect.height() - bar_height), 
                           int(bar_width - 2), int(bar_height))
            
            # Draw peak line
            if peak_height > 5:  # Only draw if peak is visible
                peak_color = QColor(base_color)
                peak_color.setAlpha(200)
                painter.setPen(QPen(peak_color, 2))
                peak_y = rect.height() - peak_height
                painter.drawLine(int(x + 1), int(peak_y), 
                               int(x + bar_width - 1), int(peak_y))
    
    def set_playing(self, is_playing):
        """Update visualizer based on playback state"""
        self.is_playing = is_playing
    
    def load_audio_file(self, file_path):
        """Load and analyze new audio file"""
        if file_path != self.current_audio_file:
            if LIBROSA_AVAILABLE:
                success = self.analyze_audio_file(file_path)
                if not success:
                    print(f"Could not analyze audio file: {file_path}")
                    # Reset to prevent using old data
                    self.audio_data = None
                    self.spectrogram = None
                    self.current_audio_file = None
            else:
                print("Librosa not available - using simulated visualizer")
                self.current_audio_file = file_path

class MusicPlayerTab(QWidget):
    """Music Player Tab for SuperCrawler"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        
        # Initialize pygame mixer
        pygame.mixer.init()
        
        # Music player state
        self.current_song_index = 0
        self.is_playing = False
        self.is_paused = False
        self.current_song = None
        self.song_length = 0
        self.current_position = 0
        self.start_time = 0
        self.pause_time = 0
        
        # Music folder path
        self.music_folder = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "music")
        
        # Load music files
        self.music_files = []
        self.load_music_files()
        
        # Create visualizer before UI setup
        self.visualizer = MusicVisualizerWidget(self)
        
        # Setup UI
        self.setup_ui()
        
        # Setup timer for progress updates
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.update_progress)
        self.progress_timer.start(100)  # Update every 100ms
        
    def load_music_files(self):
        """Load all music files from the music folder"""
        if not os.path.exists(self.music_folder):
            return
            
        # Supported audio formats
        extensions = ['*.mp3', '*.wav', '*.ogg', '*.m4a']
        
        for extension in extensions:
            files = glob.glob(os.path.join(self.music_folder, extension))
            self.music_files.extend(files)
            
        # Sort files by name
        self.music_files.sort()
        
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Title
        title_label = QLabel("🎵 Music Player")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Current song info
        self.current_song_group = QGroupBox("Now Playing")
        current_song_layout = QVBoxLayout(self.current_song_group)
        
        self.current_song_label = QLabel("No song selected")
        self.current_song_label.setAlignment(Qt.AlignCenter)
        self.current_song_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2C3E50;")
        current_song_layout.addWidget(self.current_song_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                text-align: center;
                background-color: #ECF0F1;
            }
            QProgressBar::chunk {
                background-color: #3498DB;
                border-radius: 3px;
            }
        """)
        current_song_layout.addWidget(self.progress_bar)
        
        # Time labels
        time_layout = QHBoxLayout()
        self.current_time_label = QLabel("00:00")
        self.total_time_label = QLabel("00:00")
        time_layout.addWidget(self.current_time_label)
        time_layout.addStretch()
        time_layout.addWidget(self.total_time_label)
        current_song_layout.addLayout(time_layout)
        
        layout.addWidget(self.current_song_group)
        
        # Music Visualizer
        visualizer_group = QGroupBox("🎵 Visualizer")
        visualizer_layout = QVBoxLayout(visualizer_group)
        visualizer_layout.addWidget(self.visualizer)
        layout.addWidget(visualizer_group)
        
        # Control buttons
        controls_group = QGroupBox("Controls")
        controls_layout = QVBoxLayout(controls_group)
        
        # Main control buttons
        button_layout = QHBoxLayout()
        
        self.play_button = QPushButton("▶️ Play")
        self.play_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ECC71;
            }
            QPushButton:pressed {
                background-color: #229954;
            }
        """)
        self.play_button.clicked.connect(self.toggle_play_pause)
        
        self.stop_button = QPushButton("⏹️ Stop")
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #EC7063;
            }
            QPushButton:pressed {
                background-color: #C0392B;
            }
        """)
        self.stop_button.clicked.connect(self.stop_music)
        
        self.skip_button = QPushButton("⏭️ Skip")
        self.skip_button.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5DADE2;
            }
            QPushButton:pressed {
                background-color: #2980B9;
            }
        """)
        self.skip_button.clicked.connect(self.skip_song)
        
        button_layout.addWidget(self.play_button)
        button_layout.addWidget(self.stop_button)
        button_layout.addWidget(self.skip_button)
        controls_layout.addLayout(button_layout)
        
        # Volume control
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(QLabel("🔊 Volume:"))
        
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)  # Default volume
        self.volume_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #BDC3C7;
                height: 8px;
                background: #ECF0F1;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #3498DB;
                border: 1px solid #2980B9;
                width: 18px;
                height: 18px;
                border-radius: 9px;
                margin: -5px 0;
            }
            QSlider::sub-page:horizontal {
                background: #3498DB;
                border-radius: 4px;
            }
        """)
        self.volume_slider.valueChanged.connect(self.change_volume)
        
        self.volume_label = QLabel("70%")
        self.volume_label.setMinimumWidth(40)
        
        volume_layout.addWidget(self.volume_slider)
        volume_layout.addWidget(self.volume_label)
        controls_layout.addLayout(volume_layout)
        
        layout.addWidget(controls_group)
        
        # Playlist
        playlist_group = QGroupBox("Playlist")
        playlist_layout = QVBoxLayout(playlist_group)
        
        self.playlist_widget = QListWidget()
        self.playlist_widget.setStyleSheet("""
            QListWidget {
                border: 1px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
                alternate-background-color: #F8F9FA;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ECF0F1;
            }
            QListWidget::item:selected {
                background-color: #3498DB;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #E8F4FD;
            }
        """)
        self.playlist_widget.itemDoubleClicked.connect(self.play_selected_song)
        playlist_layout.addWidget(self.playlist_widget)
        
        layout.addWidget(playlist_group)
        
        # Load playlist
        self.load_playlist()
        
        # Set initial volume
        pygame.mixer.music.set_volume(0.7)
    
    def get_current_playback_time(self):
        """Get current playback time in seconds"""
        if self.is_playing and self.start_time > 0:
            current_time = time.time()
            elapsed_time = current_time - self.start_time
            return elapsed_time
        return 0
        
    def load_playlist(self):
        """Load music files into the playlist"""
        self.playlist_widget.clear()
        
        if not self.music_files:
            self.playlist_widget.addItem("No music files found in the music folder")
            return
            
        for file_path in self.music_files:
            filename = os.path.basename(file_path)
            # Remove file extension for display
            display_name = os.path.splitext(filename)[0]
            self.playlist_widget.addItem(display_name)
            
    def toggle_play_pause(self):
        """Toggle between play and pause"""
        if not self.music_files:
            QMessageBox.warning(self, "No Music", "No music files found in the music folder!")
            return
            
        if not self.is_playing and not self.is_paused:
            # Start playing
            self.play_current_song()
        elif self.is_playing:
            # Pause
            pygame.mixer.music.pause()
            self.is_playing = False
            self.is_paused = True
            self.pause_time = time.time()
            self.play_button.setText("▶️ Play")
            
            # Pause visualizer
            self.visualizer.set_playing(False)
        elif self.is_paused:
            # Resume
            pygame.mixer.music.unpause()
            self.is_playing = True
            self.is_paused = False
            # Adjust start time to account for pause duration
            if self.pause_time > 0:
                pause_duration = time.time() - self.pause_time
                self.start_time += pause_duration
            self.play_button.setText("⏸️ Pause")
            
            # Resume visualizer
            self.visualizer.set_playing(True)
            
    def play_current_song(self):
        """Play the current song"""
        if not self.music_files:
            return
            
        if self.current_song_index >= len(self.music_files):
            self.current_song_index = 0
            
        current_file = self.music_files[self.current_song_index]
        
        try:
            pygame.mixer.music.load(current_file)
            pygame.mixer.music.play()
            
            self.is_playing = True
            self.is_paused = False
            self.current_song = current_file
            self.start_time = time.time()
            self.pause_time = 0
            
            # Load audio file for analysis and start visualizer
            self.visualizer.load_audio_file(current_file)
            self.visualizer.set_playing(True)
            
            # Get song duration
            self.song_length = self.get_song_duration(current_file)
            
            # Update UI
            filename = os.path.basename(current_file)
            display_name = os.path.splitext(filename)[0]
            self.current_song_label.setText(display_name)
            self.play_button.setText("⏸️ Pause")
            
            # Highlight current song in playlist
            self.playlist_widget.setCurrentRow(self.current_song_index)
            
            # Reset progress
            self.progress_bar.setValue(0)
            self.current_time_label.setText("00:00")
            if self.song_length > 0:
                self.total_time_label.setText(self.format_time(self.song_length))
            else:
                self.total_time_label.setText("--:--")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Could not play music file: {str(e)}")
            
    def stop_music(self):
        """Stop music playback"""
        pygame.mixer.music.stop()
        self.is_playing = False
        self.is_paused = False
        self.start_time = 0
        self.pause_time = 0
        self.play_button.setText("▶️ Play")
        self.current_song_label.setText("Stopped")
        self.progress_bar.setValue(0)
        self.current_time_label.setText("00:00")
        self.total_time_label.setText("00:00")
        
        # Stop visualizer
        self.visualizer.set_playing(False)
        
    def skip_song(self):
        """Skip to the next song"""
        if not self.music_files:
            return
            
        self.current_song_index = (self.current_song_index + 1) % len(self.music_files)
        
        if self.is_playing or self.is_paused:
            self.play_current_song()
        else:
            # Just update the selection
            self.playlist_widget.setCurrentRow(self.current_song_index)
            
    def play_selected_song(self, item):
        """Play the selected song from playlist"""
        if not self.music_files:
            return
            
        self.current_song_index = self.playlist_widget.currentRow()
        self.play_current_song()
        
    def change_volume(self, value):
        """Change the volume"""
        volume = value / 100.0
        pygame.mixer.music.set_volume(volume)
        self.volume_label.setText(f"{value}%")
        
    def update_progress(self):
        """Update the progress bar and time labels"""
        if self.is_playing and pygame.mixer.music.get_busy():
            # Calculate elapsed time
            current_time = time.time()
            elapsed_time = current_time - self.start_time
            
            # Update current time label
            self.current_time_label.setText(self.format_time(elapsed_time))
            
            # Update progress bar if we know the song length
            if self.song_length > 0:
                progress_percentage = (elapsed_time / self.song_length) * 100
                progress_percentage = min(100, max(0, progress_percentage))  # Clamp between 0-100
                self.progress_bar.setValue(int(progress_percentage))
            
        elif self.is_playing and not pygame.mixer.music.get_busy():
            # Song finished, skip to next
            self.skip_song()
            
    def get_song_duration(self, file_path):
        """Get the duration of a song file in seconds"""
        try:
            if not MUTAGEN_AVAILABLE:
                # Fallback: estimate duration (not accurate, but better than nothing)
                return 180  # 3 minutes default
                
            # Get file extension
            _, ext = os.path.splitext(file_path.lower())
            
            if ext == '.mp3':
                audio = MP3(file_path)
                return audio.info.length
            elif ext in ['.m4a', '.mp4']:
                audio = MP4(file_path)
                return audio.info.length
            elif ext == '.flac':
                audio = FLAC(file_path)
                return audio.info.length
            elif ext == '.ogg':
                audio = OggVorbis(file_path)
                return audio.info.length
            else:
                return 180  # Default 3 minutes for unsupported formats
                
        except Exception as e:
            print(f"Error getting duration for {file_path}: {e}")
            return 180  # Default 3 minutes if we can't get duration
    
    def format_time(self, seconds):
        """Format time in MM:SS format"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}" 