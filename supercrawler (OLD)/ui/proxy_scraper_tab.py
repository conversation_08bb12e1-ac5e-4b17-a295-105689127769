#!/usr/bin/env python3
"""
Fixed Proxy Scraper Tab - Simple and Reliable
Uses simple scraper for easy sources, fallback for complex ones
"""

import os
import json
import time
import requests
import random
import re
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QTextEdit, QTableWidget,
                            QGroupBox, QFormLayout, QComboBox, QSpinBox,
                            QCheckBox, QProgressBar, QHeaderView,
                            QTableWidgetItem, QMessageBox, QSplitter,
                            QFileDialog, QLineEdit, QListWidget, 
                            QProgressDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, pyqtSlot, QTimer
from PyQt5.QtGui import QFont

# Import the simple scraper
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from proxy_scraper_simple import SimpleProxyScraper

from intellicrawler.utils.error_handler import try_except_with_dialog
from intellicrawler.utils.logger import get_logger
from intellicrawler.utils.config import load_config, save_config

class SimpleProxyScraperWorker(QThread):
    """Simple worker thread that uses the simple scraper"""
    progress_updated = pyqtSignal(int, str)
    proxy_found = pyqtSignal(dict)
    scraping_completed = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, max_proxies=5000):
        super().__init__()
        self.max_proxies = max_proxies
        self.should_stop = False
        self.scraper = SimpleProxyScraper(max_proxies)
        
    def run(self):
        """Run the simple scraper"""
        try:
            def progress_callback(percent, message):
                if not self.should_stop:
                    self.progress_updated.emit(percent, message)
            
            # Scrape all sources
            proxies = self.scraper.scrape_all_sources(progress_callback)
            
            # Emit each proxy found
            for proxy in proxies:
                if self.should_stop:
                    break
                self.proxy_found.emit(proxy)
            
            if not self.should_stop:
                self.scraping_completed.emit(proxies)
                
        except Exception as e:
            self.error_occurred.emit(f"Scraping failed: {str(e)}")
    
    def stop(self):
        """Stop the scraping"""
        self.should_stop = True

class ProxyScraperTab(QWidget):
    """Simple and Reliable Proxy Scraper Tab"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.logger = get_logger()
        self.scraped_proxies = []
        self.scraper_worker = None
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("🚀 Simple & Fast Proxy Scraper")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E8B57;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        self.status_label = QLabel("Ready to scrape proxies")
        self.status_label.setStyleSheet("color: #27AE60; font-weight: bold;")
        header_layout.addWidget(self.status_label)
        
        layout.addLayout(header_layout)
        
        # Main content with better proportions for fullscreen
        splitter = QSplitter(Qt.Horizontal)
        splitter.setChildrenCollapsible(False)
        self._setup_config_panel(splitter)
        self._setup_results_panel(splitter)
        
        # Better proportions: 25% config, 75% results for fullscreen viewing
        splitter.setSizes([350, 1050])
        splitter.setStretchFactor(0, 0)  # Config panel doesn't stretch
        splitter.setStretchFactor(1, 1)  # Results panel stretches
        
        layout.addWidget(splitter)
        
    def _setup_config_panel(self, splitter):
        """Setup configuration panel"""
        config_widget = QWidget()
        config_widget.setMaximumWidth(400)  # Prevent config panel from getting too wide
        config_widget.setMinimumWidth(300)  # Ensure minimum readable width
        config_layout = QVBoxLayout(config_widget)
        config_layout.setContentsMargins(10, 10, 10, 10)
        config_layout.setSpacing(10)
        
        # Simple explanation with better styling
        info_label = QLabel("""
🎯 EXPANDED PROXY SCRAPER

This scraper covers 29+ RELIABLE sources:
• Direct API endpoints (ProxyScrape, GeoNode)
• GitHub data.txt files (Proxifly + 14 countries)
• Fresh Proxy Lists (vakhov repository)
• Simple JSON/text responses

No complex scraping needed - just fast, reliable proxy collection from around the world!
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #E8F5E8;
                border: 1px solid #27AE60;
                border-radius: 8px;
                padding: 15px;
                color: #1B5E20;
                font-size: 11px;
                line-height: 1.4;
            }
        """)
        info_label.setWordWrap(True)
        config_layout.addWidget(info_label)
        
        # Custom Source Management Section
        custom_sources_group = QGroupBox("🌐 Custom Source Manager")
        custom_sources_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498DB;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        custom_sources_layout = QVBoxLayout()
        
        # Add custom source input
        add_source_layout = QHBoxLayout()
        self.custom_source_input = QLineEdit()
        self.custom_source_input.setPlaceholderText("https://example.com/proxy-list")
        self.custom_source_input.setMinimumHeight(30)
        self.add_source_button = QPushButton("🔍 Test & Add")
        self.add_source_button.setMinimumHeight(30)
        self.add_source_button.clicked.connect(self.add_custom_source)
        add_source_layout.addWidget(QLabel("🆕 New Source:"))
        add_source_layout.addWidget(self.custom_source_input)
        add_source_layout.addWidget(self.add_source_button)
        custom_sources_layout.addLayout(add_source_layout)
        
        # Custom sources list
        self.custom_sources_list = QListWidget()
        self.custom_sources_list.setMaximumHeight(120)
        custom_sources_layout.addWidget(QLabel("📋 Custom Sources:"))
        custom_sources_layout.addWidget(self.custom_sources_list)
        
        # Source management buttons
        source_buttons_layout = QHBoxLayout()
        self.test_source_button = QPushButton("🧪 Test")
        self.remove_source_button = QPushButton("🗑️ Remove")
        self.test_source_button.clicked.connect(self.test_selected_source)
        self.remove_source_button.clicked.connect(self.remove_selected_source)
        source_buttons_layout.addWidget(self.test_source_button)
        source_buttons_layout.addWidget(self.remove_source_button)
        custom_sources_layout.addLayout(source_buttons_layout)
        
        custom_sources_group.setLayout(custom_sources_layout)
        config_layout.addWidget(custom_sources_group)
        
        # Load existing custom sources
        self._load_custom_sources()
        
        # Options with better spacing
        options_group = QGroupBox("⚙️ Simple Options")
        options_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #27AE60;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        options_layout = QFormLayout()
        options_layout.setSpacing(10)
        
        self.max_proxies_spin = QSpinBox()
        self.max_proxies_spin.setRange(10, 50000)
        self.max_proxies_spin.setValue(5000)
        self.max_proxies_spin.setMinimumHeight(30)
        options_layout.addRow("Max Proxies:", self.max_proxies_spin)
        
        options_group.setLayout(options_layout)
        config_layout.addWidget(options_group)
        
        # Buttons with better styling and spacing
        self.start_button = QPushButton("🚀 Start Expanded Scraping (29+ Sources)")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #2E8B57;
                color: white;
                font-weight: bold;
                font-size: 13px;
                padding: 15px;
                border-radius: 8px;
                border: none;
            }
            QPushButton:hover {
                background-color: #3CB371;
            }
            QPushButton:pressed { background-color: #228B22; }
            QPushButton:disabled { background-color: #BDC3C7; }
        """)
        self.start_button.setMinimumHeight(50)
        self.start_button.clicked.connect(self.start_scraping)
        config_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("⏹️ Stop")
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                font-weight: bold;
                font-size: 13px;
                padding: 12px;
                border-radius: 8px;
                border: none;
            }
            QPushButton:hover { background-color: #C0392B; }
            QPushButton:disabled { background-color: #BDC3C7; }
        """)
        self.stop_button.setMinimumHeight(40)
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_scraping)
        config_layout.addWidget(self.stop_button)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimumHeight(25)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #BDC3C7;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #27AE60;
                border-radius: 6px;
            }
        """)
        self.progress_bar.setVisible(False)
        config_layout.addWidget(self.progress_bar)
        
        # Export buttons with better layout
        export_group = QGroupBox("💾 Export Options")
        export_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498DB;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        export_layout = QVBoxLayout()
        export_layout.setSpacing(8)
        
        self.export_json_btn = QPushButton("💾 Save JSON")
        self.export_json_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover { background-color: #2980B9; }
            QPushButton:disabled { background-color: #BDC3C7; }
        """)
        self.export_json_btn.setMinimumHeight(35)
        self.export_json_btn.clicked.connect(self.export_json)
        self.export_json_btn.setEnabled(False)
        export_layout.addWidget(self.export_json_btn)
        
        self.export_txt_btn = QPushButton("📄 Save TXT")
        self.export_txt_btn.setStyleSheet("""
            QPushButton {
                background-color: #9B59B6;
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover { background-color: #8E44AD; }
            QPushButton:disabled { background-color: #BDC3C7; }
        """)
        self.export_txt_btn.setMinimumHeight(35)
        self.export_txt_btn.clicked.connect(self.export_txt)
        self.export_txt_btn.setEnabled(False)
        export_layout.addWidget(self.export_txt_btn)
        
        export_group.setLayout(export_layout)
        config_layout.addWidget(export_group)
        
        # Add stretch to push everything to the top
        config_layout.addStretch()
        
        splitter.addWidget(config_widget)
        
    def _setup_results_panel(self, splitter):
        """Setup results panel"""
        results_widget = QWidget()
        results_layout = QVBoxLayout(results_widget)
        results_layout.setContentsMargins(10, 10, 10, 10)
        results_layout.setSpacing(10)
        
        # Results header with better styling
        header_layout = QHBoxLayout()
        results_label = QLabel("📊 Found Proxies")
        results_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2E8B57;")
        header_layout.addWidget(results_label)
        
        self.proxy_count_label = QLabel("0 proxies")
        self.proxy_count_label.setStyleSheet("""
            font-weight: bold; 
            color: #E67E22; 
            font-size: 14px;
            background-color: #FEF9E7;
            padding: 5px 10px;
            border-radius: 12px;
            border: 1px solid #F39C12;
        """)
        header_layout.addWidget(self.proxy_count_label)
        header_layout.addStretch()
        
        self.clear_button = QPushButton("🗑️ Clear")
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #E67E22;
                color: white;
                font-weight: bold;
                padding: 8px 15px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover { background-color: #D35400; }
            QPushButton:disabled { background-color: #BDC3C7; }
        """)
        self.clear_button.clicked.connect(self.clear_results)
        self.clear_button.setEnabled(False)
        header_layout.addWidget(self.clear_button)
        
        results_layout.addLayout(header_layout)
        
        # Proxy table with much better fullscreen support
        self.proxy_table = QTableWidget()
        self.proxy_table.setColumnCount(6)
        self.proxy_table.setHorizontalHeaderLabels([
            "IP Address", "Port", "Country", "Protocol", "Anonymity", "Source"
        ])
        
        # Enhanced table styling for fullscreen
        self.proxy_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #BDC3C7;
                background-color: white;
                alternate-background-color: #F8F9FA;
                selection-background-color: #3498DB;
                border: 1px solid #BDC3C7;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ECF0F1;
            }
            QTableWidget::item:selected {
                background-color: #3498DB;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495E;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        self.proxy_table.setAlternatingRowColors(True)
        self.proxy_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.proxy_table.setSortingEnabled(True)
        
        header = self.proxy_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        # Better responsive column sizing for fullscreen
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # IP - Allow resize
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Port - Fit content
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # Country - Allow resize
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Protocol - Fit content
        header.setSectionResizeMode(4, QHeaderView.Interactive)  # Anonymity - Allow resize
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # Source - Stretch to fill
        
        # Set minimum column widths for better fullscreen experience
        header.resizeSection(0, 140)  # IP
        header.resizeSection(1, 60)   # Port
        header.resizeSection(2, 100)  # Country
        header.resizeSection(3, 80)   # Protocol
        header.resizeSection(4, 100)  # Anonymity
        # Source column will stretch automatically
        
        # Enable better table navigation
        self.proxy_table.setTabKeyNavigation(True)
        
        results_layout.addWidget(self.proxy_table)
        splitter.addWidget(results_widget)
        
    @try_except_with_dialog
    def start_scraping(self, checked=False):
        """Start the simple scraping process"""
        # Clear previous results
        self.scraped_proxies = []
        self.proxy_table.setRowCount(0)
        self.update_proxy_count()
        
        # Create and start worker
        self.scraper_worker = SimpleProxyScraperWorker(self.max_proxies_spin.value())
        
        # Connect signals
        self.scraper_worker.progress_updated.connect(self.on_progress_updated)
        self.scraper_worker.proxy_found.connect(self.on_proxy_found)
        self.scraper_worker.scraping_completed.connect(self.on_scraping_completed)
        self.scraper_worker.error_occurred.connect(self.on_error)
        
        # Update UI
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # Start worker
        self.scraper_worker.start()
        
    def stop_scraping(self, checked=False):
        """Stop scraping"""
        if self.scraper_worker:
            self.scraper_worker.stop()
            
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("Scraping stopped by user")
    
    @pyqtSlot(int, str)
    def on_progress_updated(self, value, message):
        """Handle progress updates"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        
    @pyqtSlot(dict)
    def on_proxy_found(self, proxy):
        """Handle new proxy found"""
        self.scraped_proxies.append(proxy)
        self.add_proxy_to_table(proxy)
        self.update_proxy_count()
        
    @pyqtSlot(list)
    def on_scraping_completed(self, proxies):
        """Handle scraping completion"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.clear_button.setEnabled(True)
        self.export_json_btn.setEnabled(True)
        self.export_txt_btn.setEnabled(True)
        
        # Show completion message
        total_proxies = len(proxies)
        
        # Count protocols
        protocols = {}
        for proxy in proxies:
            protocol = proxy.get('protocol', 'Unknown')
            protocols[protocol] = protocols.get(protocol, 0) + 1
        
        protocol_summary = ", ".join([f"{protocol}: {count}" for protocol, count in protocols.items()])
        
        completion_msg = f"✅ Found {total_proxies} proxies! ({protocol_summary})"
        self.status_label.setText(completion_msg)
        
        QMessageBox.information(self, "Scraping Complete", 
                              f"Successfully found {total_proxies} proxies!\n\n"
                              f"Protocol breakdown:\n{protocol_summary}")
        
    @pyqtSlot(str)
    def on_error(self, error_message):
        """Handle errors"""
        self.status_label.setText(f"❌ Error: {error_message}")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        QMessageBox.critical(self, "Scraping Error", f"An error occurred:\n{error_message}")
        
    def add_proxy_to_table(self, proxy):
        """Add proxy to the results table"""
        try:
            row_position = self.proxy_table.rowCount()
            self.proxy_table.insertRow(row_position)
            
            # Add proxy data to table
            self.proxy_table.setItem(row_position, 0, QTableWidgetItem(proxy.get('ip', '')))
            self.proxy_table.setItem(row_position, 1, QTableWidgetItem(str(proxy.get('port', ''))))
            self.proxy_table.setItem(row_position, 2, QTableWidgetItem(proxy.get('country', 'Unknown')))
            self.proxy_table.setItem(row_position, 3, QTableWidgetItem(proxy.get('protocol', 'HTTP')))
            self.proxy_table.setItem(row_position, 4, QTableWidgetItem(proxy.get('anonymity', 'Unknown')))
            self.proxy_table.setItem(row_position, 5, QTableWidgetItem(proxy.get('source', '')))
            
            # Auto-scroll to show latest
            self.proxy_table.scrollToBottom()
            
        except Exception as e:
            self.logger.error(f"Error adding proxy to table: {str(e)}")
    
    def update_proxy_count(self):
        """Update proxy count display"""
        count = len(self.scraped_proxies)
        self.proxy_count_label.setText(f"{count} proxies")
        
    def clear_results(self, checked=False):
        """Clear all results"""
        self.scraped_proxies = []
        self.proxy_table.setRowCount(0)
        self.update_proxy_count()
        self.clear_button.setEnabled(False)
        self.export_json_btn.setEnabled(False)
        self.export_txt_btn.setEnabled(False)
        self.status_label.setText("Results cleared")
        
    @try_except_with_dialog
    def export_json(self, checked=False):
        """Export proxies as JSON"""
        if not self.scraped_proxies:
            QMessageBox.information(self, "No Data", "No proxies to export.")
            return
            
        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Proxies as JSON", 
            f"proxies_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json)"
        )
        
        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(self.scraped_proxies, f, indent=2)
                QMessageBox.information(self, "Export Successful", 
                                      f"Exported {len(self.scraped_proxies)} proxies to:\n{filename}")
            except Exception as e:
                QMessageBox.critical(self, "Export Failed", f"Failed to export: {str(e)}")
            
    @try_except_with_dialog
    def export_txt(self, checked=False):
        """Export proxies as plain text"""
        if not self.scraped_proxies:
            QMessageBox.information(self, "No Data", "No proxies to export.")
            return
            
        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Proxies as TXT", 
            f"proxies_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt)"
        )
        
        if filename:
            try:
                with open(filename, 'w') as f:
                    for proxy in self.scraped_proxies:
                        f.write(f"{proxy['ip']}:{proxy['port']}\n")
                QMessageBox.information(self, "Export Successful", 
                                      f"Exported {len(self.scraped_proxies)} proxies to:\n{filename}")
            except Exception as e:
                QMessageBox.critical(self, "Export Failed", f"Failed to export: {str(e)}")

    def cleanup(self):
        """Cleanup resources"""
        if self.scraper_worker:
            self.scraper_worker.stop()
            self.scraper_worker.wait(3000)  # Wait up to 3 seconds
    
    def _load_custom_sources(self):
        """Load custom sources from config"""
        try:
            config_path = os.path.join(os.path.expanduser("~"), ".supercrawler", "custom_proxy_sources.json")
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    sources = json.load(f)
                    for source in sources:
                        self.custom_sources_list.addItem(f"✅ {source}")
        except Exception as e:
            self.logger.error(f"Error loading custom sources: {e}")
    
    def _save_custom_sources(self):
        """Save custom sources to config"""
        try:
            sources = []
            for i in range(self.custom_sources_list.count()):
                item_text = self.custom_sources_list.item(i).text()
                # Extract URL from the item (remove status emojis)
                url = item_text.split(' ', 1)[-1] if ' ' in item_text else item_text
                sources.append(url)
            
            config_dir = os.path.join(os.path.expanduser("~"), ".supercrawler")
            os.makedirs(config_dir, exist_ok=True)
            config_path = os.path.join(config_dir, "custom_proxy_sources.json")
            
            with open(config_path, 'w') as f:
                json.dump(sources, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving custom sources: {e}")
    
    def add_custom_source(self):
        """Add and test a new custom proxy source"""
        url = self.custom_source_input.text().strip()
        
        if not url:
            QMessageBox.warning(self, "Invalid URL", "Please enter a valid URL.")
            return
            
        if not url.startswith(("http://", "https://")):
            url = "https://" + url  # Auto-add https if missing
        
        # Check if source already exists
        for i in range(self.custom_sources_list.count()):
            item_text = self.custom_sources_list.item(i).text()
            if url in item_text:
                QMessageBox.information(self, "Source Exists", "This source is already in the list.")
                return
        
        try:
            # Show testing dialog
            progress_dialog = QProgressDialog("Testing custom source...", "Cancel", 0, 100, self)
            progress_dialog.setWindowTitle("🔍 Source Testing")
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.show()
            
            # Test the URL comprehensively
            self.status_label.setText(f"🔍 Testing custom source: {url}")
            progress_dialog.setValue(20)
            
            session = requests.Session()
            session.headers.update({
                'User-Agent': random.choice([
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ])
            })
            
            progress_dialog.setValue(40)
            response = session.get(url, timeout=15)
            
            progress_dialog.setValue(60)
            
            if response.status_code == 200:
                # Advanced content analysis
                content = response.text.lower()
                proxy_indicators = ["proxy", "ip", "port", "socks", "http", "anonymous", "elite", "transparent"]
                ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
                port_pattern = r':(\d{2,5})\b'
                
                progress_dialog.setValue(80)
                
                # Check for proxy content
                proxy_score = 0
                for indicator in proxy_indicators:
                    if indicator in content:
                        proxy_score += 1
                
                # Check for IP addresses and ports
                ips_found = len(re.findall(ip_pattern, content))
                ports_found = len(re.findall(port_pattern, content))
                
                progress_dialog.setValue(100)
                progress_dialog.close()
                
                # Determine if this looks like a proxy source
                if proxy_score >= 2 and ips_found >= 5 and ports_found >= 5:
                    status = "🟢 Excellent"
                    score_text = f"High confidence proxy source (Score: {proxy_score}, IPs: {ips_found}, Ports: {ports_found})"
                    auto_add = True
                elif proxy_score >= 1 and ips_found >= 2:
                    status = "🟡 Good"
                    score_text = f"Possible proxy source (Score: {proxy_score}, IPs: {ips_found}, Ports: {ports_found})"
                    auto_add = False
                else:
                    status = "🔴 Poor"
                    score_text = f"May not be a proxy source (Score: {proxy_score}, IPs: {ips_found}, Ports: {ports_found})"
                    auto_add = False
                
                # Ask user if they want to add it (unless it's excellent)
                if auto_add:
                    reply = QMessageBox.Yes
                else:
                    reply = QMessageBox.question(
                        self, "Source Test Results",
                        f"URL: {url}\n\nTest Results:\nStatus: {status}\nAnalysis: {score_text}\n\nDo you want to add this source?",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.Yes if proxy_score >= 2 else QMessageBox.No
                    )
                
                if reply == QMessageBox.Yes:
                    # Add to list with test results
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
                    item_text = f"✅ {url} - Tested: {current_time} ({status})"
                    self.custom_sources_list.addItem(item_text)
                    
                    # Clear input
                    self.custom_source_input.clear()
                    
                    # Save to config
                    self._save_custom_sources()
                    
                    self.status_label.setText(f"✅ Custom source added successfully")
                    QTimer.singleShot(5000, lambda: self.status_label.setText("Ready to scrape proxies"))
                    
                    QMessageBox.information(self, "Source Added", f"Successfully added: {url}\n\nStatus: {status}\nAnalysis: {score_text}")
                    self.logger.info(f"Added custom proxy source: {url} (Status: {status})")
                else:
                    self.status_label.setText("Source not added")
                    QTimer.singleShot(3000, lambda: self.status_label.setText("Ready to scrape proxies"))
            else:
                progress_dialog.close()
                QMessageBox.warning(self, "Source Test Failed", f"URL returned HTTP status {response.status_code}\n\nYou can still add it manually if you believe it's a valid proxy source.")
                
                reply = QMessageBox.question(
                    self, "Add Anyway?",
                    f"Test failed, but do you want to add this source anyway?\n\nURL: {url}\nStatus: HTTP {response.status_code}",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    item_text = f"❌ {url} - Status: HTTP {response.status_code}"
                    self.custom_sources_list.addItem(item_text)
                    self.custom_source_input.clear()
                    self._save_custom_sources()
                
        except Exception as e:
            if 'progress_dialog' in locals():
                progress_dialog.close()
            QMessageBox.warning(self, "Error", f"Failed to test source: {str(e)}\n\nYou can still add it manually if you believe it's a valid proxy source.")
            
            reply = QMessageBox.question(
                self, "Add Anyway?",
                f"Test failed with error, but do you want to add this source anyway?\n\nURL: {url}\nError: {str(e)}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                item_text = f"❓ {url} - Error during test"
                self.custom_sources_list.addItem(item_text)
                self.custom_source_input.clear()
                self._save_custom_sources()
                
            self.status_label.setText("❌ Failed to test custom source")
            QTimer.singleShot(3000, lambda: self.status_label.setText("Ready to scrape proxies"))
    
    def test_selected_source(self):
        """Test the selected custom source"""
        current_item = self.custom_sources_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "No Selection", "Please select a source to test.")
            return
        
        item_text = current_item.text()
        # Extract URL from item text
        url = item_text.split(' ', 1)[-1] if ' ' in item_text else item_text
        url = url.split(' - ')[0] if ' - ' in url else url
        
        try:
            self.status_label.setText(f"🧪 Testing source: {url}")
            
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })
            
            response = session.get(url, timeout=10)
            
            if response.status_code == 200:
                content = response.text.lower()
                proxy_indicators = ["proxy", "ip", "port", "socks", "http", "anonymous"]
                
                if any(indicator in content for indicator in proxy_indicators):
                    status = "✅"
                    result_msg = "Source is working and contains proxy data"
                else:
                    status = "❓"
                    result_msg = "Source is accessible but proxy content unclear"
            else:
                status = "❌"
                result_msg = f"Source returned HTTP {response.status_code}"
            
            # Update the list item
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
            new_text = f"{status} {url} - Last tested: {current_time}"
            current_item.setText(new_text)
            
            self._save_custom_sources()
            self.status_label.setText(result_msg)
            QTimer.singleShot(5000, lambda: self.status_label.setText("Ready to scrape proxies"))
            
            QMessageBox.information(self, "Test Result", f"{url}\n\nStatus: {status}\nResult: {result_msg}")
            
        except Exception as e:
            QMessageBox.warning(self, "Test Failed", f"Failed to test source: {str(e)}")
            self.status_label.setText("Test failed")
            QTimer.singleShot(3000, lambda: self.status_label.setText("Ready to scrape proxies"))
    
    def remove_selected_source(self):
        """Remove the selected custom source"""
        current_item = self.custom_sources_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "No Selection", "Please select a source to remove.")
            return
        
        item_text = current_item.text()
        
        reply = QMessageBox.question(
            self, "Confirm Removal", 
            f"Are you sure you want to remove this source?\n\n{item_text}",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            row = self.custom_sources_list.row(current_item)
            self.custom_sources_list.takeItem(row)
            self._save_custom_sources()
            
            self.status_label.setText("Custom source removed")
            QTimer.singleShot(3000, lambda: self.status_label.setText("Ready to scrape proxies")) 