from PyQt5.QtWidgets import (Q<PERSON>ain<PERSON><PERSON>ow, QTabWidget, QVBoxLayout,
                            QWidget, QStatusBar, QAction, QMessageBox)
from PyQt5.QtCore import Qt, QTimer
from intellicrawler.ui.crawler_tab import IntelligentCrawlerTab as CrawlerTab
from intellicrawler.ui.analysis_tab import AnalysisTab
from intellicrawler.ui.settings_tab import SettingsTab
from intellicrawler.utils.logger import get_logger
from intellicrawler.ui.error_report_tab import <PERSON>rrorReportTab
from intellicrawler.ui.proxy_scraper_tab import ProxyScraperTab
from intellicrawler.ui.music_player_tab import MusicPlayerTab
from intellicrawler.ui.ai_chat_tab import AIChatTab
from intellicrawler.utils.error_handler import try_except_with_dialog

class MainWindow(QMainWindow):
    """Main application window with tab-based interface"""
    
    def __init__(self, config):
        super().__init__()
        self.logger = get_logger()
        self.config = config
        
        # Initialize AI integration
        from intellicrawler.ai_integration import DeepSeekAI
        self.ai_integration = DeepSeekAI()
        
        # Set window properties
        self.setWindowTitle("🕷️ IntelliCrawler - Web Scraping & AI Analysis [Enhanced Version]")
        self.setMinimumSize(1200, 800)
        
        # Enable fullscreen and maximize support
        self.setWindowState(Qt.WindowMaximized)
        self.setAttribute(Qt.WA_DeleteOnClose, True)
        
        # Setup central widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.main_layout.setSpacing(5)
        
        # Create tab widget with better styling
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #BDC3C7;
                border-radius: 4px;
                background-color: white;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #ECF0F1;
                border: 1px solid #BDC3C7;
                border-bottom-color: #BDC3C7;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                min-width: 120px;
                padding: 8px 12px;
                margin-right: 2px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498DB;
                color: white;
                border-bottom-color: #3498DB;
            }
            QTabBar::tab:hover:!selected {
                background-color: #D5DBDB;
            }
        """)
        
        # Create individual tabs
        self.crawler_tab = CrawlerTab(self)
        self.analysis_tab = AnalysisTab(self)
        self.proxy_scraper_tab = ProxyScraperTab(self)
        self.music_player_tab = MusicPlayerTab(self)
        self.ai_chat_tab = AIChatTab(self)
        self.settings_tab = SettingsTab(self)
        self.error_report_tab = ErrorReportTab(self)
        
        # Connect crawler to analysis tab
        if hasattr(self.crawler_tab, 'crawl_completed') and hasattr(self.analysis_tab, 'set_crawled_data'):
            self.crawler_tab.crawl_completed.connect(self.analysis_tab.set_crawled_data)
        
        # Connect settings signals
        if hasattr(self.settings_tab, 'save_button'):
            self.settings_tab.save_button.clicked.connect(self.refresh_settings)
        
        # Add tabs to tab widget
        self.tabs.addTab(self.crawler_tab, "🕷️ Web Crawler")
        self.tabs.addTab(self.analysis_tab, "🤖 AI Analysis")
        self.tabs.addTab(self.ai_chat_tab, "💬 AI Chat")
        self.tabs.addTab(self.proxy_scraper_tab, "🔍 Proxy Scraper")
        self.tabs.addTab(self.music_player_tab, "🎵 Music Player")
        self.tabs.addTab(self.settings_tab, "⚙️ Settings")
        self.tabs.addTab(self.error_report_tab, "🐛 Error Reports")
        
        # Status bar with better styling
        self.status_bar = QStatusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #34495E;
                color: white;
                border: none;
                font-weight: bold;
                padding: 5px;
            }
            QStatusBar::item {
                border: none;
            }
        """)
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - Enhanced SuperCrawler")
        
        # Add tab widget to main layout
        self.main_layout.addWidget(self.tabs)
        
        # Setup menu bar
        self.setup_menu()
        
        # Check for API key
        self.check_api_key()
        
        self.logger.info("MainWindow initialized with enhanced features")
    
    def get_ai_instance(self):
        """Get the AI integration instance for other components"""
        return self.ai_integration
    
    def check_api_key(self):
        """Check if API key is configured and warn user if not"""
        api_key = self.config.get("deepseek_api_key", "")
        if not api_key:
            QMessageBox.warning(
                self, 
                "API Key Not Configured",
                "No DeepSeek API key is configured. AI features will not work without an API key.\n\n"
                "Please go to the Settings tab to configure your API key.",
                QMessageBox.Ok
            )
    
    def refresh_settings(self):
        """Refresh all components with the latest settings"""
        try:
            # Reload config
            from supercrawler.utils.config import load_config
            self.config = load_config()
            
            # Update AI integration with latest API key
            api_key = self.config.get("deepseek_api_key", "")
            self.logger.info(f"Refreshing settings with API key (length: {len(api_key) if api_key else 0})")
            
            # Update our AI integration instance
            if hasattr(self, 'ai_integration'):
                self.ai_integration.set_api_key(api_key)
                self.logger.info("Updated main window AI integration instance")
            
            # Refresh analysis tab settings
            if hasattr(self.analysis_tab, 'refresh_settings'):
                self.analysis_tab.refresh_settings()
            
            # Update status
            self.status_bar.showMessage("Settings updated", 3000)
            self.logger.info("Settings refreshed throughout application")
        except Exception as e:
            self.logger.error(f"Error refreshing settings: {str(e)}")
            self.status_bar.showMessage(f"Error updating settings: {str(e)}", 5000)
    
    def setup_menu(self):
        """Create application menu"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        # New Analysis
        new_analysis_action = QAction("New Analysis", self)
        new_analysis_action.setShortcut("Ctrl+N")
        new_analysis_action.triggered.connect(self.new_analysis)
        file_menu.addAction(new_analysis_action)
        
        file_menu.addSeparator()
        
        # Exit
        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("&Tools")
        
        # Clear Results
        clear_action = QAction("Clear Analysis Results", self)
        clear_action.triggered.connect(self.clear_analysis_results)
        tools_menu.addAction(clear_action)
        
        # Quick Summary
        quick_summary_action = QAction("Quick Summary", self)
        quick_summary_action.setShortcut("Ctrl+S")
        quick_summary_action.triggered.connect(self.quick_summary)
        tools_menu.addAction(quick_summary_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        # About
        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def new_analysis(self):
        """Start a new analysis"""
        if hasattr(self.analysis_tab, 'clear_results'):
            self.analysis_tab.clear_results()
        self.tabs.setCurrentIndex(1)  # Switch to analysis tab
        
    def clear_analysis_results(self):
        """Clear analysis results"""
        if hasattr(self.analysis_tab, 'clear_results'):
            self.analysis_tab.clear_results()
            
    def quick_summary(self):
        """Trigger quick summary"""
        if hasattr(self.analysis_tab, 'quick_summary'):
            self.tabs.setCurrentIndex(1)  # Switch to analysis tab
            self.analysis_tab.quick_summary()
    
    def update_status(self, message):
        """Update status bar with message"""
        self.status_bar.showMessage(message)
    
    def show_error(self, error_message):
        """Display error message"""
        QMessageBox.critical(self, "Error", error_message)
        self.logger.error(error_message)
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About SuperCrawler",
                         "SuperCrawler v2.0 (Enhanced Version)\n\n"
                         "A powerful web scraping and AI analysis tool powered by DeepSeek AI.\n\n"
                         "Features:\n"
                         "• Enhanced summary generation\n"
                         "• Multiple output formats (Human & AI-readable)\n"
                         "• Advanced analysis options\n"
                         "• Improved user interface\n\n"
                         "© 2024 SuperCrawler Enhanced")
    
    def closeEvent(self, event):
        """Handle window close event with proper cleanup"""
        try:
            # Don't show confirmation dialog during active operations for quick exit
            active_operations = []
            
            # Check for active crawling in crawler tab
            if hasattr(self.crawler_tab, 'crawler') and self.crawler_tab.crawler:
                if hasattr(self.crawler_tab.crawler, 'isRunning') and self.crawler_tab.crawler.isRunning():
                    active_operations.append("Web Crawler")
            
            # Check for active scraping in proxy scraper tab
            if hasattr(self.proxy_scraper_tab, 'scraper_worker') and self.proxy_scraper_tab.scraper_worker:
                if hasattr(self.proxy_scraper_tab.scraper_worker, 'isRunning') and self.proxy_scraper_tab.scraper_worker.isRunning():
                    active_operations.append("Proxy Scraper")
            
            # Check for custom scraper worker
            if hasattr(self.proxy_scraper_tab, 'custom_worker') and self.proxy_scraper_tab.custom_worker:
                if hasattr(self.proxy_scraper_tab.custom_worker, 'isRunning') and self.proxy_scraper_tab.custom_worker.isRunning():
                    active_operations.append("Custom Website Scraper")
            
            # Check for analysis worker
            if hasattr(self.analysis_tab, 'analysis_worker') and self.analysis_tab.analysis_worker:
                if hasattr(self.analysis_tab.analysis_worker, 'isRunning') and self.analysis_tab.analysis_worker.isRunning():
                    active_operations.append("AI Analysis")
            
            # If there are active operations, warn the user
            if active_operations:
                reply = QMessageBox.question(
                    self, 
                    "Active Operations Detected",
                    f"The following operations are still running:\n• {chr(10).join(active_operations)}\n\n"
                    "Closing now will stop these operations.\n"
                    "Are you sure you want to exit?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
            else:
                reply = QMessageBox.question(
                    self, 
                    "Exit Confirmation",
                    "Are you sure you want to exit?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
            
            if reply == QMessageBox.Yes:
                self.logger.info("Application closing - performing enhanced cleanup...")
                
                # Phase 1: Stop all active operations gracefully
                shutdown_successful = True
                try:
                    # Stop crawler with enhanced termination
                    if hasattr(self.crawler_tab, 'crawler') and self.crawler_tab.crawler:
                        if hasattr(self.crawler_tab.crawler, 'isRunning') and self.crawler_tab.crawler.isRunning():
                            self.logger.info("Stopping crawler thread...")
                            if hasattr(self.crawler_tab.crawler, 'stop'):
                                self.crawler_tab.crawler.stop()
                            
                            # Wait for graceful shutdown with timeout
                            if hasattr(self.crawler_tab.crawler, 'wait'):
                                if not self.crawler_tab.crawler.wait(5000):  # 5 second timeout
                                    self.logger.warning("Crawler thread did not stop gracefully, forcing termination")
                                    if hasattr(self.crawler_tab.crawler, 'terminate'):
                                        self.crawler_tab.crawler.terminate()
                                    shutdown_successful = False
                    
                    # Stop proxy scraper worker with enhanced cleanup
                    if hasattr(self.proxy_scraper_tab, 'scraper_worker') and self.proxy_scraper_tab.scraper_worker:
                        if hasattr(self.proxy_scraper_tab.scraper_worker, 'isRunning') and self.proxy_scraper_tab.scraper_worker.isRunning():
                            self.logger.info("Stopping proxy scraper worker...")
                            
                            # Set stop flag first
                            if hasattr(self.proxy_scraper_tab.scraper_worker, 'stop'):
                                self.proxy_scraper_tab.scraper_worker.stop()
                            
                            # Force cleanup of sessions and resources
                            if hasattr(self.proxy_scraper_tab.scraper_worker, 'cleanup'):
                                self.proxy_scraper_tab.scraper_worker.cleanup()
                            
                            # Wait for graceful shutdown
                            if hasattr(self.proxy_scraper_tab.scraper_worker, 'wait'):
                                if not self.proxy_scraper_tab.scraper_worker.wait(5000):
                                    self.logger.warning("Proxy scraper worker did not stop gracefully")
                                    if hasattr(self.proxy_scraper_tab.scraper_worker, 'terminate'):
                                        self.proxy_scraper_tab.scraper_worker.terminate()
                                    shutdown_successful = False
                    
                    # Stop custom scraper worker
                    if hasattr(self.proxy_scraper_tab, 'custom_worker') and self.proxy_scraper_tab.custom_worker:
                        if hasattr(self.proxy_scraper_tab.custom_worker, 'isRunning') and self.proxy_scraper_tab.custom_worker.isRunning():
                            self.logger.info("Stopping custom scraper worker...")
                            if hasattr(self.proxy_scraper_tab.custom_worker, 'stop'):
                                self.proxy_scraper_tab.custom_worker.stop()
                            
                            if hasattr(self.proxy_scraper_tab.custom_worker, 'wait'):
                                if not self.proxy_scraper_tab.custom_worker.wait(5000):
                                    self.logger.warning("Custom scraper worker did not stop gracefully")
                                    if hasattr(self.proxy_scraper_tab.custom_worker, 'terminate'):
                                        self.proxy_scraper_tab.custom_worker.terminate()
                                    shutdown_successful = False
                    
                    # Stop analysis worker
                    if hasattr(self.analysis_tab, 'analysis_worker') and self.analysis_tab.analysis_worker:
                        if hasattr(self.analysis_tab.analysis_worker, 'isRunning') and self.analysis_tab.analysis_worker.isRunning():
                            self.logger.info("Stopping analysis worker...")
                            if hasattr(self.analysis_tab.analysis_worker, 'stop'):
                                self.analysis_tab.analysis_worker.stop()
                            
                            if hasattr(self.analysis_tab.analysis_worker, 'wait'):
                                if not self.analysis_tab.analysis_worker.wait(5000):
                                    self.logger.warning("Analysis worker did not stop gracefully")
                                    if hasattr(self.analysis_tab.analysis_worker, 'terminate'):
                                        self.analysis_tab.analysis_worker.terminate()
                                    shutdown_successful = False
                    
                    # Phase 2: Cleanup resources
                    self.logger.info("Cleaning up application resources...")
                    
                    # Cleanup any selenium instances
                    if hasattr(self.proxy_scraper_tab, 'cleanup_selenium'):
                        self.proxy_scraper_tab.cleanup_selenium()
                    
                    # Cleanup any remaining ThreadPoolExecutors
                    import threading
                    
                    # Force cleanup any hanging ThreadPoolExecutors
                    active_threads = threading.active_count()
                    if active_threads > 1:  # Main thread + potential hangers
                        self.logger.warning(f"Still have {active_threads} active threads, forcing cleanup...")
                        
                        # Try to identify and cleanup any ThreadPoolExecutors
                        for thread in threading.enumerate():
                            if thread != threading.current_thread() and thread.is_alive():
                                self.logger.warning(f"Found hanging thread: {thread.name}")
                                # Note: We can't force kill threads in Python, but we can try to signal them
                    
                    # Phase 3: Final cleanup and status
                    if shutdown_successful:
                        self.logger.info("Application cleanup completed successfully")
                    else:
                        self.logger.warning("Application cleanup completed with warnings - some threads may have been force-terminated")
                    
                except Exception as cleanup_error:
                    self.logger.error(f"Error during cleanup: {str(cleanup_error)}")
                    shutdown_successful = False
                
                # Phase 4: Emergency force close if needed
                if not shutdown_successful:
                    self.logger.warning("Performing emergency shutdown due to hanging threads...")
                    # Import os for emergency exit
                    import os
                    
                    # Try to force close any remaining processes
                    try:
                        # Get current process ID
                        current_pid = os.getpid()
                        self.logger.info(f"Current process PID: {current_pid}")
                        
                        # Set a timer for emergency exit
                        emergency_timer = QTimer()
                        emergency_timer.setSingleShot(True)
                        emergency_timer.timeout.connect(lambda: os._exit(0))  # Force exit after 3 seconds
                        emergency_timer.start(3000)
                        
                    except Exception as e:
                        self.logger.error(f"Emergency shutdown error: {str(e)}")
                        # Ultimate fallback
                        os._exit(0)
                
                event.accept()
            else:
                event.ignore()
                
        except Exception as e:
            self.logger.error(f"Critical error in closeEvent: {str(e)}")
            # If there's a critical error, force close to prevent hanging
            import os
            self.logger.error("Performing emergency exit due to critical error in closeEvent")
            os._exit(1)
            
    @try_except_with_dialog
    def start_crawling_with_params(self, url, max_pages, depth, dynamic, chrome_path=None):
        """Start crawling with parameters provided from command line
        
        Args:
            url (str): URL to crawl
            max_pages (int): Maximum number of pages to crawl
            depth (int): Crawl depth
            dynamic (bool): Whether to use dynamic crawling
            chrome_path (str, optional): Path to Chrome browser executable
        """
        self.logger.info(f"Starting crawling with params: URL={url}, max_pages={max_pages}, depth={depth}, dynamic={dynamic}")
        
        # Switch to crawler tab
        self.tabs.setCurrentIndex(0)
        
        # Pass parameters to crawler tab if it supports it
        if hasattr(self.crawler_tab, 'start_crawl_with_params'):
            self.crawler_tab.start_crawl_with_params(url, max_pages, depth, dynamic, chrome_path)
        else:
            self.logger.warning("Crawler tab does not support parameterized crawling")
            self.show_error("This version does not support command-line crawling parameters") 