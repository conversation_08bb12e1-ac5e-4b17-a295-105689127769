import json
from datetime import datetime
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                            QTextEdit, QLineEdit, QLabel, QGroupBox, QComboBox,
                            QSlider, QSpinBox, QCheckBox,
                            QMessageBox, QProgressBar, QSplitter, QTabWidget,
                            QFormLayout, QTextBrowser, QButtonGroup, QRadioButton)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QTextCursor

from supercrawler.utils.logger import get_logger, log_error
from supercrawler.utils.config import load_config


class DeepSeekChatMessage:
    """Represents a chat message with DeepSeek-specific features"""
    
    def __init__(self, role: str, content: str, reasoning_content: str = None,
                 tool_calls: list = None, name: str = None, prefix: bool = False):
        self.role = role  # system, user, assistant, tool
        self.content = content
        self.reasoning_content = reasoning_content  # For deepseek-reasoner model
        self.tool_calls = tool_calls or []
        self.name = name
        self.prefix = prefix  # For prefix completion
        self.timestamp = datetime.now()


class ChatStreamWorker(QThread):
    """Worker thread for handling streaming chat responses"""
    
    message_chunk = pyqtSignal(str)  # Streaming text chunks
    reasoning_chunk = pyqtSignal(str)  # Reasoning content chunks
    message_complete = pyqtSignal(dict)  # Complete message with metadata
    error_occurred = pyqtSignal(str)
    
    def __init__(self, client, messages, options):
        super().__init__()
        self.client = client
        self.messages = messages
        self.options = options
        self.is_running = True
        
    def run(self):
        logger = get_logger()
        logger.info("=== ChatStreamWorker Started ===")
        
        try:
            logger.info(f"Creating streaming request with model: {self.options.get('model', 'deepseek-chat')}")
            logger.info(f"Message count: {len(self.messages)}")
            logger.info(f"Options: {self.options}")
            
            response = self.client.chat.completions.create(
                model=self.options.get('model', 'deepseek-chat'),
                messages=self.messages,
                stream=True,
                temperature=self.options.get('temperature', 1.0),
                max_tokens=self.options.get('max_tokens', 4096),
                top_p=self.options.get('top_p', 1.0),
                frequency_penalty=self.options.get('frequency_penalty', 0.0),
                presence_penalty=self.options.get('presence_penalty', 0.0),
                stop=self.options.get('stop', None),
                stream_options={"include_usage": True} if self.options.get('include_usage', False) else None
            )
            
            logger.info("Streaming response created, processing chunks...")
            
            full_content = ""
            full_reasoning = ""
            usage_info = None
            chunk_count = 0
            
            for chunk in response:
                if not self.is_running:
                    logger.info("Stream worker stopped by user")
                    break
                
                chunk_count += 1
                if chunk_count % 10 == 0:  # Log every 10th chunk to avoid spam
                    logger.debug(f"Processed {chunk_count} chunks so far")
                    
                if hasattr(chunk, 'choices') and chunk.choices:
                    delta = chunk.choices[0].delta
                    
                    # Handle regular content
                    if hasattr(delta, 'content') and delta.content:
                        full_content += delta.content
                        self.message_chunk.emit(delta.content)
                    
                    # Handle reasoning content (deepseek-reasoner model)
                    if hasattr(delta, 'reasoning_content') and delta.reasoning_content:
                        full_reasoning += delta.reasoning_content
                        self.reasoning_chunk.emit(delta.reasoning_content)
                
                # Handle usage statistics (final chunk)
                if hasattr(chunk, 'usage') and chunk.usage:
                    logger.info(f"Received usage statistics: {chunk.usage}")
                    usage_info = {
                        'completion_tokens': chunk.usage.completion_tokens,
                        'prompt_tokens': chunk.usage.prompt_tokens,
                        'total_tokens': chunk.usage.total_tokens,
                        'prompt_cache_hit_tokens': getattr(chunk.usage, 'prompt_cache_hit_tokens', 0),
                        'prompt_cache_miss_tokens': getattr(chunk.usage, 'prompt_cache_miss_tokens', 0)
                    }
                    
                    # Check for reasoning tokens
                    if hasattr(chunk.usage, 'completion_tokens_details'):
                        details = chunk.usage.completion_tokens_details
                        if hasattr(details, 'reasoning_tokens'):
                            usage_info['reasoning_tokens'] = details.reasoning_tokens
            
            logger.info(f"Stream completed. Total chunks: {chunk_count}")
            logger.info(f"Content length: {len(full_content)}")
            logger.info(f"Reasoning length: {len(full_reasoning) if full_reasoning else 0}")
            
            # Emit complete message
            complete_message = {
                'content': full_content,
                'reasoning_content': full_reasoning if full_reasoning else None,
                'usage': usage_info
            }
            self.message_complete.emit(complete_message)
            logger.info("=== ChatStreamWorker Completed Successfully ===")
            
        except Exception as e:
            logger.error("=== ChatStreamWorker FAILED ===")
            logger.error(f"Streaming error: {str(e)}")
            log_error(e, "ChatStreamWorker failed")
            self.error_occurred.emit(str(e))
    
    def stop(self):
        self.is_running = False


class FunctionCallingWidget(QWidget):
    """Widget for managing function calling capabilities"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.functions = {}
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Function management
        function_group = QGroupBox("Available Functions")
        function_layout = QVBoxLayout(function_group)
        
        self.function_list = QTextEdit()
        self.function_list.setMaximumHeight(150)
        self.function_list.setPlainText("No functions defined")
        function_layout.addWidget(self.function_list)
        
        # Add sample functions
        sample_btn = QPushButton("Add Sample Functions")
        sample_btn.clicked.connect(self.add_sample_functions)
        function_layout.addWidget(sample_btn)
        
        layout.addWidget(function_group)
    
    def add_sample_functions(self):
        """Add sample functions for demonstration"""
        sample_functions = [
            {
                "type": "function",
                "function": {
                    "name": "get_current_weather",
                    "description": "Get the current weather for a location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "The city and state, e.g. San Francisco, CA"
                            },
                            "unit": {
                                "type": "string",
                                "enum": ["celsius", "fahrenheit"],
                                "description": "The temperature unit"
                            }
                        },
                        "required": ["location"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_web",
                    "description": "Search the web for information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "The search query"
                            },
                            "max_results": {
                                "type": "integer",
                                "description": "Maximum number of results",
                                "default": 5
                            }
                        },
                        "required": ["query"]
                    }
                }
            }
        ]
        
        self.functions = sample_functions
        function_text = json.dumps(sample_functions, indent=2)
        self.function_list.setPlainText(function_text)
    
    def get_functions(self):
        """Get the current function definitions"""
        return self.functions


class AIChatTab(QWidget):
    """Advanced AI Chat tab with full DeepSeek API integration"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.logger = get_logger()
        self.logger.info("=== AI Chat Tab Initialization Started ===")
        
        try:
            # Load configuration
            self.logger.info("Loading configuration...")
            self.config = load_config()
            if not self.config:
                self.logger.warning("No configuration found, using defaults")
                self.config = {}
            
            # DeepSeek API client
            self.client = None
            self.api_key = self.config.get("deepseek_api_key", "")
            self.logger.info(f"API key configured: {'Yes' if self.api_key else 'No'} (length: {len(self.api_key) if self.api_key else 0})")
            
            # Chat state
            self.messages = []
            self.current_model = "deepseek-chat"
            self.conversation_id = None
            self.context_cache_enabled = True
            
            # Threading
            self.stream_worker = None
            
            # Initialize UI components
            self.logger.info("Setting up UI components...")
            self.setup_ui()
            
            # Initialize API client
            self.logger.info("Setting up API client...")
            self.setup_api_client()
            
            # Auto-refresh settings (reduced frequency to avoid excessive logging)
            self.logger.info("Setting up settings auto-refresh timer...")
            self.settings_timer = QTimer()
            self.settings_timer.timeout.connect(self.refresh_settings)
            self.settings_timer.start(60000)  # Check every 60 seconds instead of 5
            
            self.logger.info("=== AI Chat Tab Initialization Completed Successfully ===")
            
        except Exception as e:
            self.logger.error("=== AI Chat Tab Initialization FAILED ===")
            self.logger.error(f"Error during initialization: {str(e)}")
            log_error(e, "AI Chat Tab initialization failed")
            # Still try to show a basic UI even if initialization fails
            try:
                self.setup_basic_ui()
            except Exception as ui_error:
                self.logger.error(f"Even basic UI setup failed: {str(ui_error)}")
                log_error(ui_error, "Basic UI setup failed")
    
    def setup_api_client(self):
        """Initialize the DeepSeek API client"""
        self.logger.info("--- Setting up DeepSeek API Client ---")
        
        try:
            if self.api_key:
                self.logger.info("API key found, attempting to initialize OpenAI client...")
                try:
                    from openai import OpenAI
                    self.logger.info("OpenAI library imported successfully")
                    
                    self.client = OpenAI(
                        api_key=self.api_key,
                        base_url="https://api.deepseek.com"
                    )
                    self.logger.info("DeepSeek API client created successfully")
                    
                    # Test the connection with a simple model list request
                    try:
                        models = self.client.models.list()
                        self.logger.info(f"API connection test successful, found {len(models.data)} models")
                        status_text = "✅ Connected to DeepSeek API"
                        status_color = "color: green; font-weight: bold;"
                    except Exception as test_error:
                        self.logger.warning(f"API client created but connection test failed: {str(test_error)}")
                        status_text = "⚠️ API client created (connection not verified)"
                        status_color = "color: orange; font-weight: bold;"
                    
                    if hasattr(self, 'status_label'):
                        self.status_label.setText(status_text)
                        self.status_label.setStyleSheet(status_color)
                    
                except ImportError as import_error:
                    self.logger.error(f"Failed to import OpenAI library: {str(import_error)}")
                    log_error(import_error, "OpenAI library import failed")
                    if hasattr(self, 'status_label'):
                        self.status_label.setText("❌ OpenAI library not installed")
                        self.status_label.setStyleSheet("color: red; font-weight: bold;")
                        
            else:
                self.logger.warning("No API key configured - API client will not be initialized")
                if hasattr(self, 'status_label'):
                    self.status_label.setText("❌ No API key configured")
                    self.status_label.setStyleSheet("color: red; font-weight: bold;")
                    
        except Exception as e:
            self.logger.error(f"Failed to setup API client: {str(e)}")
            log_error(e, "API client setup failed")
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"❌ API Error: {str(e)}")
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
        
        self.logger.info("--- API Client Setup Complete ---")
    
    def setup_basic_ui(self):
        """Setup a basic UI when full initialization fails"""
        self.logger.info("Setting up basic UI as fallback...")
        try:
            layout = QVBoxLayout(self)
            
            # Error message
            error_label = QLabel("⚠️ AI Chat Tab failed to initialize properly. Check logs for details.")
            error_label.setStyleSheet("color: red; font-weight: bold; padding: 20px;")
            layout.addWidget(error_label)
            
            # Basic status
            self.status_label = QLabel("❌ Initialization Failed")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            layout.addWidget(self.status_label)
            
            # Retry button
            retry_button = QPushButton("🔄 Retry Initialization")
            retry_button.clicked.connect(self.retry_initialization)
            layout.addWidget(retry_button)
            
            layout.addStretch()
            self.logger.info("Basic UI setup completed")
            
        except Exception as e:
            self.logger.error(f"Even basic UI setup failed: {str(e)}")
            log_error(e, "Basic UI setup failed")
    
    def retry_initialization(self):
        """Retry full initialization"""
        self.logger.info("Retrying full initialization...")
        try:
            # Clear current layout
            if self.layout():
                QWidget().setLayout(self.layout())
            
            # Retry full initialization
            self.__init__(self.parent)
            
        except Exception as e:
            self.logger.error(f"Retry initialization failed: {str(e)}")
            log_error(e, "Retry initialization failed")
            QMessageBox.critical(self, "Initialization Error", f"Failed to initialize: {str(e)}")
    
    def setup_ui(self):
        """Setup the comprehensive UI with all DeepSeek features"""
        main_layout = QVBoxLayout(self)
        
        # Title and status
        title_layout = QHBoxLayout()
        title_label = QLabel("🤖 DeepSeek AI Chat")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        self.status_label = QLabel("Initializing...")
        title_layout.addWidget(self.status_label)
        
        main_layout.addLayout(title_layout)
        
        # Create main splitter
        main_splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Chat area
        chat_panel = self.create_chat_panel()
        main_splitter.addWidget(chat_panel)
        
        # Right panel - Settings and tools
        settings_panel = self.create_settings_panel()
        main_splitter.addWidget(settings_panel)
        
        # Set proportional sizes that scale with screen size
        main_splitter.setSizes([1000, 600])  # Better ratio for large screens
        main_splitter.setStretchFactor(0, 3)  # Chat area gets 3x stretch
        main_splitter.setStretchFactor(1, 2)  # Settings area gets 2x stretch
        main_layout.addWidget(main_splitter)
    
    def create_chat_panel(self):
        """Create the main chat interface panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Chat display with tabs for content and reasoning
        self.chat_tabs = QTabWidget()
        
        # Main chat tab
        self.chat_display = QTextBrowser()
        self.chat_display.setStyleSheet("""
            QTextBrowser {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 2px solid #333333;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 14px;
                line-height: 1.6;
                selection-background-color: #4a90e2;
            }
        """)
        self.chat_display.setMinimumHeight(400)  # Ensure minimum height
        self.chat_tabs.addTab(self.chat_display, "💬 Chat")
        
        # Reasoning display (for deepseek-reasoner model)
        self.reasoning_display = QTextBrowser()
        self.reasoning_display.setStyleSheet("""
            QTextBrowser {
                background-color: #2d1b2e;
                color: #e0e0e0;
                border: 2px solid #6a4c93;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                line-height: 1.3;
            }
        """)
        self.chat_tabs.addTab(self.reasoning_display, "🧠 Reasoning (CoT)")
        
        layout.addWidget(self.chat_tabs)
        
        # Input area
        input_group = QGroupBox("Message Input")
        input_layout = QVBoxLayout(input_group)
        
        self.message_input = QTextEdit()
        self.message_input.setMinimumHeight(60)
        self.message_input.setMaximumHeight(120)
        self.message_input.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border-color: #007bff;
                background-color: #ffffff;
            }
        """)
        self.message_input.setPlaceholderText("Type your message here... (Ctrl+Enter to send)")
        input_layout.addWidget(self.message_input)
        
        # Input controls
        controls_layout = QHBoxLayout()
        
        self.send_button = QPushButton("🚀 Send")
        self.send_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        self.send_button.clicked.connect(self.send_message)
        controls_layout.addWidget(self.send_button)
        
        self.stream_toggle = QCheckBox("Stream Response")
        self.stream_toggle.setChecked(True)
        controls_layout.addWidget(self.stream_toggle)
        
        self.clear_button = QPushButton("🗑️ Clear Chat")
        self.clear_button.clicked.connect(self.clear_chat)
        controls_layout.addWidget(self.clear_button)
        
        controls_layout.addStretch()
        
        # Progress bar for streaming
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        controls_layout.addWidget(self.progress_bar)
        
        input_layout.addLayout(controls_layout)
        layout.addWidget(input_group)
        
        # Connect Enter key
        self.message_input.keyPressEvent = self.handle_key_press
        
        return panel
    
    def create_settings_panel(self):
        """Create the settings and configuration panel"""
        panel = QWidget()
        panel.setMinimumWidth(350)  # Ensure minimum width for readability
        panel.setMaximumWidth(500)  # Prevent it from getting too wide
        layout = QVBoxLayout(panel)
        
        # Create tabbed settings
        settings_tabs = QTabWidget()
        settings_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 8px 12px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007bff;
            }
        """)
        
        # Model settings tab
        model_tab = self.create_model_settings_tab()
        settings_tabs.addTab(model_tab, "🎯 Model")
        
        # Advanced settings tab
        advanced_tab = self.create_advanced_settings_tab()
        settings_tabs.addTab(advanced_tab, "⚙️ Advanced")
        
        # Function calling tab
        function_tab = FunctionCallingWidget()
        self.function_widget = function_tab
        settings_tabs.addTab(function_tab, "🔧 Functions")
        
        # Usage stats tab
        usage_tab = self.create_usage_stats_tab()
        settings_tabs.addTab(usage_tab, "📊 Usage")
        
        layout.addWidget(settings_tabs)
        
        return panel
    
    def create_model_settings_tab(self):
        """Create model selection and basic settings"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # Model selection
        model_group = QGroupBox("Model Selection")
        model_layout = QVBoxLayout(model_group)
        
        self.model_selector = QComboBox()
        self.model_selector.addItems([
            "deepseek-chat (V3 - General Purpose)",
            "deepseek-reasoner (R1 - Advanced Reasoning)"
        ])
        self.model_selector.currentTextChanged.connect(self.on_model_changed)
        model_layout.addWidget(self.model_selector)
        
        # Model info display
        self.model_info = QTextBrowser()
        self.model_info.setMaximumHeight(100)
        self.model_info.setStyleSheet("background-color: #f0f8ff; border: 1px solid #d0d0d0; padding: 8px;")
        self.update_model_info()
        model_layout.addWidget(self.model_info)
        
        layout.addRow(model_group)
        
        # Basic parameters
        params_group = QGroupBox("Parameters")
        params_layout = QFormLayout(params_group)
        
        # Temperature
        self.temperature_slider = QSlider(Qt.Horizontal)
        self.temperature_slider.setRange(0, 200)  # 0.0 to 2.0
        self.temperature_slider.setValue(100)  # Default 1.0
        self.temperature_label = QLabel("1.0")
        temp_layout = QHBoxLayout()
        temp_layout.addWidget(self.temperature_slider)
        temp_layout.addWidget(self.temperature_label)
        self.temperature_slider.valueChanged.connect(
            lambda v: self.temperature_label.setText(f"{v/100:.1f}")
        )
        params_layout.addRow("Temperature:", temp_layout)
        
        # Max tokens
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(1, 8192)
        self.max_tokens_spin.setValue(4096)
        params_layout.addRow("Max Tokens:", self.max_tokens_spin)
        
        # Top P
        self.top_p_slider = QSlider(Qt.Horizontal)
        self.top_p_slider.setRange(1, 100)  # 0.01 to 1.0
        self.top_p_slider.setValue(100)  # Default 1.0
        self.top_p_label = QLabel("1.0")
        top_p_layout = QHBoxLayout()
        top_p_layout.addWidget(self.top_p_slider)
        top_p_layout.addWidget(self.top_p_label)
        self.top_p_slider.valueChanged.connect(
            lambda v: self.top_p_label.setText(f"{v/100:.2f}")
        )
        params_layout.addRow("Top P:", top_p_layout)
        
        layout.addRow(params_group)
        
        return widget
    
    def create_advanced_settings_tab(self):
        """Create advanced settings and features"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Response format
        format_group = QGroupBox("Response Format")
        format_layout = QVBoxLayout(format_group)
        
        self.format_group = QButtonGroup()
        
        self.format_text = QRadioButton("Text (Default)")
        self.format_text.setChecked(True)
        self.format_group.addButton(self.format_text)
        format_layout.addWidget(self.format_text)
        
        self.format_json = QRadioButton("JSON Object")
        self.format_group.addButton(self.format_json)
        format_layout.addWidget(self.format_json)
        
        layout.addWidget(format_group)
        
        # Context and caching
        context_group = QGroupBox("Context & Caching")
        context_layout = QVBoxLayout(context_group)
        
        self.context_cache_cb = QCheckBox("Enable Context Caching (74% cost savings)")
        self.context_cache_cb.setChecked(True)
        context_layout.addWidget(self.context_cache_cb)
        
        self.multi_round_cb = QCheckBox("Multi-round Conversation")
        self.multi_round_cb.setChecked(True)
        context_layout.addWidget(self.multi_round_cb)
        
        layout.addWidget(context_group)
        
        # Penalties
        penalty_group = QGroupBox("Penalties")
        penalty_layout = QFormLayout(penalty_group)
        
        self.freq_penalty_slider = QSlider(Qt.Horizontal)
        self.freq_penalty_slider.setRange(-200, 200)  # -2.0 to 2.0
        self.freq_penalty_slider.setValue(0)
        self.freq_penalty_label = QLabel("0.0")
        freq_layout = QHBoxLayout()
        freq_layout.addWidget(self.freq_penalty_slider)
        freq_layout.addWidget(self.freq_penalty_label)
        self.freq_penalty_slider.valueChanged.connect(
            lambda v: self.freq_penalty_label.setText(f"{v/100:.1f}")
        )
        penalty_layout.addRow("Frequency Penalty:", freq_layout)
        
        self.presence_penalty_slider = QSlider(Qt.Horizontal)
        self.presence_penalty_slider.setRange(-200, 200)  # -2.0 to 2.0
        self.presence_penalty_slider.setValue(0)
        self.presence_penalty_label = QLabel("0.0")
        pres_layout = QHBoxLayout()
        pres_layout.addWidget(self.presence_penalty_slider)
        pres_layout.addWidget(self.presence_penalty_label)
        self.presence_penalty_slider.valueChanged.connect(
            lambda v: self.presence_penalty_label.setText(f"{v/100:.1f}")
        )
        penalty_layout.addRow("Presence Penalty:", pres_layout)
        
        layout.addWidget(penalty_group)
        
        # Stop sequences
        stop_group = QGroupBox("Stop Sequences")
        stop_layout = QVBoxLayout(stop_group)
        
        self.stop_sequences = QLineEdit()
        self.stop_sequences.setPlaceholderText("Enter stop sequences separated by commas")
        stop_layout.addWidget(self.stop_sequences)
        
        layout.addWidget(stop_group)
        
        layout.addStretch()
        
        return widget
    
    def create_usage_stats_tab(self):
        """Create usage statistics display"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Token usage display
        usage_group = QGroupBox("Token Usage Statistics")
        usage_layout = QFormLayout(usage_group)
        
        self.prompt_tokens_label = QLabel("0")
        usage_layout.addRow("Prompt Tokens:", self.prompt_tokens_label)
        
        self.completion_tokens_label = QLabel("0")
        usage_layout.addRow("Completion Tokens:", self.completion_tokens_label)
        
        self.reasoning_tokens_label = QLabel("0")
        usage_layout.addRow("Reasoning Tokens:", self.reasoning_tokens_label)
        
        self.total_tokens_label = QLabel("0")
        usage_layout.addRow("Total Tokens:", self.total_tokens_label)
        
        self.cache_hit_label = QLabel("0")
        usage_layout.addRow("Cache Hit Tokens:", self.cache_hit_label)
        
        self.cache_miss_label = QLabel("0")
        usage_layout.addRow("Cache Miss Tokens:", self.cache_miss_label)
        
        layout.addWidget(usage_group)
        
        # Cost estimation
        cost_group = QGroupBox("Cost Estimation")
        cost_layout = QFormLayout(cost_group)
        
        self.estimated_cost_label = QLabel("$0.00")
        cost_layout.addRow("Estimated Cost:", self.estimated_cost_label)
        
        layout.addWidget(cost_group)
        
        # Session stats
        session_group = QGroupBox("Session Statistics")
        session_layout = QFormLayout(session_group)
        
        self.message_count_label = QLabel("0")
        session_layout.addRow("Messages Sent:", self.message_count_label)
        
        self.session_tokens_label = QLabel("0")
        session_layout.addRow("Session Tokens:", self.session_tokens_label)
        
        layout.addWidget(session_group)
        
        layout.addStretch()
        
        return widget
    
    def on_model_changed(self, model_text):
        """Handle model selection change"""
        if "deepseek-chat" in model_text:
            self.current_model = "deepseek-chat"
        elif "deepseek-reasoner" in model_text:
            self.current_model = "deepseek-reasoner"
        
        self.update_model_info()
        
        # Show/hide reasoning tab based on model
        if self.current_model == "deepseek-reasoner":
            if self.chat_tabs.count() == 1:
                self.chat_tabs.addTab(self.reasoning_display, "🧠 Reasoning (CoT)")
        else:
            if self.chat_tabs.count() == 2:
                self.chat_tabs.removeTab(1)
    
    def update_model_info(self):
        """Update model information display"""
        model_info = {
            "deepseek-chat": {
                "name": "DeepSeek V3",
                "description": "General-purpose model for chat, coding, and content generation",
                "context": "128,000 tokens",
                "features": ["High performance", "Code generation", "Multilingual", "Context caching"]
            },
            "deepseek-reasoner": {
                "name": "DeepSeek R1", 
                "description": "Advanced reasoning model with Chain-of-Thought capabilities",
                "context": "128,000 tokens", 
                "features": ["Complex reasoning", "Mathematical problem solving", "Step-by-step analysis", "Research tasks"]
            }
        }
        
        info = model_info.get(self.current_model, model_info["deepseek-chat"])
        
        html = f"""
        <b>{info['name']}</b><br>
        <i>{info['description']}</i><br>
        <b>Context:</b> {info['context']}<br>
        <b>Features:</b> {', '.join(info['features'])}
        """
        
        self.model_info.setHtml(html)
    
    def handle_key_press(self, event):
        """Handle keyboard shortcuts in message input"""
        if event.key() == Qt.Key_Return and event.modifiers() == Qt.ControlModifier:
            self.send_message()
        else:
            QTextEdit.keyPressEvent(self.message_input, event)
    
    def get_current_options(self):
        """Get current chat options from UI"""
        options = {
            'model': self.current_model,
            'temperature': self.temperature_slider.value() / 100.0,
            'max_tokens': self.max_tokens_spin.value(),
            'top_p': self.top_p_slider.value() / 100.0,
            'frequency_penalty': self.freq_penalty_slider.value() / 100.0,
            'presence_penalty': self.presence_penalty_slider.value() / 100.0,
            'stream': self.stream_toggle.isChecked(),
            'include_usage': True
        }
        
        # Add response format
        if self.format_json.isChecked():
            options['response_format'] = {"type": "json_object"}
        
        # Add stop sequences
        stop_text = self.stop_sequences.text().strip()
        if stop_text:
            stop_sequences = [s.strip() for s in stop_text.split(',') if s.strip()]
            if stop_sequences:
                options['stop'] = stop_sequences
        
        # Add function calling if functions are defined
        functions = self.function_widget.get_functions()
        if functions:
            options['tools'] = functions
            options['tool_choice'] = "auto"
        
        return options
    
    def send_message(self):
        """Send a message to DeepSeek API"""
        self.logger.info("=== Send Message Started ===")
        
        try:
            # Validate API client
            if not self.client:
                error_msg = "DeepSeek API client not initialized. Please check your API key in Settings."
                self.logger.error(f"Send message failed: {error_msg}")
                QMessageBox.warning(self, "API Error", error_msg)
                return
            
            # Get and validate message text
            message_text = self.message_input.toPlainText().strip()
            self.logger.info(f"Message text length: {len(message_text) if message_text else 0}")
            
            if not message_text:
                self.logger.warning("Empty message - nothing to send")
                return
            
            self.logger.info(f"Sending message: {message_text[:100]}{'...' if len(message_text) > 100 else ''}")
            
            # Add user message to conversation
            user_message = DeepSeekChatMessage("user", message_text)
            self.messages.append(user_message)
            self.logger.info(f"Added user message to conversation. Total messages: {len(self.messages)}")
            
            # Display user message
            self.logger.info("Displaying user message...")
            self.display_message(user_message)
            
            # Clear input
            self.message_input.clear()
            
            # Prepare messages for API
            self.logger.info("Preparing API messages...")
            api_messages = self.prepare_api_messages()
            self.logger.info(f"Prepared {len(api_messages)} messages for API")
            
            # Get current options
            self.logger.info("Getting current options...")
            options = self.get_current_options()
            self.logger.info(f"Current options: {options}")
            
            # Send message
            if options.get('stream', True):
                self.logger.info("Sending streaming message...")
                self.send_streaming_message(api_messages, options)
            else:
                self.logger.info("Sending non-streaming message...")
                self.send_non_streaming_message(api_messages, options)
                
            self.logger.info("=== Send Message Process Initiated ===")
            
        except Exception as e:
            self.logger.error("=== Send Message FAILED ===")
            self.logger.error(f"Error in send_message: {str(e)}")
            log_error(e, "Send message failed")
            QMessageBox.critical(self, "Error", f"Failed to send message: {str(e)}")
            
            # Re-enable send button in case of error
            if hasattr(self, 'send_button'):
                self.send_button.setEnabled(True)
            if hasattr(self, 'progress_bar'):
                self.progress_bar.setVisible(False)
    
    def prepare_api_messages(self):
        """Prepare messages for API call"""
        api_messages = []
        
        for msg in self.messages:
            api_msg = {
                "role": msg.role,
                "content": msg.content
            }
            
            # Add optional fields only if they should be sent to API
            if msg.name:
                api_msg["name"] = msg.name
            
            if msg.prefix and msg.role == "assistant":
                api_msg["prefix"] = True
            
            # IMPORTANT: Do NOT include reasoning_content in API requests
            # The reasoning_content is for display purposes only and will cause
            # a 400 error if included in subsequent API calls
            # if msg.reasoning_content and msg.role == "assistant":
            #     api_msg["reasoning_content"] = msg.reasoning_content
            
            if msg.tool_calls:
                api_msg["tool_calls"] = msg.tool_calls
            
            api_messages.append(api_msg)
            self.logger.debug(f"Prepared API message {len(api_messages)}: role={msg.role}, content_length={len(msg.content)}, has_reasoning={bool(msg.reasoning_content)}")
        
        self.logger.info(f"Total API messages prepared: {len(api_messages)}")
        
        return api_messages
    
    def send_streaming_message(self, messages, options):
        """Send message with streaming response"""
        self.logger.info("--- Starting Streaming Message ---")
        
        try:
            # Stop any existing stream first
            if self.stream_worker and self.stream_worker.isRunning():
                self.logger.info("Stopping existing stream worker...")
                self.stream_worker.stop()
                self.stream_worker.wait(500)
                if self.stream_worker.isRunning():
                    self.logger.warning("Stream worker did not stop gracefully, terminating...")
                    self.stream_worker.terminate()
                    self.stream_worker.wait(1000)
            
            # Clean up old worker reference
            if self.stream_worker:
                self.logger.info("Cleaning up old stream worker...")
                try:
                    self.stream_worker.deleteLater()
                except RuntimeError:
                    pass  # Worker already deleted
                self.stream_worker = None
            
            self.send_button.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
            
            self.logger.info("Creating new stream worker...")
            
            # Create and start worker thread
            self.stream_worker = ChatStreamWorker(self.client, messages, options)
            self.stream_worker.message_chunk.connect(self.on_message_chunk)
            self.stream_worker.reasoning_chunk.connect(self.on_reasoning_chunk)
            self.stream_worker.message_complete.connect(self.on_message_complete)
            self.stream_worker.error_occurred.connect(self.on_stream_error)
            
            # Handle worker cleanup when finished (but don't auto-delete)
            self.stream_worker.finished.connect(self.on_stream_worker_finished)
            
            self.logger.info("Starting stream worker...")
            self.stream_worker.start()
            
            # Start assistant message display
            self.current_assistant_message = DeepSeekChatMessage("assistant", "")
            self.display_message_start(self.current_assistant_message)
            
            self.logger.info("--- Streaming Message Started Successfully ---")
            
        except Exception as e:
            self.logger.error(f"Error starting streaming message: {str(e)}")
            log_error(e, "Streaming message start failed")
            
            # Reset UI state on error
            self.send_button.setEnabled(True)
            self.progress_bar.setVisible(False)
            
            QMessageBox.critical(self, "Streaming Error", f"Failed to start streaming: {str(e)}")
    
    def on_stream_worker_finished(self):
        """Handle stream worker finished signal"""
        self.logger.info("Stream worker finished, scheduling cleanup...")
        # Don't immediately delete the worker, let it be cleaned up later
        if self.stream_worker:
            # Disconnect all signals to prevent issues
            try:
                self.stream_worker.message_chunk.disconnect()
                self.stream_worker.reasoning_chunk.disconnect()
                self.stream_worker.message_complete.disconnect()
                self.stream_worker.error_occurred.disconnect()
                self.stream_worker.finished.disconnect()
            except (RuntimeError, TypeError):
                pass  # Signals may already be disconnected
    
    def send_non_streaming_message(self, messages, options):
        """Send message without streaming"""
        self.send_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        
        try:
            # Remove stream option for non-streaming
            options_copy = options.copy()
            options_copy['stream'] = False
            
            response = self.client.chat.completions.create(
                messages=messages,
                **options_copy
            )
            
            # Process response
            choice = response.choices[0]
            message = choice.message
            
            assistant_message = DeepSeekChatMessage(
                "assistant",
                message.content,
                getattr(message, 'reasoning_content', None),
                getattr(message, 'tool_calls', None)
            )
            
            self.messages.append(assistant_message)
            self.display_message(assistant_message)
            
            # Update usage stats
            if hasattr(response, 'usage'):
                self.update_usage_stats(response.usage)
            
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            QMessageBox.critical(self, "API Error", f"Failed to send message: {str(e)}")
        
        finally:
            self.send_button.setEnabled(True)
            self.progress_bar.setVisible(False)
    
    def on_message_chunk(self, chunk):
        """Handle streaming message chunk"""
        if hasattr(self, 'current_assistant_message'):
            self.current_assistant_message.content += chunk
            self.append_to_current_message(chunk)
    
    def on_reasoning_chunk(self, chunk):
        """Handle reasoning content chunk"""
        if hasattr(self, 'current_assistant_message'):
            if self.current_assistant_message.reasoning_content is None:
                self.current_assistant_message.reasoning_content = ""
            self.current_assistant_message.reasoning_content += chunk
            self.append_to_reasoning_display(chunk)
    
    def on_message_complete(self, complete_data):
        """Handle completion of streaming message"""
        try:
            if hasattr(self, 'current_assistant_message'):
                # Update message with complete data
                self.current_assistant_message.content = complete_data['content']
                self.current_assistant_message.reasoning_content = complete_data.get('reasoning_content')
                
                # Add to messages list
                self.messages.append(self.current_assistant_message)
                
                # Update usage stats
                if complete_data.get('usage'):
                    # Convert dict to object-like structure for compatibility
                    usage_dict = complete_data['usage']
                    self.logger.info(f"Updating usage stats: {usage_dict}")
                    self.update_usage_stats_from_dict(usage_dict)
                
                delattr(self, 'current_assistant_message')
            
            # Clean up reasoning state
            if hasattr(self, 'reasoning_started'):
                delattr(self, 'reasoning_started')
                
        except Exception as e:
            self.logger.error(f"Error completing message: {e}")
        finally:
            # Always reset UI state
            self.send_button.setEnabled(True)
            self.progress_bar.setVisible(False)
            self.progress_bar.setRange(0, 100)
            
            # Clean up the current worker reference after completion
            self.logger.info("Message completion cleanup - marking worker for cleanup")
            if hasattr(self, 'stream_worker') and self.stream_worker:
                # Schedule worker deletion after a short delay to prevent issues
                QTimer.singleShot(100, self.cleanup_stream_worker)
    
    def cleanup_stream_worker(self):
        """Clean up the stream worker safely"""
        try:
            if hasattr(self, 'stream_worker') and self.stream_worker:
                self.logger.info("Cleaning up stream worker...")
                try:
                    if not self.stream_worker.isRunning():
                        self.stream_worker.deleteLater()
                        self.stream_worker = None
                        self.logger.info("Stream worker cleaned up successfully")
                    else:
                        self.logger.warning("Stream worker still running, delaying cleanup")
                        QTimer.singleShot(500, self.cleanup_stream_worker)  # Try again later
                except RuntimeError:
                    # Worker already deleted
                    self.stream_worker = None
                    self.logger.info("Stream worker was already deleted")
        except Exception as e:
            self.logger.error(f"Error during stream worker cleanup: {str(e)}")
            self.stream_worker = None  # Clear reference anyway
    
    def on_stream_error(self, error_msg):
        """Handle streaming error"""
        self.logger.error("=== STREAMING ERROR OCCURRED ===")
        self.logger.error(f"Error message: {error_msg}")
        
        # Log current state for debugging
        self.logger.error(f"Current messages count: {len(self.messages) if hasattr(self, 'messages') else 'N/A'}")
        self.logger.error(f"Has current_assistant_message: {hasattr(self, 'current_assistant_message')}")
        self.logger.error(f"Stream worker running: {self.stream_worker.isRunning() if self.stream_worker else 'No worker'}")
        
        try:
            # Show error to user
            QMessageBox.critical(self, "Streaming Error", f"Error during streaming: {error_msg}")
            
            # Clean up any partial state
            if hasattr(self, 'current_assistant_message'):
                self.logger.info("Cleaning up partial assistant message")
                delattr(self, 'current_assistant_message')
            if hasattr(self, 'reasoning_started'):
                self.logger.info("Cleaning up reasoning state")
                delattr(self, 'reasoning_started')
            
            # Reset UI state
            self.logger.info("Resetting UI state after error")
            if hasattr(self, 'send_button'):
                self.send_button.setEnabled(True)
            if hasattr(self, 'progress_bar'):
                self.progress_bar.setVisible(False)
                self.progress_bar.setRange(0, 100)
            
            # Clean up the worker after error
            if hasattr(self, 'stream_worker') and self.stream_worker:
                try:
                    self.stream_worker.stop()
                    self.stream_worker.wait(1000)
                    self.stream_worker.deleteLater()
                    self.stream_worker = None
                    self.logger.info("Stream worker cleaned up after error")
                except RuntimeError:
                    self.stream_worker = None
                    self.logger.info("Stream worker was already deleted")
                
            self.logger.info("Streaming error cleanup completed")
            
        except Exception as cleanup_error:
            self.logger.error(f"Error during streaming error cleanup: {str(cleanup_error)}")
            log_error(cleanup_error, "Streaming error cleanup failed")
    
    def display_message(self, message):
        """Display a complete message in the chat"""
        timestamp = message.timestamp.strftime("%H:%M:%S")
        
        if message.role == "user":
            html = f"""
            <div style="margin: 10px 0; padding: 10px; background-color: #e3f2fd; border-left: 4px solid #2196f3; border-radius: 5px;">
                <strong style="color: #1976d2;">👤 You</strong> <span style="color: #666; font-size: 0.9em;">{timestamp}</span><br>
                <div style="margin-top: 5px; white-space: pre-wrap;">{self.escape_html(message.content)}</div>
            </div>
            """
        else:  # assistant
            model_icon = "🧠" if self.current_model == "deepseek-reasoner" else "🤖"
            model_name = "DeepSeek R1" if self.current_model == "deepseek-reasoner" else "DeepSeek V3"
            
            html = f"""
            <div style="margin: 10px 0; padding: 10px; background-color: #f3e5f5; border-left: 4px solid #9c27b0; border-radius: 5px;">
                <strong style="color: #7b1fa2;">{model_icon} {model_name}</strong> <span style="color: #666; font-size: 0.9em;">{timestamp}</span><br>
                <div style="margin-top: 5px; white-space: pre-wrap;">{self.escape_html(message.content)}</div>
            </div>
            """
        
        self.chat_display.append(html)
        self.scroll_to_bottom()
        
        # Display reasoning content if available
        if message.reasoning_content and self.current_model == "deepseek-reasoner":
            reasoning_html = f"""
            <div style="margin: 10px 0; padding: 10px; background-color: #2d1b2e; color: #e0e0e0; border-left: 4px solid #6a4c93; border-radius: 5px;">
                <strong style="color: #9c4dcc;">🧠 Chain of Thought</strong> <span style="color: #999; font-size: 0.9em;">{timestamp}</span><br>
                <div style="margin-top: 5px; white-space: pre-wrap; font-family: monospace; font-size: 0.9em;">{self.escape_html(message.reasoning_content)}</div>
            </div>
            """
            self.reasoning_display.append(reasoning_html)
    
    def display_message_start(self, message):
        """Start displaying a streaming message"""
        timestamp = message.timestamp.strftime("%H:%M:%S")
        model_icon = "🧠" if self.current_model == "deepseek-reasoner" else "🤖"
        model_name = "DeepSeek R1" if self.current_model == "deepseek-reasoner" else "DeepSeek V3"
        
        html = f"""
        <div id="current-message" style="margin: 10px 0; padding: 10px; background-color: #f3e5f5; border-left: 4px solid #9c27b0; border-radius: 5px;">
            <strong style="color: #7b1fa2;">{model_icon} {model_name}</strong> <span style="color: #666; font-size: 0.9em;">{timestamp}</span><br>
            <div id="message-content" style="margin-top: 5px; white-space: pre-wrap;">
        """
        
        self.chat_display.append(html)
    
    def append_to_current_message(self, chunk):
        """Append chunk to current streaming message"""
        cursor = self.chat_display.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertText(chunk)
        self.scroll_to_bottom()
    
    def append_to_reasoning_display(self, chunk):
        """Append reasoning chunk to reasoning display"""
        if not hasattr(self, 'reasoning_started'):
            timestamp = datetime.now().strftime("%H:%M:%S")
            html = f"""
            <div style="margin: 10px 0; padding: 10px; background-color: #2d1b2e; color: #e0e0e0; border-left: 4px solid #6a4c93; border-radius: 5px;">
                <strong style="color: #9c4dcc;">🧠 Chain of Thought</strong> <span style="color: #999; font-size: 0.9em;">{timestamp}</span><br>
                <div style="margin-top: 5px; white-space: pre-wrap; font-family: monospace; font-size: 0.9em;">
            """
            self.reasoning_display.append(html)
            self.reasoning_started = True
        
        cursor = self.reasoning_display.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertText(chunk)
    
    def update_usage_stats(self, usage):
        """Update usage statistics display from usage object"""
        try:
            self.prompt_tokens_label.setText(str(usage.prompt_tokens))
            self.completion_tokens_label.setText(str(usage.completion_tokens))
            self.total_tokens_label.setText(str(usage.total_tokens))
            
            # Handle cache statistics
            if hasattr(usage, 'prompt_cache_hit_tokens'):
                self.cache_hit_label.setText(str(usage.prompt_cache_hit_tokens))
            if hasattr(usage, 'prompt_cache_miss_tokens'):
                self.cache_miss_label.setText(str(usage.prompt_cache_miss_tokens))
            
            # Handle reasoning tokens
            if hasattr(usage, 'completion_tokens_details'):
                details = usage.completion_tokens_details
                if hasattr(details, 'reasoning_tokens'):
                    self.reasoning_tokens_label.setText(str(details.reasoning_tokens))
            
            # Estimate cost (approximate rates)
            input_cost = usage.prompt_tokens * 0.00007 / 1000  # $0.07 per 1M tokens
            output_cost = usage.completion_tokens * 0.00028 / 1000  # $0.28 per 1M tokens
            total_cost = input_cost + output_cost
            
            self.estimated_cost_label.setText(f"${total_cost:.6f}")
            
            # Update session stats
            current_messages = int(self.message_count_label.text()) + 1
            self.message_count_label.setText(str(current_messages))
            
            current_session_tokens = int(self.session_tokens_label.text()) + usage.total_tokens
            self.session_tokens_label.setText(str(current_session_tokens))
            
        except Exception as e:
            self.logger.error(f"Error updating usage stats: {str(e)}")

    def update_usage_stats_from_dict(self, usage_dict):
        """Update usage statistics display from usage dictionary"""
        try:
            self.logger.info("Updating usage stats from dictionary...")
            
            # Extract values from dictionary
            prompt_tokens = usage_dict.get('prompt_tokens', 0)
            completion_tokens = usage_dict.get('completion_tokens', 0)
            total_tokens = usage_dict.get('total_tokens', 0)
            
            self.logger.info(f"Prompt tokens: {prompt_tokens}, Completion tokens: {completion_tokens}, Total tokens: {total_tokens}")
            
            if hasattr(self, 'prompt_tokens_label'):
                self.prompt_tokens_label.setText(str(prompt_tokens))
            if hasattr(self, 'completion_tokens_label'):
                self.completion_tokens_label.setText(str(completion_tokens))
            if hasattr(self, 'total_tokens_label'):
                self.total_tokens_label.setText(str(total_tokens))
            
            # Handle cache statistics
            cache_hit = usage_dict.get('prompt_cache_hit_tokens', 0)
            cache_miss = usage_dict.get('prompt_cache_miss_tokens', 0)
            
            if hasattr(self, 'cache_hit_label'):
                self.cache_hit_label.setText(str(cache_hit))
            if hasattr(self, 'cache_miss_label'):
                self.cache_miss_label.setText(str(cache_miss))
            
            # Handle reasoning tokens
            reasoning_tokens = usage_dict.get('reasoning_tokens', 0)
            if hasattr(self, 'reasoning_tokens_label'):
                self.reasoning_tokens_label.setText(str(reasoning_tokens))
            
            # Estimate cost (approximate rates)
            input_cost = prompt_tokens * 0.00007 / 1000  # $0.07 per 1M tokens
            output_cost = completion_tokens * 0.00028 / 1000  # $0.28 per 1M tokens
            total_cost = input_cost + output_cost
            
            if hasattr(self, 'estimated_cost_label'):
                self.estimated_cost_label.setText(f"${total_cost:.6f}")
            
            # Update session stats
            if hasattr(self, 'message_count_label'):
                current_messages = int(self.message_count_label.text()) + 1
                self.message_count_label.setText(str(current_messages))
            
            if hasattr(self, 'session_tokens_label'):
                current_session_tokens = int(self.session_tokens_label.text()) + total_tokens
                self.session_tokens_label.setText(str(current_session_tokens))
                
            self.logger.info("Usage stats updated successfully")
            
        except Exception as e:
            self.logger.error(f"Error updating usage stats from dict: {str(e)}")
            log_error(e, "Usage stats update failed")
    
    def clear_chat(self):
        """Clear the chat history"""
        reply = QMessageBox.question(
            self, 
            "Clear Chat", 
            "Are you sure you want to clear the chat history?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Stop any active streaming
            if self.stream_worker and self.stream_worker.isRunning():
                self.stream_worker.stop()
                self.stream_worker.wait(1000)  # Wait up to 1 second
                if self.stream_worker.isRunning():
                    self.stream_worker.terminate()
            
            # Reset UI state
            self.send_button.setEnabled(True)
            self.progress_bar.setVisible(False)
            self.progress_bar.setRange(0, 100)
            
            # Clear messages and displays
            self.messages.clear()
            self.chat_display.clear()
            self.reasoning_display.clear()
            
            # Reset session stats
            self.message_count_label.setText("0")
            self.session_tokens_label.setText("0")
            
            # Reset all usage labels
            self.prompt_tokens_label.setText("0")
            self.completion_tokens_label.setText("0")
            self.reasoning_tokens_label.setText("0")
            self.total_tokens_label.setText("0")
            self.cache_hit_label.setText("0")
            self.cache_miss_label.setText("0")
            self.estimated_cost_label.setText("$0.00")
            
            # Clean up temporary attributes
            if hasattr(self, 'reasoning_started'):
                delattr(self, 'reasoning_started')
            if hasattr(self, 'current_assistant_message'):
                delattr(self, 'current_assistant_message')
            
            # Reset stream worker reference
            self.stream_worker = None
    
    def refresh_settings(self):
        """Refresh settings from config"""
        self.logger.debug("--- Refreshing Settings ---")
        
        try:
            # Load latest config
            self.logger.debug("Loading latest configuration...")
            new_config = load_config()
            
            if not new_config:
                self.logger.warning("No configuration loaded, keeping existing settings")
                return
            
            # Check for API key changes
            old_api_key = getattr(self, 'api_key', '')
            new_api_key = new_config.get("deepseek_api_key", "")
            
            self.logger.debug(f"API key changed: {old_api_key != new_api_key}")
            self.logger.debug(f"Old key length: {len(old_api_key) if old_api_key else 0}")
            self.logger.debug(f"New key length: {len(new_api_key) if new_api_key else 0}")
            
            # Update config and API key
            self.config = new_config
            
            if new_api_key != self.api_key:
                self.logger.info("API key changed, reinitializing client...")
                self.api_key = new_api_key
                self.setup_api_client()
            else:
                self.logger.debug("API key unchanged, skipping client reinitialization")
                
            self.logger.debug("Settings refresh completed successfully")
                
        except Exception as e:
            self.logger.error(f"Error refreshing settings: {str(e)}")
            log_error(e, "Settings refresh failed")
    
    def scroll_to_bottom(self):
        """Scroll chat display to bottom"""
        try:
            if hasattr(self, 'chat_display'):
                scrollbar = self.chat_display.verticalScrollBar()
                scrollbar.setValue(scrollbar.maximum())
        except Exception as e:
            self.logger.error(f"Error scrolling to bottom: {str(e)}")
    
    def escape_html(self, text):
        """Escape HTML characters in text"""
        try:
            return (text.replace("&", "&amp;")
                       .replace("<", "&lt;")
                       .replace(">", "&gt;")
                       .replace('"', "&quot;")
                       .replace("'", "&#x27;"))
        except Exception as e:
            self.logger.error(f"Error escaping HTML: {str(e)}")
            return str(text)  # Return as string if escape fails