import requests
from bs4 import BeautifulSoup
import urllib.parse
import time
import random

import threading
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options

from PyQt5.QtCore import QObject, pyqtSignal
import os
import json
from datetime import datetime
from intellicrawler.utils.logger import get_logger

class Crawler(QObject):
    """Web crawler component that handles the scraping logic"""
    
    # Signals for UI updates
    progress_updated = pyqtSignal(int, int)  # current, total
    status_updated = pyqtSignal(str)
    page_scraped = pyqtSignal(dict)
    crawl_completed = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'IntelliCrawler/2.0 (AI-powered web analysis)'
        })
        self.driver = None
        self.visited_urls = set()
        self.scraped_data = []
        self.base_url = ""
        self.should_stop = False
        
        # Thread-safe data structures
        self.visited_urls_lock = threading.Lock()
        self.scraped_data_lock = threading.Lock()
        self.pages_scraped_count = 0
        self.pages_scraped_lock = threading.Lock()
    
    def initialize_selenium(self, chrome_path=None):
        """Initialize Selenium for JavaScript-heavy websites"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920x1080")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-features=NetworkService")
            chrome_options.add_argument("--disable-features=IsolateOrigins")
            chrome_options.add_argument("--disable-site-isolation-trials")
            
            # Use the provided Chrome binary path if specified
            if chrome_path and os.path.exists(chrome_path):
                self.logger.info(f"Using custom Chrome binary path: {chrome_path}")
                chrome_options.binary_location = chrome_path
                
                # Check if this is Edge browser
                if "edge" in chrome_path.lower() or "msedge" in chrome_path.lower():
                    self.logger.info("Microsoft Edge detected, using Edge WebDriver")
                    try:
                        # For Selenium 4.9.0, we need to use the WebDriver module directly
                        from selenium.webdriver.edge.service import Service as EdgeService
                        from webdriver_manager.microsoft import EdgeChromiumDriverManager
                        
                        # Create Edge options
                        edge_options = webdriver.EdgeOptions()
                        
                        # Copy Chrome options to Edge options
                        for arg in chrome_options.arguments:
                            edge_options.add_argument(arg)
                            
                        edge_options.binary_location = chrome_path
                        
                        # Create service with explicit timeouts
                        edge_service = EdgeService(
                            EdgeChromiumDriverManager().install()
                        )
                        
                        # Initialize Edge WebDriver with Selenium 4.9.0
                        self.driver = webdriver.Edge(service=edge_service, options=edge_options)
                        self.logger.info("Edge WebDriver initialized successfully with Selenium 4.9.0")
                        return True
                        
                    except Exception as e:
                        self.logger.error(f"Error initializing Edge WebDriver: {str(e)}")
                        raise
            
            # Try to find Chrome in common installation locations if path not provided
            if not chrome_path or not os.path.exists(chrome_path):
                common_chrome_locations = [
                    # Windows default installation paths
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    # User profile installation paths
                    os.path.join(os.environ.get('LOCALAPPDATA', ''), r"Google\Chrome\Application\chrome.exe"),
                    # Edge as fallback (Chromium-based)
                    r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
                ]
                
                for location in common_chrome_locations:
                    if os.path.exists(location):
                        self.logger.info(f"Found Chrome/Chromium browser at: {location}")
                        chrome_options.binary_location = location
                        
                        # Check if this is Edge browser
                        if "edge" in location.lower() or "msedge" in location.lower():
                            self.logger.info("Microsoft Edge detected, using Edge WebDriver")
                            try:
                                # For Selenium 4.9.0, we need to use the WebDriver module directly
                                from selenium.webdriver.edge.service import Service as EdgeService
                                from webdriver_manager.microsoft import EdgeChromiumDriverManager
                                
                                # Create Edge options
                                edge_options = webdriver.EdgeOptions()
                                
                                # Copy Chrome options to Edge options
                                for arg in chrome_options.arguments:
                                    edge_options.add_argument(arg)
                                    
                                edge_options.binary_location = location
                                
                                # Create service with explicit timeouts
                                edge_service = EdgeService(
                                    EdgeChromiumDriverManager().install()
                                )
                                
                                # Initialize Edge WebDriver with Selenium 4.9.0
                                self.driver = webdriver.Edge(service=edge_service, options=edge_options)
                                self.logger.info("Edge WebDriver initialized successfully with Selenium 4.9.0")
                                return True
                                
                            except Exception as e:
                                self.logger.error(f"Error initializing Edge WebDriver: {str(e)}")
                                raise
                        break
                else:
                    # This only executes if the for loop completes without a break
                    self.logger.warning("Chrome browser not found in any common location")
                    helpful_message = (
                        "Chrome browser not found. Please do one of the following:\n"
                        "1. Install Google Chrome from https://www.google.com/chrome/\n"
                        "2. Specify the chrome_path parameter with the path to your Chrome executable\n"
                        "3. Use --no-dynamic option to disable JavaScript rendering (some websites may not work properly)"
                    )
                    self.logger.warning(helpful_message)
                    self.error_occurred.emit(helpful_message)
                    return False
            
            # If we're here, we're using Chrome (not Edge)
            # Add exception handling and retries for Chrome WebDriver installation
            max_retries = 3
            retry_count = 0
            last_exception = None
            
            while retry_count < max_retries:
                try:
                    # Create a Service with Selenium 4.9.0 compatible params
                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    self.logger.info("Chrome WebDriver initialized successfully")
                    return True
                except Exception as e:
                    retry_count += 1
                    last_exception = e
                    self.logger.warning(f"Chrome WebDriver initialization attempt {retry_count} failed: {str(e)}")
                    time.sleep(2)  # Wait before retrying
            
            # If we've exhausted all retries
            if last_exception:
                raise last_exception
                
        except Exception as e:
            error_message = f"Failed to initialize Selenium: {str(e)}"
            self.logger.error(error_message)
            self.error_occurred.emit(error_message)
            return False
    
    def crawl(self, start_url, max_pages=100, depth=3, dynamic=False, delay=1.0, chrome_path=None):
        """
        Main crawling method
        
        Args:
            start_url (str): URL to start crawling from
            max_pages (int): Maximum number of pages to crawl
            depth (int): Maximum crawl depth
            dynamic (bool): Whether to use Selenium for dynamic content
            delay (float): Delay between requests in seconds
            chrome_path (str, optional): Path to Chrome binary
        """
        self.should_stop = False
        self.visited_urls = set()
        self.scraped_data = []
        
        try:
            self.base_url = urllib.parse.urlparse(start_url).netloc
            
            if dynamic:
                # Initialize Selenium if it's not already initialized
                if not hasattr(self, 'driver') or self.driver is None:
                    selenium_init_success = self.initialize_selenium(chrome_path)
                    if not selenium_init_success:
                        error_message = "Failed to initialize Selenium WebDriver. Aborting crawl."
                        self.logger.error(error_message)
                        self.error_occurred.emit(error_message)
                        return
            
            self.status_updated.emit(f"Starting crawl from {start_url}")
            
            # URLs to crawl with their depth
            urls_to_crawl = [(start_url, 0)]  
            
            pages_scraped = 0
            
            while urls_to_crawl and pages_scraped < max_pages and not self.should_stop:
                url, current_depth = urls_to_crawl.pop(0)
                
                if url in self.visited_urls:
                    continue
                    
                self.visited_urls.add(url)
                
                try:
                    self.status_updated.emit(f"Scraping {url}")
                    
                    # Get page content
                    if dynamic:
                        # Verify that driver is available
                        if not hasattr(self, 'driver') or self.driver is None:
                            error_message = "WebDriver is not initialized. Aborting crawl."
                            self.logger.error(error_message)
                            self.error_occurred.emit(error_message)
                            return
                            
                        try:
                            self.driver.get(url)
                            time.sleep(delay)  # Wait for JS to load
                            page_content = self.driver.page_source
                        except Exception as e:
                            error_message = f"Error loading page with Selenium: {str(e)}"
                            self.logger.error(error_message)
                            self.error_occurred.emit(error_message)
                            continue  # Skip this URL and continue with the next
                    else:
                        try:
                            response = self.session.get(url, timeout=10)
                            if response.status_code != 200:
                                self.logger.warning(f"Received status code {response.status_code} for {url}")
                                continue
                            page_content = response.text
                        except Exception as e:
                            error_message = f"Error fetching page {url}: {str(e)}"
                            self.logger.error(error_message)
                            self.error_occurred.emit(error_message)
                            continue  # Skip this URL and continue with the next
                    
                    # Parse the page
                    try:
                        soup = BeautifulSoup(page_content, 'lxml')
                    except Exception:
                        # Fallback to html.parser if lxml is not available
                        soup = BeautifulSoup(page_content, 'html.parser')
                    
                    # Extract page data
                    title = soup.title.string if soup.title else "No Title"
                    text_content = soup.get_text(separator=' ', strip=True)
                    
                    # Store the scraped data
                    page_data = {
                        'url': url,
                        'title': title,
                        'content': text_content,
                        'html': page_content,
                        'timestamp': datetime.now().isoformat(),
                        'depth': current_depth
                    }
                    
                    self.scraped_data.append(page_data)
                    self.page_scraped.emit(page_data)
                    pages_scraped += 1
                    
                    # Update progress
                    self.progress_updated.emit(pages_scraped, max_pages)
                    
                    # If we haven't reached max depth, collect more URLs
                    if current_depth < depth:
                        new_urls = self._extract_links(soup, url)
                        # Add new URLs with incremented depth
                        for new_url in new_urls:
                            if new_url not in self.visited_urls:
                                urls_to_crawl.append((new_url, current_depth + 1))
                    
                    # Respect robots.txt with a delay
                    time.sleep(delay + random.uniform(0.1, 0.5))
                    
                except Exception as e:
                    self.logger.error(f"Error crawling {url}: {str(e)}")
                    self.error_occurred.emit(f"Error crawling {url}: {str(e)}")
            
            self.status_updated.emit(f"Crawl completed. Scraped {pages_scraped} pages.")
            self.crawl_completed.emit(self.scraped_data)
            
            # Clean up Selenium if used
            if dynamic and self.driver:
                self.driver.quit()
                self.driver = None
            
        except Exception as e:
            error_message = f"Error in crawl method: {str(e)}"
            self.logger.error(error_message)
            self.error_occurred.emit(error_message)
    
    def stop(self):
        """Stop the ongoing crawl"""
        self.should_stop = True
        self.status_updated.emit("Crawl stopping...")
        
        # Safely quit the WebDriver if it exists
        try:
            if hasattr(self, 'driver') and self.driver is not None:
                self.logger.info("Closing WebDriver...")
                try:
                    self.driver.quit()
                except Exception as e:
                    self.logger.error(f"Error while closing WebDriver: {str(e)}")
                finally:
                    self.driver = None
        except Exception as e:
            self.logger.error(f"Error in stop method: {str(e)}")
    
    def _extract_links(self, soup, current_url):
        """Extract links from the page that belong to the same domain"""
        parsed_url = urllib.parse.urlparse(current_url)
        base_url = parsed_url.netloc
        
        links = []
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']
            
            # Handle relative URLs
            if href.startswith('/'):
                href = f"{parsed_url.scheme}://{base_url}{href}"
            elif not href.startswith(('http://', 'https://')):
                # Skip anchors and javascript links
                if href.startswith('#') or href.startswith('javascript:'):
                    continue
                # Resolve relative link
                href = urllib.parse.urljoin(current_url, href)
            
            # Only follow links in the same domain
            parsed_href = urllib.parse.urlparse(href)
            if parsed_href.netloc == base_url:
                links.append(href)
                
        return links
    
    def save_scraped_data(self, filepath):
        """Save the scraped data to a JSON file"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.scraped_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"Error saving data: {str(e)}")
            self.error_occurred.emit(f"Error saving data: {str(e)}")
            return False
    
    def scrape_page(self, url, timeout=10):
        """
        Scrape a single page and return its content
        
        Args:
            url (str): URL to scrape
            timeout (int): Request timeout in seconds
            
        Returns:
            dict: Page data with title, content, url, and timestamp
        """
        try:
            self.logger.info(f"Scraping single page: {url}")
            
            # Use requests session for single page scraping
            response = self.session.get(url, timeout=timeout)
            if response.status_code != 200:
                self.logger.warning(f"Received status code {response.status_code} for {url}")
                return None
                
            page_content = response.text
            
            # Parse the page
            try:
                soup = BeautifulSoup(page_content, 'lxml')
            except Exception:
                # Fallback to html.parser if lxml is not available
                soup = BeautifulSoup(page_content, 'html.parser')
            
            # Extract page data
            title = soup.title.string if soup.title else "No Title"
            text_content = soup.get_text(separator=' ', strip=True)
            
            # Return page data
            page_data = {
                'url': url,
                'title': title.strip() if title else "No Title",
                'content': text_content,
                'html': page_content,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"Successfully scraped page: {url} ({len(text_content)} characters)")
            return page_data
            
        except Exception as e:
            self.logger.error(f"Error scraping page {url}: {str(e)}")
            return None 