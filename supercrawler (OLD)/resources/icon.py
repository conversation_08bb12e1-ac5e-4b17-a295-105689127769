"""
This module provides a simple text-based logo for IntelliCrawler
as a placeholder until a proper graphical icon is created.
"""

def print_logo():
    """Print the ASCII art logo to the console"""
    logo = """
  _____ _   _ _______ ______ _      _      _____ _____  _______          ___      ______ _____
 |_   _| \ | |__   __|  ____| |    | |    |_   _/ ____|  __ \ \        / / |    |  ____|  __ \
   | | |  \| |  | |  | |__  | |    | |      | || |    | |__) \ \  /\  / /| |    | |__  | |__) |
   | | | . ` |  | |  |  __| | |    | |      | || |    |  _  / \ \/  \/ / | |    |  __| |  _  /
  _| |_| |\  |  | |  | |____| |____| |____ _| || |____| | \ \  \  /\  /  | |____| |____| | \ \
 |_____|_| \_|  |_|  |______|______|______|_____\_____|_|  \_\  \/  \/   |______|______|_|  \_\\

 Advanced AI-Powered Web Crawling & Analysis Platform - DeepSeek R1 & V2.5 integrated
    """
    print(logo)

if __name__ == "__main__":
    print_logo() 