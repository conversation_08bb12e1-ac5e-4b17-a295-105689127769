#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import time
import logging
import traceback
import platform
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal, QThread

# Import IntelliCrawler components
from intellicrawler.utils.logger import setup_logger, get_logger
from intellicrawler.utils.config import load_config, save_config
from intellicrawler.crawler import Crawler
from intellicrawler.ai_integration import DeepSeekAI

# Constants
TEST_URL = "https://docs.firecrawl.dev/introduction"
CONFIG_DIR = os.path.expanduser("~/.intellicrawler")
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")
LOG_DIR = os.path.join(CONFIG_DIR, "logs")
TEST_OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "test_output")
USER_AGENT = "IntelliCrawler-SelfTest/1.0"

# Define platform-compatible symbols
if platform.system() == "Windows":
    PASS_SYMBOL = "[PASS]"
    FAIL_SYMBOL = "[FAIL]"
else:
    PASS_SYMBOL = "✓"
    FAIL_SYMBOL = "✗"

class DiagnosticTool(QObject):
    """
    Comprehensive diagnostic tool for IntelliCrawler

    This class provides self-testing functionality to debug various components
    of the IntelliCrawler application, including:
    - System environment checks
    - Configuration and file access
    - Network connectivity
    - Crawler functionality (both dynamic and non-dynamic modes)
    - WebDriver initialization
    - DeepSeek AI API integration

    Usage:
        diagnostic = DiagnosticTool()
        results = diagnostic.run_all_tests()
        print(diagnostic.format_results(results))
    """
    
    # Signals
    test_started = pyqtSignal(str)
    test_completed = pyqtSignal(str, bool, str)  # test_name, success, message
    all_tests_completed = pyqtSignal(dict)  # results dictionary
    
    def __init__(self, verbose=True):
        super().__init__()
        self.verbose = verbose
        self.logger = get_logger()
        self.results = {}
        self.test_count = 0
        self.passed_count = 0
        
        # Create test output directory if it doesn't exist
        os.makedirs(TEST_OUTPUT_DIR, exist_ok=True)
        
        # Initialize components for testing
        self.crawler = None
        self.ai_instance = None
    
    def log(self, message: str) -> None:
        """Log a message if verbose mode is enabled"""
        if self.verbose:
            self.logger.info(message)
            print(message)
    
    def run_all_tests(self) -> Dict[str, Dict[str, Any]]:
        """Run all diagnostic tests and return results"""
        self.log("Starting SuperCrawler diagnostic tests...")
        self.results = {}
        self.test_count = 0
        self.passed_count = 0
        
        try:
            # System tests
            self.run_system_tests()
            
            # Configuration tests
            self.run_config_tests()
            
            # Network tests
            self.run_network_tests()
            
            # Crawler tests
            self.run_crawler_tests()
            
            # AI integration tests
            self.run_ai_tests()
            
            self.log(f"\nTest Summary: {self.passed_count}/{self.test_count} tests passed")
            
            # Emit signal with results
            self.all_tests_completed.emit(self.results)
            
            return self.results
            
        except Exception as e:
            error_msg = f"Error during diagnostic tests: {str(e)}"
            self.logger.error(error_msg)
            traceback.print_exc()
            return {"error": {"success": False, "message": error_msg}}
    
    def run_test(self, test_name: str, test_func, *args, **kwargs) -> Tuple[bool, str]:
        """Run a single test and record results"""
        self.test_count += 1
        self.test_started.emit(test_name)
        
        self.log(f"\nRunning test: {test_name}")
        start_time = time.time()
        
        try:
            result, message = test_func(*args, **kwargs)
            elapsed = time.time() - start_time
            
            if result:
                self.passed_count += 1
                self.log(f"{PASS_SYMBOL} PASS: {test_name} ({elapsed:.2f}s)")
                self.log(f"  {message}")
            else:
                self.log(f"{FAIL_SYMBOL} FAIL: {test_name} ({elapsed:.2f}s)")
                self.log(f"  {message}")
            
            self.results[test_name] = {
                "success": result,
                "message": message,
                "elapsed": elapsed
            }
            
            self.test_completed.emit(test_name, result, message)
            return result, message
            
        except Exception as e:
            elapsed = time.time() - start_time
            error_msg = f"Test failed with exception: {str(e)}"
            self.logger.error(error_msg)
            traceback.print_exc()
            
            self.results[test_name] = {
                "success": False,
                "message": error_msg,
                "elapsed": elapsed,
                "exception": str(e)
            }
            
            self.test_completed.emit(test_name, False, error_msg)
            return False, error_msg
    
    def run_system_tests(self) -> None:
        """Run system environment tests"""
        self.run_test("System Info", self._test_system_info)
        self.run_test("Python Version", self._test_python_version)
        self.run_test("Required Packages", self._test_required_packages)
        self.run_test("File Permissions", self._test_file_permissions)
    
    def run_config_tests(self) -> None:
        """Run configuration tests"""
        self.run_test("Config Directory", self._test_config_directory)
        self.run_test("Config File Access", self._test_config_file_access)
        self.run_test("API Key Configuration", self._test_api_key_config)
    
    def run_network_tests(self) -> None:
        """Run network connectivity tests"""
        self.run_test("Internet Connectivity", self._test_internet_connectivity)
        self.run_test("DeepSeek API Connectivity", self._test_deepseek_connectivity)
        self.run_test("Test URL Accessibility", self._test_url_accessibility)
    
    def run_crawler_tests(self) -> None:
        """Run crawler component tests"""
        # Initialize crawler if needed
        if self.crawler is None:
            self.crawler = Crawler()
        
        self.run_test("Crawler Initialization", self._test_crawler_init)
        self.run_test("Static Crawling Mode", self._test_static_crawl)
        self.run_test("WebDriver Availability", self._test_webdriver_availability)
        
        # Only run dynamic test if webdriver is available
        if self.results.get("WebDriver Availability", {}).get("success", False):
            self.run_test("Dynamic Crawling Mode", self._test_dynamic_crawl)
    
    def run_ai_tests(self) -> None:
        """Run AI integration tests"""
        self.run_test("AI Module Initialization", self._test_ai_init)
        
        # Only run API tests if initialization was successful and API key is set
        if (self.results.get("AI Module Initialization", {}).get("success", False) and
            self.results.get("API Key Configuration", {}).get("success", False)):
            self.run_test("AI Model Availability", self._test_ai_models)
            self.run_test("AI API Authentication", self._test_ai_authentication)
    
    # System test implementations
    def _test_system_info(self) -> Tuple[bool, str]:
        """Test system information retrieval"""
        try:
            system_info = {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "architecture": platform.architecture(),
                "processor": platform.processor(),
                "machine": platform.machine(),
                "python_implementation": platform.python_implementation(),
            }
            
            # Write system info to file
            system_info_path = os.path.join(TEST_OUTPUT_DIR, "system_info.json")
            with open(system_info_path, 'w', encoding='utf-8') as f:
                json.dump(system_info, f, indent=2)
            
            return True, f"System info collected and saved to {system_info_path}"
        except Exception as e:
            return False, f"Failed to retrieve system info: {str(e)}"
    
    def _test_python_version(self) -> Tuple[bool, str]:
        """Test Python version compatibility"""
        py_version = sys.version_info
        
        # Check if Python version is 3.7 or higher
        if py_version.major >= 3 and py_version.minor >= 7:
            return True, f"Python version {py_version.major}.{py_version.minor}.{py_version.micro} is compatible"
        else:
            return False, f"Python version {py_version.major}.{py_version.minor}.{py_version.micro} is not compatible (3.7+ required)"
    
    def _test_required_packages(self) -> Tuple[bool, str]:
        """Test if required packages are installed"""
        required_packages = [
            "PyQt5", "selenium", "beautifulsoup4", "requests", 
            "openai", "webdriver_manager"
        ]
        
        missing_packages = []
        installed_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                installed_packages.append(package)
            except ImportError:
                missing_packages.append(package)
        
        if not missing_packages:
            return True, f"All required packages are installed: {', '.join(installed_packages)}"
        else:
            return False, f"Missing required packages: {', '.join(missing_packages)}"
    
    def _test_file_permissions(self) -> Tuple[bool, str]:
        """Test file system permissions for SuperCrawler directories"""
        dirs_to_check = [
            CONFIG_DIR,
            LOG_DIR,
            TEST_OUTPUT_DIR
        ]
        
        permission_issues = []
        
        for directory in dirs_to_check:
            os.makedirs(directory, exist_ok=True)
            test_file = os.path.join(directory, ".permission_test")
            
            try:
                # Test write access
                with open(test_file, 'w') as f:
                    f.write("test")
                
                # Test read access
                with open(test_file, 'r') as f:
                    f.read()  # Just test read access, don't store content
                
                # Test delete access
                os.remove(test_file)
            except Exception as e:
                permission_issues.append(f"{directory}: {str(e)}")
        
        if not permission_issues:
            return True, f"File permissions are correctly set for all required directories"
        else:
            return False, f"Permission issues found in directories: {', '.join(permission_issues)}"
    
    # Configuration test implementations
    def _test_config_directory(self) -> Tuple[bool, str]:
        """Test if config directory exists and is accessible"""
        try:
            os.makedirs(CONFIG_DIR, exist_ok=True)
            
            if os.path.exists(CONFIG_DIR) and os.path.isdir(CONFIG_DIR):
                return True, f"Config directory exists at: {CONFIG_DIR}"
            else:
                return False, f"Config directory could not be created at: {CONFIG_DIR}"
        except Exception as e:
            return False, f"Error accessing config directory: {str(e)}"
    
    def _test_config_file_access(self) -> Tuple[bool, str]:
        """Test if config file can be read and written"""
        try:
            # Ensure config directory exists
            os.makedirs(CONFIG_DIR, exist_ok=True)
            
            # Create a test config
            test_config = {
                "test_key": "test_value",
                "timestamp": time.time()
            }
            
            # Write config
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(test_config, f, ensure_ascii=False, indent=2)
            
            # Read config
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                read_config = json.load(f)
            
            # Restore original config if it exists
            config = load_config()
            save_config(config)
            
            if read_config.get("test_key") == "test_value":
                return True, f"Config file can be successfully read and written: {CONFIG_FILE}"
            else:
                return False, f"Config file read/write test failed: values don't match"
        except Exception as e:
            return False, f"Config file access test failed: {str(e)}"
    
    def _test_api_key_config(self) -> Tuple[bool, str]:
        """Test if API key is properly configured"""
        try:
            config = load_config()
            api_key = config.get("deepseek_api_key", "")
            
            if api_key:
                masked_key = "********" + api_key[-4:] if len(api_key) > 4 else "********"
                return True, f"API key is configured ({masked_key}, length: {len(api_key)})"
            else:
                return False, "No API key found in configuration"
        except Exception as e:
            return False, f"API key configuration test failed: {str(e)}"
    
    # Network test implementations
    def _test_internet_connectivity(self) -> Tuple[bool, str]:
        """Test if the system has internet connectivity"""
        test_urls = [
            "https://www.google.com",
            "https://www.microsoft.com",
            "https://www.github.com"
        ]
        
        successful = 0
        failures = []
        
        for url in test_urls:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    successful += 1
                else:
                    failures.append(f"{url} (Status: {response.status_code})")
            except Exception as e:
                failures.append(f"{url} (Error: {str(e)})")
        
        if successful == len(test_urls):
            return True, f"Successfully connected to {successful}/{len(test_urls)} test sites"
        elif successful > 0:
            return True, f"Partial connectivity: {successful}/{len(test_urls)} sites accessible. Failures: {', '.join(failures)}"
        else:
            return False, f"No internet connectivity. Failed to connect to: {', '.join(failures)}"
    
    def _test_deepseek_connectivity(self) -> Tuple[bool, str]:
        """Test if DeepSeek API is accessible"""
        try:
            # Just test a connection to the API endpoint without authentication
            response = requests.get("https://api.deepseek.com", timeout=5)
            
            if response.status_code < 500:  # Any non-server error is acceptable here
                return True, f"DeepSeek API endpoint is accessible (Status: {response.status_code})"
            else:
                return False, f"DeepSeek API endpoint returned server error: {response.status_code}"
        except Exception as e:
            return False, f"Failed to connect to DeepSeek API: {str(e)}"
    
    def _test_url_accessibility(self) -> Tuple[bool, str]:
        """Test if the test URL is accessible"""
        try:
            headers = {'User-Agent': USER_AGENT}
            response = requests.get(TEST_URL, headers=headers, timeout=5)
            
            if response.status_code == 200:
                # Save the HTML for inspection
                html_path = os.path.join(TEST_OUTPUT_DIR, "test_url.html")
                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                return True, f"Test URL is accessible: {TEST_URL}"
            else:
                return False, f"Test URL returned non-200 status: {response.status_code}"
        except Exception as e:
            return False, f"Failed to access test URL: {str(e)}"
    
    # Crawler test implementations
    def _test_crawler_init(self) -> Tuple[bool, str]:
        """Test crawler initialization"""
        try:
            if self.crawler is None:
                self.crawler = Crawler()
            
            # Check if basic properties are initialized
            checks = [
                hasattr(self.crawler, 'session'),
                hasattr(self.crawler, 'visited_urls'),
                hasattr(self.crawler, 'scraped_data'),
                hasattr(self.crawler, 'progress_updated'),
                hasattr(self.crawler, 'crawl_completed')
            ]
            
            if all(checks):
                return True, "Crawler successfully initialized with all required properties"
            else:
                missing = [i for i, check in enumerate(checks) if not check]
                return False, f"Crawler initialization missing properties at indices: {missing}"
        except Exception as e:
            return False, f"Crawler initialization failed: {str(e)}"
    
    def _test_static_crawl(self) -> Tuple[bool, str]:
        """Test static (non-dynamic) crawling mode with a single page"""
        try:
            if self.crawler is None:
                self.crawler = Crawler()
            
            # Clear previous data
            self.crawler.visited_urls = set()
            self.crawler.scraped_data = []
            
            # Define a minimal callback to capture results
            results = []
            
            def on_crawl_complete(data):
                nonlocal results
                results = data
            
            # Connect signal
            self.crawler.crawl_completed.connect(on_crawl_complete)
            
            # Crawl a single page in static mode
            self.crawler.crawl(
                start_url=TEST_URL,
                max_pages=1,  # Just one page
                depth=0,      # No link following
                dynamic=False,
                delay=1.0
            )
            
            # Disconnect signal
            self.crawler.crawl_completed.disconnect(on_crawl_complete)
            
            # Verify results
            if results and len(results) > 0:
                # Save results for inspection
                results_path = os.path.join(TEST_OUTPUT_DIR, "static_crawl_results.json")
                with open(results_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2)
                
                return True, f"Static crawl successfully completed. Scraped {len(results)} pages."
            else:
                return False, "Static crawl did not return any results"
        except Exception as e:
            return False, f"Static crawl test failed: {str(e)}"
    
    def _test_webdriver_availability(self) -> Tuple[bool, str]:
        """Test if WebDriver is available for dynamic crawling"""
        try:
            if self.crawler is None:
                self.crawler = Crawler()
            
            # Try to initialize Selenium
            self.crawler.initialize_selenium()
            
            if self.crawler.driver is not None:
                browser_name = self.crawler.driver.capabilities.get('browserName', 'unknown')
                browser_version = self.crawler.driver.capabilities.get('browserVersion', 'unknown')
                
                # Don't forget to quit the driver
                self.crawler.driver.quit()
                self.crawler.driver = None
                
                return True, f"WebDriver successfully initialized: {browser_name} {browser_version}"
            else:
                return False, "WebDriver initialization returned None"
        except Exception as e:
            return False, f"WebDriver initialization failed: {str(e)}"
    
    def _test_dynamic_crawl(self) -> Tuple[bool, str]:
        """Test dynamic (Selenium-based) crawling mode with a single page"""
        try:
            if self.crawler is None:
                self.crawler = Crawler()
            
            # Clear previous data
            self.crawler.visited_urls = set()
            self.crawler.scraped_data = []
            
            # Define a minimal callback to capture results
            results = []
            
            def on_crawl_complete(data):
                nonlocal results
                results = data
            
            # Connect signal
            self.crawler.crawl_completed.connect(on_crawl_complete)
            
            # Crawl a single page in dynamic mode
            self.crawler.crawl(
                start_url=TEST_URL,
                max_pages=1,  # Just one page
                depth=0,      # No link following
                dynamic=True,
                delay=1.0
            )
            
            # Disconnect signal
            self.crawler.crawl_completed.disconnect(on_crawl_complete)
            
            # Verify results
            if results and len(results) > 0:
                # Save results for inspection
                results_path = os.path.join(TEST_OUTPUT_DIR, "dynamic_crawl_results.json")
                with open(results_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2)
                
                return True, f"Dynamic crawl successfully completed. Scraped {len(results)} pages."
            else:
                return False, "Dynamic crawl did not return any results"
        except Exception as e:
            return False, f"Dynamic crawl test failed: {str(e)}"
    
    # AI integration test implementations
    def _test_ai_init(self) -> Tuple[bool, str]:
        """Test AI module initialization"""
        try:
            self.ai_instance = DeepSeekAI()
            
            # Check if basic properties are initialized
            checks = [
                hasattr(self.ai_instance, 'client'),
                hasattr(self.ai_instance, 'api_key'),
                hasattr(self.ai_instance, 'status_updated'),
                hasattr(self.ai_instance, 'analysis_completed'),
                hasattr(self.ai_instance, 'error_occurred')
            ]
            
            if all(checks):
                return True, "AI module successfully initialized with all required properties"
            else:
                missing = [i for i, check in enumerate(checks) if not check]
                return False, f"AI module initialization missing properties at indices: {missing}"
        except Exception as e:
            return False, f"AI module initialization failed: {str(e)}"
    
    def _test_ai_models(self) -> Tuple[bool, str]:
        """Test if AI models are available"""
        try:
            if self.ai_instance is None:
                self.ai_instance = DeepSeekAI()
            
            models = self.ai_instance.get_available_models()
            
            if models and len(models) > 0:
                return True, f"AI models are available: {', '.join(list(models.keys()))}"
            else:
                return False, "No AI models available"
        except Exception as e:
            return False, f"AI models test failed: {str(e)}"
    
    def _test_ai_authentication(self) -> Tuple[bool, str]:
        """Test AI API authentication"""
        try:
            if self.ai_instance is None:
                self.ai_instance = DeepSeekAI()
            
            auth_result = self.ai_instance.check_api_key()
            
            if auth_result:
                return True, "API key authentication successful"
            else:
                return False, "API key authentication failed"
        except Exception as e:
            return False, f"API authentication test failed: {str(e)}"
    
    def format_results(self, results: Dict[str, Dict[str, Any]]) -> str:
        """Format test results as a readable string"""
        if not results:
            return "No test results available."
        
        output = []
        output.append("=" * 80)
        output.append("SUPERCRAWLER DIAGNOSTIC RESULTS")
        output.append("=" * 80)
        output.append("")
        
        # Group tests by category
        categories = {
            "System": ["System Info", "Python Version", "Required Packages", "File Permissions"],
            "Configuration": ["Config Directory", "Config File Access", "API Key Configuration"],
            "Network": ["Internet Connectivity", "DeepSeek API Connectivity", "Test URL Accessibility"],
            "Crawler": ["Crawler Initialization", "Static Crawling Mode", "WebDriver Availability", "Dynamic Crawling Mode"],
            "AI Integration": ["AI Module Initialization", "AI Model Availability", "AI API Authentication"]
        }
        
        total_tests = 0
        total_passed = 0
        
        for category, tests in categories.items():
            output.append(f"## {category} Tests\n")
            
            for test in tests:
                if test in results:
                    result = results[test]
                    status = f"{PASS_SYMBOL} PASS" if result["success"] else f"{FAIL_SYMBOL} FAIL"
                    elapsed = result.get("elapsed", 0)
                    message = result.get("message", "No message")
                    
                    output.append(f"{status}: {test} ({elapsed:.2f}s)")
                    output.append(f"  {message}")
                    output.append("")
                    
                    total_tests += 1
                    if result["success"]:
                        total_passed += 1
            
            output.append("-" * 80)
        
        # Summary
        output.append(f"\nSUMMARY: {total_passed}/{total_tests} tests passed")
        output.append(f"Pass rate: {(total_passed/total_tests)*100:.1f}%")
        output.append("")
        
        # Recommendations
        output.append("RECOMMENDATIONS:")
        
        # Add specific recommendations based on failed tests
        recommendations = []
        
        if not results.get("API Key Configuration", {}).get("success", False):
            recommendations.append("- Configure a valid DeepSeek API key in the application settings")
        
        if not results.get("WebDriver Availability", {}).get("success", False):
            recommendations.append("- Install Chrome or Edge browser for dynamic crawling")
            recommendations.append("- Ensure WebDriver is properly configured")
        
        if not results.get("AI API Authentication", {}).get("success", False) and results.get("API Key Configuration", {}).get("success", False):
            recommendations.append("- Check if your DeepSeek API key is valid")
            recommendations.append("- Verify your internet connection and firewall settings")
        
        if not results.get("Required Packages", {}).get("success", False):
            recommendations.append("- Install missing required packages using pip")
        
        if recommendations:
            output.extend(recommendations)
        else:
            output.append("- No specific recommendations - all critical tests passed!")
        
        output.append("\nTest output files are available in the test_output directory")
        output.append("=" * 80)
        
        return "\n".join(output)


def run_diagnostic_cli():
    """Run diagnostic tests from command line"""
    print("SuperCrawler Diagnostic Tool")
    print("===========================")
    
    # Setup logger
    setup_logger()
    
    diagnostic = DiagnosticTool(verbose=True)
    results = diagnostic.run_all_tests()
    
    # Print formatted results
    print("\n" + diagnostic.format_results(results))
    
    # Return success code based on pass rate
    total_tests = len(results)
    passed_tests = sum(1 for r in results.values() if r.get("success", False))
    
    return 0 if passed_tests == total_tests else 1


def run_diagnostic_gui():
    """Run diagnostic tests with GUI progress dialog"""
    from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QProgressBar, QTextEdit, QPushButton
    
    class DiagnosticThread(QThread):
        def __init__(self, diagnostic_tool):
            super().__init__()
            self.diagnostic_tool = diagnostic_tool
            
        def run(self):
            """Run all tests in this thread"""
            self.diagnostic_tool.run_all_tests()
    
    class DiagnosticDialog(QDialog):
        def __init__(self, parent=None):
            super().__init__(parent)
            self.setWindowTitle("SuperCrawler Diagnostic Tool")
            self.resize(600, 400)
            
            # Create layout
            layout = QVBoxLayout(self)
            
            # Status label
            self.status_label = QLabel("Running diagnostic tests...")
            layout.addWidget(self.status_label)
            
            # Progress bar
            self.progress_bar = QProgressBar()
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
            layout.addWidget(self.progress_bar)
            
            # Text output
            self.output_text = QTextEdit()
            self.output_text.setReadOnly(True)
            layout.addWidget(self.output_text)
            
            # Close button
            self.close_button = QPushButton("Close")
            self.close_button.clicked.connect(self.accept)
            layout.addWidget(self.close_button)
            
            # Create diagnostic tool
            self.diagnostic = DiagnosticTool(verbose=False)
            
            # Connect signals
            self.diagnostic.test_started.connect(self.on_test_started)
            self.diagnostic.test_completed.connect(self.on_test_completed)
            self.diagnostic.all_tests_completed.connect(self.on_all_tests_completed)
            
            # Create and start worker thread
            self.worker_thread = DiagnosticThread(self.diagnostic)
            self.worker_thread.start()
        
        def on_test_started(self, test_name):
            """Handle test started signal"""
            self.status_label.setText(f"Running test: {test_name}")
            self.output_text.append(f"Starting test: {test_name}")
        
        def on_test_completed(self, test_name, success, message):
            """Handle test completed signal"""
            status = "PASS" if success else "FAIL"
            self.output_text.append(f"[{status}] {test_name}")
            self.output_text.append(f"  {message}")
            self.output_text.append("")
        
        def on_all_tests_completed(self, results):
            """Handle all tests completed signal"""
            self.status_label.setText("All diagnostic tests completed")
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(100)
            
            # Show formatted results
            self.output_text.clear()
            self.output_text.setPlainText(self.diagnostic.format_results(results))
    
    # Create application if needed
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # Setup logger
    setup_logger()
    
    # Create and show dialog
    dialog = DiagnosticDialog()
    return dialog.exec_()


# Run tests if script is executed directly
if __name__ == "__main__":
    # Check if we should run in GUI mode
    if len(sys.argv) > 1 and sys.argv[1] == "--gui":
        sys.exit(run_diagnostic_gui())
    else:
        sys.exit(run_diagnostic_cli()) 